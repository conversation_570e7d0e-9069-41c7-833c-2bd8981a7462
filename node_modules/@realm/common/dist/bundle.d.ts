/**
 * Display a deprecation warning for features being removed in the next major version
 * to users in development mode (as best as we can detect this, see `isDevelopmentMode`)
 *
 * @param deprecatedItem The method signature or name of the deprecated item
 * @param suggestedReplacement The method signature or name of the suggested replacement
 */
declare const deprecationWarning: (deprecatedItem: string, suggestedReplacement: string) => void;
/**
 * Helper function for migrating from positional arguments to a single dictionary argument.
 * Check the arguments passed to a function, if the first argument is not an object (i.e. it
 * is presumed to be a deprecated positional-style call), shows a deprecation warning and
 * converts the positional arguments into an object matching the expected "new" shape.
 *
 * @param args Array of arguments passed to the function (captured with `...args`).
 * @param methodName The name of the method, used to show the deprecation warning.
 * @param argNames The list of positional argument names, used to covert them into
 * an object if a deprecated call is made and to show the deprecation warning.
 * @param hasRestArgs Optional flag indicating that the function's final argument is
 * `...args` (to capture any extra arguments), in which case we capture them and return
 * as the second element of the return array.
 *
 * @returns An object containing:
 *
 * argsObject: a dictionary of function arguments, either passed through from args[0] if
 * args[0] is an object, or created from `args` and `argNames` if the args are a
 * deprecated positional argument call.
 *
 * restArgs: an array of the "...args" passed to the function if `hasRestArgs` is true;
 * otherwise it is `undefined`.
 */
declare const handleDeprecatedPositionalArgs: <T extends object>(args: unknown[] | [T, ...unknown[]], methodName: string, argNames: (keyof T)[], hasRestArgs?: boolean | undefined) => {
    argsObject: T;
    restArgs: unknown[] | undefined;
};

declare const PROXY_TARGET: unique symbol;
declare const IS_PROXIED_DICTIONARY: unique symbol;

declare const symbols_d_PROXY_TARGET: typeof PROXY_TARGET;
declare const symbols_d_IS_PROXIED_DICTIONARY: typeof IS_PROXIED_DICTIONARY;
declare namespace symbols_d {
  export {
    symbols_d_PROXY_TARGET as PROXY_TARGET,
    symbols_d_IS_PROXIED_DICTIONARY as IS_PROXIED_DICTIONARY,
  };
}

declare const safeGlobalThis: typeof globalThis;

export { deprecationWarning, handleDeprecatedPositionalArgs, safeGlobalThis, symbols_d as symbols };
