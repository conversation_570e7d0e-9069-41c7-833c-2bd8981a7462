'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

////////////////////////////////////////////////////////////////////////////
//
// Copyright 2021 Realm Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
////////////////////////////////////////////////////////////////////////////
/**
 * Check whether the user's app is running in "development mode" (e.g. `npm run dev`
 * for a React app, or `NODE_ENV=development` for a Node app). Each platform's entry
 * point should define the value of this using `setIsDevelopmentMode`.
 * The default behaviour is to always return `false`.
 *
 * @returns true if the user's app is running in development mode, false otherwise
 */
let isDevelopmentMode = false;
/**
 * Set the value of `isDevelopmentMode`. This allows each entry point (node vs DOM)
 * to use its own method for determining whether we are in development mode.
 *
 * @param state A boolean indicating whether the user's app is running in
 * development mode or not.
 */
const setIsDevelopmentMode = (state) => {
    isDevelopmentMode = state;
};

////////////////////////////////////////////////////////////////////////////
/**
 * Display a deprecation warning for features being removed in the next major version
 * to users in development mode (as best as we can detect this, see `isDevelopmentMode`)
 *
 * @param deprecatedItem The method signature or name of the deprecated item
 * @param suggestedReplacement The method signature or name of the suggested replacement
 */
const deprecationWarning = (deprecatedItem, suggestedReplacement) => {
    if (!isDevelopmentMode)
        return;
    console.warn(`Deprecation warning from Realm: ${deprecatedItem} is deprecated and will be removed in a future major release. Consider switching to ${suggestedReplacement}.`);
};
/**
 * Helper function for migrating from positional arguments to a single dictionary argument.
 * Check the arguments passed to a function, if the first argument is not an object (i.e. it
 * is presumed to be a deprecated positional-style call), shows a deprecation warning and
 * converts the positional arguments into an object matching the expected "new" shape.
 *
 * @param args Array of arguments passed to the function (captured with `...args`).
 * @param methodName The name of the method, used to show the deprecation warning.
 * @param argNames The list of positional argument names, used to covert them into
 * an object if a deprecated call is made and to show the deprecation warning.
 * @param hasRestArgs Optional flag indicating that the function's final argument is
 * `...args` (to capture any extra arguments), in which case we capture them and return
 * as the second element of the return array.
 *
 * @returns An object containing:
 *
 * argsObject: a dictionary of function arguments, either passed through from args[0] if
 * args[0] is an object, or created from `args` and `argNames` if the args are a
 * deprecated positional argument call.
 *
 * restArgs: an array of the "...args" passed to the function if `hasRestArgs` is true;
 * otherwise it is `undefined`.
 */
// Allow use of `object` type
// eslint-disable-next-line @typescript-eslint/ban-types
const handleDeprecatedPositionalArgs = (args, methodName, argNames, hasRestArgs) => {
    if (typeof args[0] !== "object") {
        const restArgsText = hasRestArgs ? ", ...args" : "";
        deprecationWarning(`${methodName}(${argNames.join(", ")}${restArgsText})`, `${methodName}({ ${argNames.join(", ")} }${restArgsText})`);
        // Convert the array of arguments into a dictionary keyed by the relevant argName
        const argsObject = argNames.reduce((prev, argName, index) => {
            return { ...prev, [argName]: args[index] };
        }, {});
        const restArgs = hasRestArgs ? args.slice(argNames.length) : undefined;
        return { argsObject, restArgs };
    }
    return { argsObject: args[0], restArgs: hasRestArgs ? args.slice(1) : undefined };
};

////////////////////////////////////////////////////////////////////////////
//
// Copyright 2022 Realm Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
////////////////////////////////////////////////////////////////////////////
// Used as a key by Realm React in `useQuery`, to store the original object
// which is being proxied, for compatibility with JSC pre-v11 (#4541)
const PROXY_TARGET = Symbol("PROXY_TARGET");
// Used to indicate that an object is a proxied Realm.Dictionary, to allow us
// to correctly detect Dictionaries in toJSON when using JSC pre-v11 (#4674)
const IS_PROXIED_DICTIONARY = Symbol("IS_PROXIED_DICTIONARY");

var symbols = /*#__PURE__*/Object.freeze({
    __proto__: null,
    PROXY_TARGET: PROXY_TARGET,
    IS_PROXIED_DICTIONARY: IS_PROXIED_DICTIONARY
});

////////////////////////////////////////////////////////////////////////////
//
// Copyright 2022 Realm Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
////////////////////////////////////////////////////////////////////////////
// Exports a globalThis which is polyfilled for iOS 11/12
// From https://github.com/zloirock/core-js/blob/master/packages/core-js/internals/global.js
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const check = function (it) {
    return it && it.Math == Math && it;
};
// eslint-disable-next-line no-restricted-globals
const safeGlobalThis = 
// eslint-disable-next-line no-restricted-globals
check(typeof globalThis == "object" && globalThis) ||
    check(typeof window == "object" && window) ||
    // eslint-disable-next-line no-restricted-globals -- safe
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore allow `self`
    check(typeof self == "object" && self) ||
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore allow `global`
    check(typeof global == "object" && global) ||
    // eslint-disable-next-line no-new-func -- fallback
    (function () {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore allow `this`
        return this;
    })() ||
    Function("return this")();

////////////////////////////////////////////////////////////////////////////
// Exported for unit testing
const isDevelopmentModeImpl = () => {
    var _a;
    try {
        // eslint-disable-next-line @typescript-eslint/no-var-requires
        const { app } = require("electron");
        // We are in an electron app, check if the app is packaged (release mode)
        return app !== undefined && !app.isPackaged;
    }
    catch (_) {
        // ignore error
    }
    // Node.js has no default for NODE_ENV, so check if it is anything other than
    // "production" to catch cases where it is just started with `node index.js`
    return ((_a = process.env) === null || _a === void 0 ? void 0 : _a.NODE_ENV) !== "production";
};
setIsDevelopmentMode(isDevelopmentModeImpl());

exports.deprecationWarning = deprecationWarning;
exports.handleDeprecatedPositionalArgs = handleDeprecatedPositionalArgs;
exports.isDevelopmentModeImpl = isDevelopmentModeImpl;
exports.safeGlobalThis = safeGlobalThis;
exports.symbols = symbols;
