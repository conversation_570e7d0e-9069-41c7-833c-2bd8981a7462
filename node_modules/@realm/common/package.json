{"name": "@realm/common", "version": "0.1.4", "description": "Cross-product common code used by Realm", "main": "./dist/bundle.cjs.js", "module": "./dist/bundle.es.js", "types": "./dist/bundle.d.ts", "browser": {"./dist/bundle.cjs.js": "./dist/bundle.dom.cjs.js", "./dist/bundle.es.js": "./dist/bundle.dom.es.js", "./dist/bundle.d.ts": "./dist/bundle.dom.d.ts"}, "scripts": {"generate-types": "tsc --project tsconfig.types.json --declaration --emitDeclarationOnly --declarationDir types/generated", "build": "npm run generate-types && rollup --config", "lint": "eslint --ext .js,.ts .", "test": "mocha 'src/**/*.test.ts'", "prepare": "npm run build"}, "files": ["dist"], "author": {"name": "Realm", "email": "<EMAIL>", "url": "https://realm.io"}, "repository": {"type": "git", "url": "https://github.com/realm/realm-js.git"}, "bugs": {"url": "https://github.com/realm/realm-js/issues"}, "license": "Apache-2.0", "dependencies": {}, "devDependencies": {"@rollup/plugin-commonjs": "^14.0.0", "@rollup/plugin-node-resolve": "^7.1.3", "@rollup/plugin-typescript": "^4.1.1", "@types/chai": "^4.2.10", "@types/mocha": "^5", "chai": "^4.2.0", "mocha": "^5.2.0", "rollup": "^2.6.1", "rollup-plugin-dts": "^1.4.0"}}