{"author": "jdiaz5513", "bugs": {"url": "https://github.com/jdiaz5513/capnp-ts/issues"}, "dependencies": {"debug": "^4.3.1", "tslib": "^2.2.0"}, "description": "Strongly typed Cap'n Proto implementation for the browser and Node.js using TypeScript", "homepage": "https://github.com/jdiaz5513/capnp-ts#readme", "keywords": ["capnp", "rpc", "typescript"], "license": "MIT", "main": "./src/index.js", "name": "capnp-ts", "repository": {"type": "git", "url": "git+https://github.com/jdiaz5513/capnp-ts.git"}, "types": "./src/index.d.ts", "version": "0.7.0"}