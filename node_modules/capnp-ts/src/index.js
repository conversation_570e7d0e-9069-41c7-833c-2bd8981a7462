"use strict";
/**
 * <AUTHOR>
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Uint64 = exports.Int64 = exports.getUint8Mask = exports.getUint64Mask = exports.getUint32Mask = exports.getUint16Mask = exports.getInt8Mask = exports.getInt64Mask = exports.getInt32Mask = exports.getInt16Mask = exports.getFloat64Mask = exports.getFloat32Mask = exports.getBitMask = exports.Void = exports.VoidList = exports.Uint8List = exports.Uint64List = exports.Uint32List = exports.Uint16List = exports.TextList = exports.Text = exports.Struct = exports.Pointer = exports.PointerType = exports.PointerList = exports.Orphan = exports.List = exports.InterfaceList = exports.Interface = exports.Int8List = exports.Int64List = exports.Int32List = exports.Int16List = exports.Float64List = exports.Float32List = exports.DataList = exports.Data = exports.CompositeList = exports.BoolList = exports.AnyPointerList = exports.readRawPointer = exports.ObjectSize = exports.Message = exports.ListElementSize = void 0;
var serialization_1 = require("./serialization");
Object.defineProperty(exports, "ListElementSize", { enumerable: true, get: function () { return serialization_1.ListElementSize; } });
Object.defineProperty(exports, "Message", { enumerable: true, get: function () { return serialization_1.Message; } });
Object.defineProperty(exports, "ObjectSize", { enumerable: true, get: function () { return serialization_1.ObjectSize; } });
Object.defineProperty(exports, "readRawPointer", { enumerable: true, get: function () { return serialization_1.readRawPointer; } });
Object.defineProperty(exports, "AnyPointerList", { enumerable: true, get: function () { return serialization_1.AnyPointerList; } });
Object.defineProperty(exports, "BoolList", { enumerable: true, get: function () { return serialization_1.BoolList; } });
Object.defineProperty(exports, "CompositeList", { enumerable: true, get: function () { return serialization_1.CompositeList; } });
Object.defineProperty(exports, "Data", { enumerable: true, get: function () { return serialization_1.Data; } });
Object.defineProperty(exports, "DataList", { enumerable: true, get: function () { return serialization_1.DataList; } });
Object.defineProperty(exports, "Float32List", { enumerable: true, get: function () { return serialization_1.Float32List; } });
Object.defineProperty(exports, "Float64List", { enumerable: true, get: function () { return serialization_1.Float64List; } });
Object.defineProperty(exports, "Int16List", { enumerable: true, get: function () { return serialization_1.Int16List; } });
Object.defineProperty(exports, "Int32List", { enumerable: true, get: function () { return serialization_1.Int32List; } });
Object.defineProperty(exports, "Int64List", { enumerable: true, get: function () { return serialization_1.Int64List; } });
Object.defineProperty(exports, "Int8List", { enumerable: true, get: function () { return serialization_1.Int8List; } });
Object.defineProperty(exports, "Interface", { enumerable: true, get: function () { return serialization_1.Interface; } });
Object.defineProperty(exports, "InterfaceList", { enumerable: true, get: function () { return serialization_1.InterfaceList; } });
Object.defineProperty(exports, "List", { enumerable: true, get: function () { return serialization_1.List; } });
Object.defineProperty(exports, "Orphan", { enumerable: true, get: function () { return serialization_1.Orphan; } });
Object.defineProperty(exports, "PointerList", { enumerable: true, get: function () { return serialization_1.PointerList; } });
Object.defineProperty(exports, "PointerType", { enumerable: true, get: function () { return serialization_1.PointerType; } });
Object.defineProperty(exports, "Pointer", { enumerable: true, get: function () { return serialization_1.Pointer; } });
Object.defineProperty(exports, "Struct", { enumerable: true, get: function () { return serialization_1.Struct; } });
Object.defineProperty(exports, "Text", { enumerable: true, get: function () { return serialization_1.Text; } });
Object.defineProperty(exports, "TextList", { enumerable: true, get: function () { return serialization_1.TextList; } });
Object.defineProperty(exports, "Uint16List", { enumerable: true, get: function () { return serialization_1.Uint16List; } });
Object.defineProperty(exports, "Uint32List", { enumerable: true, get: function () { return serialization_1.Uint32List; } });
Object.defineProperty(exports, "Uint64List", { enumerable: true, get: function () { return serialization_1.Uint64List; } });
Object.defineProperty(exports, "Uint8List", { enumerable: true, get: function () { return serialization_1.Uint8List; } });
Object.defineProperty(exports, "VoidList", { enumerable: true, get: function () { return serialization_1.VoidList; } });
Object.defineProperty(exports, "Void", { enumerable: true, get: function () { return serialization_1.Void; } });
Object.defineProperty(exports, "getBitMask", { enumerable: true, get: function () { return serialization_1.getBitMask; } });
Object.defineProperty(exports, "getFloat32Mask", { enumerable: true, get: function () { return serialization_1.getFloat32Mask; } });
Object.defineProperty(exports, "getFloat64Mask", { enumerable: true, get: function () { return serialization_1.getFloat64Mask; } });
Object.defineProperty(exports, "getInt16Mask", { enumerable: true, get: function () { return serialization_1.getInt16Mask; } });
Object.defineProperty(exports, "getInt32Mask", { enumerable: true, get: function () { return serialization_1.getInt32Mask; } });
Object.defineProperty(exports, "getInt64Mask", { enumerable: true, get: function () { return serialization_1.getInt64Mask; } });
Object.defineProperty(exports, "getInt8Mask", { enumerable: true, get: function () { return serialization_1.getInt8Mask; } });
Object.defineProperty(exports, "getUint16Mask", { enumerable: true, get: function () { return serialization_1.getUint16Mask; } });
Object.defineProperty(exports, "getUint32Mask", { enumerable: true, get: function () { return serialization_1.getUint32Mask; } });
Object.defineProperty(exports, "getUint64Mask", { enumerable: true, get: function () { return serialization_1.getUint64Mask; } });
Object.defineProperty(exports, "getUint8Mask", { enumerable: true, get: function () { return serialization_1.getUint8Mask; } });
var types_1 = require("./types");
Object.defineProperty(exports, "Int64", { enumerable: true, get: function () { return types_1.Int64; } });
Object.defineProperty(exports, "Uint64", { enumerable: true, get: function () { return types_1.Uint64; } });
//# sourceMappingURL=index.js.map