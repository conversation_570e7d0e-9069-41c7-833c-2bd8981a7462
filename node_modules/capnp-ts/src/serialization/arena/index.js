"use strict";
/**
 * <AUTHOR>
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SingleSegmentArena = exports.MultiSegmentArena = exports.ArenaKind = exports.Arena = void 0;
var arena_1 = require("./arena");
Object.defineProperty(exports, "Arena", { enumerable: true, get: function () { return arena_1.Arena; } });
var arena_kind_1 = require("./arena-kind");
Object.defineProperty(exports, "ArenaKind", { enumerable: true, get: function () { return arena_kind_1.ArenaKind; } });
var multi_segment_arena_1 = require("./multi-segment-arena");
Object.defineProperty(exports, "MultiSegmentArena", { enumerable: true, get: function () { return multi_segment_arena_1.MultiSegmentArena; } });
var single_segment_arena_1 = require("./single-segment-arena");
Object.defineProperty(exports, "SingleSegmentArena", { enumerable: true, get: function () { return single_segment_arena_1.SingleSegmentArena; } });
//# sourceMappingURL=index.js.map