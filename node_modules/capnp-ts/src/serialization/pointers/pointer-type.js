"use strict";
/**
 * <AUTHOR>
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.PointerType = void 0;
var PointerType;
(function (PointerType) {
    PointerType[PointerType["STRUCT"] = 0] = "STRUCT";
    PointerType[PointerType["LIST"] = 1] = "LIST";
    PointerType[PointerType["FAR"] = 2] = "FAR";
    PointerType[PointerType["OTHER"] = 3] = "OTHER";
})(PointerType = exports.PointerType || (exports.PointerType = {}));
//# sourceMappingURL=pointer-type.js.map