{"version": 3, "file": "orphan.js", "sourceRoot": "", "sources": ["orphan.ts"], "names": [], "mappings": ";;;;AAAA,0DAA8B;AAE9B,yCAAsG;AACtG,qCAAoC;AACpC,4DAAuD;AACvD,gDAA2D;AAE3D,uCAgBmB;AACnB,iDAA6C;AAE7C,MAAM,KAAK,GAAG,eAAS,CAAC,cAAc,CAAC,CAAC;AACxC,KAAK,CAAC,MAAM,CAAC,CAAC;AAUd,oHAAoH;AACpH,sEAAsE;AAEtE;;;;;;;;;GASG;AAEH,MAAa,MAAM;IAOjB,YAAY,GAAM;QAChB,MAAM,CAAC,GAAG,oBAAU,CAAC,GAAG,CAAC,CAAC;QAE1B,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC;QAE/B,IAAI,CAAC,MAAM,GAAG,EAAa,CAAC;QAE5B,iFAAiF;QAEjF,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,8BAAoB,CAAC,GAAG,CAAC,CAAC;QAE7C,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;YACxB,KAAK,0BAAW,CAAC,MAAM;gBACrB,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,6BAAmB,CAAC,GAAG,CAAC,CAAC;gBAE5C,MAAM;YAER,KAAK,0BAAW,CAAC,IAAI;gBACnB,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,6BAAmB,CAAC,GAAG,CAAC,CAAC;gBAC9C,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,kCAAwB,CAAC,GAAG,CAAC,CAAC;gBAExD,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,KAAK,mCAAe,CAAC,SAAS,EAAE;oBACzD,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,oCAA0B,CAAC,GAAG,CAAC,CAAC;iBACpD;gBAED,MAAM;YAER,KAAK,0BAAW,CAAC,KAAK;gBACpB,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,yBAAe,CAAC,GAAG,CAAC,CAAC;gBAEzC,MAAM;YAER;gBACE,8BAA8B;gBAC9B,0BAA0B;gBAC1B,MAAM,IAAI,KAAK,CAAC,iCAAwB,CAAC,CAAC;SAC7C;QAED,uDAAuD;QAEvD,sBAAY,CAAC,GAAG,CAAC,CAAC;IACpB,CAAC;IAED;;;;;OAKG;IAEH,OAAO,CAAC,GAAM;QACZ,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE;YAC7B,MAAM,IAAI,KAAK,CAAC,aAAM,CAAC,4BAAmB,EAAE,IAAI,CAAC,CAAC,CAAC;SACpD;QAED,oDAAoD;QACpD,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE;YAChD,MAAM,IAAI,KAAK,CAAC,aAAM,CAAC,gCAAuB,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;SAC7D;QAED,sDAAsD;QAEtD,eAAK,CAAC,GAAG,CAAC,CAAC;QAEX,MAAM,GAAG,GAAG,qBAAW,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;QAE5D,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;YACxB,KAAK,0BAAW,CAAC,MAAM;gBACrB,0BAAgB,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;gBAEjE,MAAM;YAER,KAAK,0BAAW,CAAC,IAAI,CAAC,CAAC;gBACrB,IAAI,WAAW,GAAG,GAAG,CAAC,WAAW,CAAC;gBAElC,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,KAAK,mCAAe,CAAC,SAAS,EAAE;oBACzD,WAAW,EAAE,CAAC,CAAC,6BAA6B;iBAC7C;gBAED,wBAAc,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBAExG,MAAM;aACP;YACD,KAAK,0BAAW,CAAC,KAAK;gBACpB,6BAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;gBAEpD,MAAM;YAER,0BAA0B;YAC1B;gBACE,MAAM,IAAI,KAAK,CAAC,iCAAwB,CAAC,CAAC;SAC7C;QAED,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;IAC1B,CAAC;IAED,OAAO;QACL,4BAA4B;QAC5B,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE;YAC7B,KAAK,CAAC,0CAA0C,EAAE,IAAI,CAAC,CAAC;YAExD,OAAO;SACR;QAED,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;YACxB,KAAK,0BAAW,CAAC,MAAM;gBACrB,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,EAAE,2BAAa,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;gBAE7E,MAAM;YAER,KAAK,0BAAW,CAAC,IAAI,CAAC,CAAC;gBACrB,MAAM,UAAU,GAAG,2BAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBACpG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;gBAExD,MAAM;aACP;YACD;gBACE,uDAAuD;gBAEvD,MAAM;SACT;QAED,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;IAC1B,CAAC;IAED,QAAQ;QACN,OAAO,aAAM,CAAC,sBAAsB,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC3G,CAAC;CACF;AAxID,wBAwIC"}