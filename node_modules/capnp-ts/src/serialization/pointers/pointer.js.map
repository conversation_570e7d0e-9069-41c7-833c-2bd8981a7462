{"version": 3, "file": "pointer.js", "sourceRoot": "", "sources": ["pointer.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;;AAEH,0DAA8B;AAE9B,+CAAwG;AACxG,qCAA4D;AAC5D,4DAAuD;AACvD,gDAMwB;AAExB,qCAAkC;AAClC,2EAAsE;AACtE,iDAA6C;AAE7C,yCAWsB;AAEtB,MAAM,KAAK,GAAG,eAAS,CAAC,eAAe,CAAC,CAAC;AACzC,KAAK,CAAC,MAAM,CAAC,CAAC;AAwBd;;;;;;GAMG;AAEH,MAAa,OAAO;IAwBlB,YAAY,OAAgB,EAAE,UAAkB,EAAE,UAAU,GAAG,qBAAS;QACtE,IAAI,CAAC,MAAM,GAAG,EAAE,aAAa,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC;QACnD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAE7B,IAAI,UAAU,KAAK,CAAC,EAAE;YACpB,MAAM,IAAI,KAAK,CAAC,aAAM,CAAC,iCAAwB,EAAE,IAAI,CAAC,CAAC,CAAC;SACzD;QAED,iGAAiG;QAEjG,sBAAsB,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAE9C,6GAA6G;QAC7G,yGAAyG;QACzG,iHAAiH;QACjH,eAAe;QAEf,IAAI,UAAU,GAAG,CAAC,IAAI,UAAU,GAAG,OAAO,CAAC,UAAU,EAAE;YACrD,MAAM,IAAI,KAAK,CAAC,aAAM,CAAC,iCAAwB,EAAE,UAAU,CAAC,CAAC,CAAC;SAC/D;QAED,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IACxB,CAAC;IAED,QAAQ;QACN,OAAO,aAAM,CAAC,2BAA2B,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;IACnH,CAAC;;AAnDH,0BAoDC;AAnDiB,aAAK,GAAG,KAAK,CAAC;AACd,gBAAQ,GAAG,QAAQ,CAAC;AACpB,cAAM,GAAG,MAAM,CAAC;AAChB,YAAI,GAAG,IAAI,CAAC;AACZ,cAAM,GAAG,MAAM,CAAC;AAEhB,cAAM,GAAiB;IACrC,WAAW,EAAE,SAAmB;CACjC,CAAC;AA6CJ;;;;;;GAMG;AAEH,SAAgB,KAAK,CAAoB,GAAc,EAAE,CAAI;IAC3D,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACjB,CAAC;AAFD,sBAEC;AAED;;;;;;;;;GASG;AAEH,SAAgB,MAAM,CAAoB,CAAI;IAC5C,OAAO,IAAI,eAAM,CAAC,CAAC,CAAC,CAAC;AACvB,CAAC;AAFD,wBAEC;AAED,SAAgB,IAAI,CAAC,CAAU;IAC7B,OAAO,kBAAW,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7E,CAAC;AAFD,oBAEC;AAED;;;;;;;;;GASG;AAEH,SAAgB,iBAAiB,CAAC,WAA4B,EAAE,MAAc,EAAE,aAA0B;IACxG,QAAQ,WAAW,EAAE;QACnB,KAAK,mCAAe,CAAC,GAAG;YACtB,OAAO,gBAAS,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;QAEvC,KAAK,mCAAe,CAAC,IAAI,CAAC;QAC1B,KAAK,mCAAe,CAAC,MAAM,CAAC;QAC5B,KAAK,mCAAe,CAAC,MAAM,CAAC;QAC5B,KAAK,mCAAe,CAAC,MAAM,CAAC;QAC5B,KAAK,mCAAe,CAAC,OAAO,CAAC;QAC7B,KAAK,mCAAe,CAAC,IAAI;YACvB,OAAO,gBAAS,CAAC,wBAAwB,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC,CAAC;QAEnE,0BAA0B;QAC1B,KAAK,mCAAe,CAAC,SAAS;YAC5B,IAAI,aAAa,KAAK,SAAS,EAAE;gBAC/B,MAAM,IAAI,KAAK,CAAC,aAAM,CAAC,8BAAqB,EAAE,GAAG,CAAC,CAAC,CAAC;aACrD;YAED,OAAO,MAAM,GAAG,gBAAS,CAAC,2BAAa,CAAC,aAAa,CAAC,CAAC,CAAC;QAE1D,0BAA0B;QAC1B;YACE,MAAM,IAAI,KAAK,CAAC,8BAAqB,CAAC,CAAC;KAC1C;AACH,CAAC;AAzBD,8CAyBC;AAED;;;;;;GAMG;AAEH,SAAgB,wBAAwB,CAAC,WAA4B;IACnE,QAAQ,WAAW,EAAE;QACnB,0BAA0B;QAC1B,KAAK,mCAAe,CAAC,GAAG;YACtB,OAAO,GAAG,CAAC;QAEb,KAAK,mCAAe,CAAC,IAAI;YACvB,OAAO,CAAC,CAAC;QAEX,KAAK,mCAAe,CAAC,MAAM;YACzB,OAAO,CAAC,CAAC;QAEX,KAAK,mCAAe,CAAC,MAAM;YACzB,OAAO,CAAC,CAAC;QAEX,KAAK,mCAAe,CAAC,MAAM,CAAC;QAC5B,KAAK,mCAAe,CAAC,OAAO;YAC1B,OAAO,CAAC,CAAC;QAEX,0BAA0B;QAC1B,KAAK,mCAAe,CAAC,SAAS;YAC5B,qDAAqD;YAErD,OAAO,GAAG,CAAC;QAEb,0BAA0B;QAC1B,KAAK,mCAAe,CAAC,IAAI;YACvB,OAAO,CAAC,CAAC;QAEX,0BAA0B;QAC1B;YACE,MAAM,IAAI,KAAK,CAAC,aAAM,CAAC,8BAAqB,EAAE,WAAW,CAAC,CAAC,CAAC;KAC/D;AACH,CAAC;AAjCD,4DAiCC;AAED;;;;;;GAMG;AAEH,SAAgB,GAAG,CAAC,MAAc,EAAE,CAAU;IAC5C,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,UAAU,GAAG,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AAC5E,CAAC;AAFD,kBAEC;AAED;;;;;;GAMG;AAEH,SAAgB,QAAQ,CAAC,GAAY,EAAE,CAAU;IAC/C,kDAAkD;IAElD,IAAI,CAAC,CAAC,OAAO,KAAK,GAAG,CAAC,OAAO,IAAI,CAAC,CAAC,UAAU,KAAK,GAAG,CAAC,UAAU,EAAE;QAChE,KAAK,CAAC,mDAAmD,EAAE,GAAG,CAAC,CAAC;QAEhE,OAAO;KACR;IAED,iGAAiG;IAEjG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe;IAEzB,IAAI,MAAM,CAAC,GAAG,CAAC;QAAE,OAAO;IAExB,QAAQ,oBAAoB,CAAC,GAAG,CAAC,EAAE;QACjC,KAAK,0BAAW,CAAC,MAAM;YACrB,cAAc,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YAEvB,MAAM;QAER,KAAK,0BAAW,CAAC,IAAI;YACnB,YAAY,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YAErB,MAAM;QAER,0BAA0B;QAC1B;YACE,MAAM,IAAI,KAAK,CAAC,aAAM,CAAC,iCAAwB,EAAE,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC9E;AACH,CAAC;AA9BD,4BA8BC;AAED;;;;;;;;;;GAUG;AAEH,SAAgB,KAAK,CAAC,CAAU;IAC9B,IAAI,MAAM,CAAC,CAAC,CAAC;QAAE,OAAO;IAEtB,gCAAgC;IAEhC,IAAI,CAAU,CAAC;IAEf,QAAQ,oBAAoB,CAAC,CAAC,CAAC,EAAE;QAC/B,KAAK,0BAAW,CAAC,MAAM,CAAC,CAAC;YACvB,MAAM,IAAI,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YAElB,yBAAyB;YAEzB,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC;YAE/D,+CAA+C;YAE/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC,EAAE,EAAE;gBAC3C,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;aACtB;YAED,MAAM;SACP;QACD,KAAK,0BAAW,CAAC,IAAI,CAAC,CAAC;YACrB,MAAM,WAAW,GAAG,wBAAwB,CAAC,CAAC,CAAC,CAAC;YAChD,MAAM,MAAM,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAC;YACtC,IAAI,YAAY,GAAG,gBAAS,CAAC,MAAM,GAAG,wBAAwB,CAAC,WAAW,CAAC,CAAC,CAAC;YAC7E,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YAElB,IAAI,WAAW,KAAK,mCAAe,CAAC,OAAO,EAAE;gBAC3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC/B,KAAK,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;iBAC9E;gBAED,oFAAoF;gBAEpF,MAAM;aACP;iBAAM,IAAI,WAAW,KAAK,mCAAe,CAAC,SAAS,EAAE;gBACpD,qCAAqC;gBACrC,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACvB,MAAM,aAAa,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;gBACzC,MAAM,mBAAmB,GAAG,2BAAa,CAAC,aAAa,CAAC,CAAC;gBACzD,YAAY,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;gBAEnC,qBAAqB;gBACrB,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;gBAExC,kCAAkC;gBAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,aAAa,EAAE,CAAC,EAAE,EAAE;wBACpD,KAAK,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,UAAU,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;qBACxG;iBACF;aACF;YAED,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;YAEpD,MAAM;SACP;QACD,KAAK,0BAAW,CAAC,KAAK;YACpB,cAAc;YAEd,MAAM;QAER;YACE,MAAM,IAAI,KAAK,CAAC,aAAM,CAAC,iCAAwB,EAAE,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC9E;IAED,YAAY,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC;AAtED,sBAsEC;AAED;;;;;GAKG;AAEH,SAAgB,YAAY,CAAC,CAAU;IACrC,IAAI,cAAc,CAAC,CAAC,CAAC,KAAK,0BAAW,CAAC,GAAG,EAAE;QACzC,MAAM,UAAU,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;QAEhC,IAAI,WAAW,CAAC,CAAC,CAAC,EAAE;YAClB,gCAAgC;YAEhC,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;SAC3D;QAED,wBAAwB;QAExB,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;KACvD;IAED,sCAAsC;IAEtC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;AACtC,CAAC;AAlBD,oCAkBC;AAED;;;;;GAKG;AAEH,SAAgB,SAAS,CAAC,CAAU;IAClC,MAAM,aAAa,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;IAC1F,MAAM,gBAAgB,GAAG,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IAEjE,OAAO,IAAI,OAAO,CAAC,aAAa,EAAE,gBAAgB,GAAG,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;AACnF,CAAC;AALD,8BAKC;AAED;;;;;;GAMG;AAEH,SAAgB,UAAU,CAAC,CAAU;IACnC,IAAI,cAAc,CAAC,CAAC,CAAC,KAAK,0BAAW,CAAC,GAAG,EAAE;QACzC,MAAM,UAAU,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;QAEhC,IAAI,WAAW,CAAC,CAAC,CAAC;YAAE,UAAU,CAAC,UAAU,IAAI,CAAC,CAAC;QAE/C,OAAO,UAAU,CAAC;KACnB;IAED,OAAO,CAAC,CAAC;AACX,CAAC;AAVD,gCAUC;AAED,SAAgB,eAAe,CAAC,CAAU;IACxC,OAAO,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;AAC/C,CAAC;AAFD,0CAEC;AAED,SAAS,eAAe,CAAC,CAAU;IACjC,OAAO,oBAAoB,CAAC,CAAC,CAAC,KAAK,0BAAW,CAAC,IAAI,IAAI,wBAAwB,CAAC,CAAC,CAAC,KAAK,mCAAe,CAAC,SAAS,CAAC;AACnH,CAAC;AAED;;;;;;;;GAQG;AAEH,SAAgB,UAAU,CAAC,CAAU,EAAE,oBAA8B;IACnE,IAAI,CAAU,CAAC;IAEf,IAAI,WAAW,CAAC,CAAC,CAAC,EAAE;QAClB,MAAM,UAAU,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,EAAE,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;KAC5G;SAAM;QACL,MAAM,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,GAAG,IAAI,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,UAAU,GAAG,CAAC,GAAG,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;KACrF;IAED,IAAI,eAAe,CAAC,CAAC,CAAC;QAAE,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC;IAE1C,IAAI,CAAC,oBAAoB,IAAI,CAAC,CAAC,MAAM,CAAC,cAAc,KAAK,SAAS,EAAE;QAClE,8EAA8E;QAE9E,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC;QAElB,uEAAuE;QAEvE,CAAC,CAAC,UAAU,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,cAAc,GAAG,2BAAa,CAAC,uBAAe,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAChG;IAED,OAAO,CAAC,CAAC;AACX,CAAC;AAxBD,gCAwBC;AAED;;;;;GAKG;AAEH,SAAgB,eAAe,CAAC,CAAU;IACxC,OAAO,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;AAC/C,CAAC;AAFD,0CAEC;AAED;;;;;GAKG;AAEH,SAAgB,kBAAkB,CAAC,CAAU;IAC3C,OAAO,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,0BAAc,CAAC;AAChE,CAAC;AAFD,gDAEC;AAED;;;;;;;;;GASG;AAEH,SAAgB,aAAa,CAAC,CAAU;IACtC,OAAO,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;AACrD,CAAC;AAFD,sCAEC;AAED;;;;;;;GAOG;AAEH,SAAgB,cAAc,CAAC,CAAU;IACvC,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;IAE3C,yCAAyC;IACzC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AACjC,CAAC;AALD,wCAKC;AAED;;;;;GAKG;AAEH,SAAgB,cAAc,CAAC,CAAU;IACvC,OAAO,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,6BAAiB,CAAC;AAC/D,CAAC;AAFD,wCAEC;AAED;;;;;GAKG;AAEH,SAAgB,kBAAkB,CAAC,CAAU;IAC3C,OAAO,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;AAC/C,CAAC;AAFD,gDAEC;AAED;;;;;GAKG;AAEH,SAAgB,sBAAsB,CAAC,CAAU;IAC/C,OAAO,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;AAC/C,CAAC;AAFD,wDAEC;AAED;;;;;GAKG;AAEH,SAAgB,aAAa,CAAC,CAAU;IACtC,OAAO,IAAI,wBAAU,CAAC,kBAAkB,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9E,CAAC;AAFD,sCAEC;AAED;;;;;GAKG;AAEH,SAAgB,yBAAyB,CAAC,CAAU;IAClD,MAAM,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;IAExB,gEAAgE;IAEhE,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC;IAElB,OAAO,CAAC,CAAC;AACX,CAAC;AARD,8DAQC;AAED;;;;;GAKG;AAEH,SAAgB,0BAA0B,CAAC,CAAU;IACnD,OAAO,aAAa,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC;AACrD,CAAC;AAFD,gEAEC;AAED;;;;;GAKG;AAEH,SAAgB,wBAAwB,CAAC,CAAU;IACjD,OAAO,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,CAAC;AAFD,4DAEC;AAED;;;;;;GAMG;AAEH,SAAgB,mBAAmB,CAAC,CAAU;IAC5C,MAAM,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;IAExB,IAAI,kBAAkB,CAAC,CAAC,CAAC,KAAK,mCAAe,CAAC,SAAS,EAAE;QACvD,wGAAwG;QAExG,OAAO,cAAc,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC;KACrD;IAED,OAAO,aAAa,CAAC,CAAC,CAAC,CAAC;AAC1B,CAAC;AAVD,kDAUC;AAED;;;;;;;;;GASG;AAEH,SAAgB,oBAAoB,CAAC,CAAU;IAC7C,MAAM,CAAC,GAAG,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IAExC,IAAI,CAAC,KAAK,0BAAW,CAAC,GAAG;QAAE,MAAM,IAAI,KAAK,CAAC,aAAM,CAAC,+BAAsB,EAAE,CAAC,CAAC,CAAC,CAAC;IAE9E,OAAO,CAAC,CAAC;AACX,CAAC;AAND,oDAMC;AAED;;;;;GAKG;AAEH,SAAgB,mBAAmB,CAAC,CAAU;IAC5C,OAAO,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AACtC,CAAC;AAFD,kDAEC;AAED;;;;;;;;;;;;GAYG;AAEH,SAAgB,WAAW,CAAC,cAAuB,EAAE,aAAqB,EAAE,CAAU;IACpF,IAAI,CAAC,CAAC,OAAO,KAAK,cAAc,EAAE;QAChC,sBAAsB;QAEtB,KAAK,CAAC,oCAAoC,EAAE,CAAC,EAAE,cAAc,CAAC,CAAC;QAE/D,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;YAClC,kGAAkG;YAElG,MAAM,UAAU,GAAG,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAE1C,KAAK,CAAC,2DAA2D,EAAE,CAAC,EAAE,cAAc,EAAE,UAAU,CAAC,CAAC;YAElG,aAAa,CAAC,IAAI,EAAE,UAAU,CAAC,UAAU,GAAG,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;YACzE,aAAa,CAAC,KAAK,EAAE,aAAa,GAAG,CAAC,EAAE,cAAc,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;YAEvE,UAAU,CAAC,UAAU,IAAI,CAAC,CAAC;YAE3B,OAAO,IAAI,mDAAuB,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;SACnD;QAED,4DAA4D;QAE5D,MAAM,UAAU,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAE9C,IAAI,UAAU,CAAC,OAAO,CAAC,EAAE,KAAK,cAAc,CAAC,EAAE,EAAE;YAC/C,MAAM,IAAI,KAAK,CAAC,mCAA0B,CAAC,CAAC;SAC7C;QAED,aAAa,CAAC,KAAK,EAAE,UAAU,CAAC,UAAU,GAAG,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QAE1E,OAAO,IAAI,mDAAuB,CAAC,UAAU,EAAE,CAAC,aAAa,GAAG,UAAU,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;KACjG;IAED,KAAK,CAAC,8CAA8C,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC;IAExE,OAAO,IAAI,mDAAuB,CAAC,CAAC,EAAE,CAAC,aAAa,GAAG,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAChF,CAAC;AArCD,kCAqCC;AAED;;;;;GAKG;AAEH,SAAgB,WAAW,CAAC,CAAU;IACpC,OAAO,cAAc,CAAC,CAAC,CAAC,KAAK,0BAAW,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,mCAAuB,CAAC,KAAK,CAAC,CAAC;AACtH,CAAC;AAFD,kCAEC;AAED;;;;;;GAMG;AAEH,SAAgB,MAAM,CAAC,CAAU;IAC/B,OAAO,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;AAC5C,CAAC;AAFD,wBAEC;AAED;;;;;;;;;GASG;AAEH,SAAgB,UAAU,CAAC,GAAY,EAAE,GAAY;IACnD,MAAM,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;IAC1B,MAAM,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,CAAC,qBAAqB;IACzE,MAAM,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;IAEjD,uDAAuD;IACvD,KAAK,CAAC,GAAG,CAAC,CAAC;IAEX,MAAM,GAAG,GAAG,WAAW,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,UAAU,GAAG,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;IAElF,gDAAgD;IAChD,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,GAAG,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC,CAAC;IACnF,gCAAgC;IAChC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;IAE9D,YAAY,CAAC,GAAG,CAAC,CAAC;AACpB,CAAC;AAhBD,gCAgBC;AAED;;;;;;;;GAQG;AAEH,SAAgB,aAAa,CAAC,SAAkB,EAAE,WAAmB,EAAE,SAAiB,EAAE,CAAU;IAClG,MAAM,CAAC,GAAG,0BAAW,CAAC,GAAG,CAAC;IAC1B,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,MAAM,CAAC,GAAG,WAAW,CAAC;IACtB,MAAM,CAAC,GAAG,SAAS,CAAC;IAEpB,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC3D,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3C,CAAC;AARD,sCAQC;AAED;;;;;;GAMG;AAEH,SAAgB,mBAAmB,CAAC,KAAa,EAAE,CAAU;IAC3D,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,EAAE,0BAAW,CAAC,KAAK,CAAC,CAAC;IACrD,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;AAC/C,CAAC;AAHD,kDAGC;AAED;;;;;;;;;;GAUG;AAEH,SAAgB,cAAc,CAC5B,WAAmB,EACnB,IAAqB,EACrB,MAAc,EACd,CAAU,EACV,aAA0B;IAE1B,MAAM,CAAC,GAAG,0BAAW,CAAC,IAAI,CAAC;IAC3B,MAAM,CAAC,GAAG,WAAW,CAAC;IACtB,MAAM,CAAC,GAAG,IAAI,CAAC;IACf,IAAI,CAAC,GAAG,MAAM,CAAC;IAEf,IAAI,IAAI,KAAK,mCAAe,CAAC,SAAS,EAAE;QACtC,IAAI,aAAa,KAAK,SAAS,EAAE;YAC/B,MAAM,IAAI,SAAS,CAAC,sCAA6B,CAAC,CAAC;SACpD;QAED,CAAC,IAAI,2BAAa,CAAC,aAAa,CAAC,CAAC;KACnC;IAED,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAChD,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACtD,CAAC;AAtBD,wCAsBC;AAED;;;;;;;;GAQG;AAEH,SAAgB,gBAAgB,CAAC,WAAmB,EAAE,IAAgB,EAAE,CAAU;IAChF,MAAM,CAAC,GAAG,0BAAW,CAAC,MAAM,CAAC;IAC7B,MAAM,CAAC,GAAG,WAAW,CAAC;IACtB,MAAM,CAAC,GAAG,+BAAiB,CAAC,IAAI,CAAC,CAAC;IAClC,MAAM,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;IAE7B,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAChD,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;IACzC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3C,CAAC;AATD,4CASC;AAED;;;;;;;;GAQG;AAEH,SAAgB,QAAQ,CAAC,WAAwB,EAAE,CAAU,EAAE,WAA6B;IAC1F,IAAI,MAAM,CAAC,CAAC,CAAC;QAAE,OAAO;IAEtB,MAAM,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;IAExB,0BAA0B;IAE1B,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,6BAAiB,CAAC;IAEhE,IAAI,CAAC,KAAK,WAAW,EAAE;QACrB,MAAM,IAAI,KAAK,CAAC,aAAM,CAAC,+BAAsB,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC;KACjE;IAED,4CAA4C;IAE5C,IAAI,WAAW,KAAK,SAAS,EAAE;QAC7B,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,0BAAc,CAAC;QAEjE,IAAI,CAAC,KAAK,WAAW,EAAE;YACrB,MAAM,IAAI,KAAK,CAAC,aAAM,CAAC,4BAAmB,EAAE,CAAC,EAAE,mCAAe,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;SAC/E;KACF;AACH,CAAC;AAtBD,4BAsBC;AAED,SAAgB,YAAY,CAAC,GAAY,EAAE,GAAY;IACrD,IAAI,GAAG,CAAC,MAAM,CAAC,UAAU,IAAI,CAAC;QAAE,MAAM,IAAI,KAAK,CAAC,iCAAwB,CAAC,CAAC;IAE1E,MAAM,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;IACnC,MAAM,cAAc,GAAG,wBAAwB,CAAC,GAAG,CAAC,CAAC;IACrD,MAAM,SAAS,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAC;IAC3C,IAAI,gBAAgB,CAAC;IACrB,IAAI,mBAAmB,CAAC;IACxB,IAAI,UAAU,CAAC;IAEf,IAAI,cAAc,KAAK,mCAAe,CAAC,OAAO,EAAE;QAC9C,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC;QAElD,6CAA6C;QAE7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;YAClC,MAAM,MAAM,GAAG,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,UAAU,CAAC,UAAU,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;YAC5G,MAAM,MAAM,GAAG,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,UAAU,CAAC,UAAU,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;YAE5G,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;SAC1B;KACF;SAAM,IAAI,cAAc,KAAK,mCAAe,CAAC,SAAS,EAAE;QACvD,gBAAgB,GAAG,uBAAe,CAAC,0BAA0B,CAAC,GAAG,CAAC,CAAC,CAAC;QACpE,mBAAmB,GAAG,2BAAa,CAAC,gBAAgB,CAAC,CAAC;QAEtD,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,2BAAa,CAAC,gBAAgB,CAAC,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC;QAEnF,qBAAqB;QAErB,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC,OAAO,EAAE,UAAU,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;QAElG,0GAA0G;QAC1G,6FAA6F;QAE7F,uEAAuE;QACvE,IAAI,gBAAgB,CAAC,cAAc,GAAG,CAAC,EAAE;YACvC,MAAM,UAAU,GAAG,2BAAa,CAAC,gBAAgB,CAAC,GAAG,SAAS,CAAC;YAE/D,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,UAAU,GAAG,CAAC,EAAE,UAAU,CAAC,OAAO,EAAE,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;SAChH;QAED,oDAAoD;QAEpD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;YAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,aAAa,EAAE,CAAC,EAAE,EAAE;gBACvD,MAAM,MAAM,GAAG,CAAC,GAAG,mBAAmB,GAAG,gBAAgB,CAAC,cAAc,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBAEpF,MAAM,MAAM,GAAG,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,UAAU,CAAC,UAAU,GAAG,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;gBAC1G,MAAM,MAAM,GAAG,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,UAAU,CAAC,UAAU,GAAG,MAAM,GAAG,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;gBAE9G,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;aAC1B;SACF;KACF;SAAM;QACL,MAAM,UAAU,GAAG,gBAAS,CAC1B,cAAc,KAAK,mCAAe,CAAC,GAAG;YACpC,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,KAAK,CAAC;YACvB,CAAC,CAAC,wBAAwB,CAAC,cAAc,CAAC,GAAG,SAAS,CACzD,CAAC;QACF,MAAM,UAAU,GAAG,UAAU,KAAK,CAAC,CAAC;QAEpC,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAE9C,8CAA8C;QAE9C,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC,OAAO,EAAE,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;KAC5G;IAED,+BAA+B;IAE/B,MAAM,GAAG,GAAG,WAAW,CAAC,UAAU,CAAC,OAAO,EAAE,UAAU,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;IACxE,cAAc,CAAC,GAAG,CAAC,WAAW,EAAE,cAAc,EAAE,SAAS,EAAE,GAAG,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;AAC5F,CAAC;AAxED,oCAwEC;AAED,SAAgB,cAAc,CAAC,GAAY,EAAE,GAAY;IACvD,IAAI,GAAG,CAAC,MAAM,CAAC,UAAU,IAAI,CAAC;QAAE,MAAM,IAAI,KAAK,CAAC,iCAAwB,CAAC,CAAC;IAE1E,MAAM,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;IACnC,MAAM,OAAO,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAC;IACzC,MAAM,iBAAiB,GAAG,+BAAiB,CAAC,OAAO,CAAC,CAAC;IAErD,8CAA8C;IAE9C,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,2BAAa,CAAC,OAAO,CAAC,CAAC,CAAC;IAEhE,yBAAyB;IAEzB,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC,OAAO,EAAE,UAAU,CAAC,UAAU,EAAE,iBAAiB,CAAC,CAAC;IAElH,4BAA4B;IAE5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC,EAAE,EAAE;QAC9C,MAAM,MAAM,GAAG,OAAO,CAAC,cAAc,GAAG,CAAC,GAAG,CAAC,CAAC;QAE9C,MAAM,MAAM,GAAG,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,UAAU,CAAC,UAAU,GAAG,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;QAC1G,MAAM,MAAM,GAAG,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,UAAU,CAAC,UAAU,GAAG,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;QAE1G,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KAC1B;IAED,kHAAkH;IAClH,iEAAiE;IAEjE,IAAI,GAAG,CAAC,MAAM,CAAC,aAAa;QAAE,OAAO;IAErC,iCAAiC;IAEjC,MAAM,GAAG,GAAG,WAAW,CAAC,UAAU,CAAC,OAAO,EAAE,UAAU,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;IACxE,gBAAgB,CAAC,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;AAC1D,CAAC;AAnCD,wCAmCC;AAED;;;;;;;;;GASG;AAEH,SAAgB,sBAAsB,CAAC,OAAgB,EAAE,CAAU;IACjE,OAAO,CAAC,MAAM,CAAC,cAAc,IAAI,CAAC,CAAC;IAEnC,IAAI,OAAO,CAAC,MAAM,CAAC,cAAc,IAAI,CAAC,EAAE;QACtC,MAAM,IAAI,KAAK,CAAC,aAAM,CAAC,qCAA4B,EAAE,CAAC,CAAC,CAAC,CAAC;KAC1D;AACH,CAAC;AAND,wDAMC"}