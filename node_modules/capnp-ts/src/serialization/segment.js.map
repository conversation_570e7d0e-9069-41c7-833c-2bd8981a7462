{"version": 3, "file": "segment.js", "sourceRoot": "", "sources": ["segment.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;;;AAEH,0DAA8B;AAE9B,4CAAwE;AACxE,sCAAiG;AACjG,oCAAyC;AACzC,kCAA4C;AAE5C,yCAAqC;AAErC,MAAM,KAAK,GAAG,eAAS,CAAC,eAAe,CAAC,CAAC;AACzC,KAAK,CAAC,MAAM,CAAC,CAAC;AAEd,MAAa,OAAO;IAwBlB,YAAY,EAAU,EAAE,OAAgB,EAAE,MAAmB,EAAE,UAAU,GAAG,CAAC;QARpE,QAAoB,GAAG,SAAuB,CAAC;QAStD,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,GAAG,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC;QAEhC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;IAED;;;;;;OAMG;IAEH,QAAQ,CAAC,UAAkB;QACzB,KAAK,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;QAElC,4DAA4D;QAC5D,IAAI,OAAO,GAAY,IAAI,CAAC;QAE5B,UAAU,GAAG,gBAAS,CAAC,UAAU,CAAC,CAAC;QAEnC,IAAI,UAAU,GAAG,8BAAkB,GAAG,CAAC,EAAE;YACvC,MAAM,IAAI,KAAK,CAAC,aAAM,CAAC,0BAAiB,EAAE,UAAU,CAAC,CAAC,CAAC;SACxD;QAED,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE;YACpC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;SACvD;QAED,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;QAEtC,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,GAAG,UAAU,CAAC;QAErD,KAAK,CAAC,mDAAmD,EAAE,UAAU,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QAEtF,OAAO,IAAI,kBAAO,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;IAC1C,CAAC;IAED;;;;;;;OAOG;IAEH,QAAQ,CAAC,UAAkB,EAAE,UAAmB,EAAE,aAAqB;QACrE,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,aAAa,EAAE,gCAAoB,CAAC,CAAC;QAE7E,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,UAAU,EAAE,KAAK,EAAE,gCAAoB,CAAC,CAAC;IAC/D,CAAC;IAED;;;;;;;;OAQG;IAEH,SAAS,CAAC,UAAkB,EAAE,UAAmB,EAAE,aAAqB,EAAE,UAAkB;QAC1F,MAAM,GAAG,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;QAClE,MAAM,GAAG,GAAG,IAAI,YAAY,CAAC,UAAU,CAAC,MAAM,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;QAE3E,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACf,CAAC;IAED;;;;;;OAMG;IAEH,aAAa,CAAC,UAAkB,EAAE,UAAkB;QAClD,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAChE,CAAC;IAED,qDAAqD;IAErD,WAAW,CAAC,UAAkB,EAAE,YAAsB;QACpD,MAAM,IAAI,KAAK,CAAC,aAAM,CAAC,wBAAe,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC,CAAC;IACrE,CAAC;IAED,qDAAqD;IAErD,YAAY,CAAC,UAAkB,EAAE,YAAsB;QACrD,MAAM,IAAI,KAAK,CAAC,aAAM,CAAC,wBAAe,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC,CAAC;IACrE,CAAC;IAED;;;;OAIG;IAEH,WAAW;QACT,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;IAChC,CAAC;IAED;;;;;OAKG;IAEH,UAAU,CAAC,UAAkB;QAC3B,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IAC/C,CAAC;IAED;;;;;OAKG;IAEH,UAAU,CAAC,UAAkB;QAC3B,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IAC/C,CAAC;IAED;;;;;OAKG;IAEH,QAAQ,CAAC,UAAkB;QACzB,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED;;;;;OAKG;IAEH,QAAQ,CAAC,UAAkB;QACzB,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED;;;;;OAKG;IAEH,QAAQ,CAAC,UAAkB;QACzB,OAAO,IAAI,aAAK,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAClF,CAAC;IAED;;;;;OAKG;IAEH,OAAO,CAAC,UAAkB;QACxB,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IACtC,CAAC;IAED;;;;;OAKG;IAEH,SAAS,CAAC,UAAkB;QAC1B,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IAC9C,CAAC;IAED;;;;;OAKG;IAEH,SAAS,CAAC,UAAkB;QAC1B,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IAC9C,CAAC;IAED;;;;;;OAMG;IAEH,SAAS,CAAC,UAAkB;QAC1B,OAAO,IAAI,cAAM,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACnF,CAAC;IAED;;;;;OAKG;IAEH,QAAQ,CAAC,UAAkB;QACzB,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IACvC,CAAC;IAED,WAAW,CAAC,UAAkB;QAC5B,KAAK,CAAC,iBAAiB,EAAE,UAAU,CAAC,CAAC;QAErC,oCAAoC;QAEpC,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC;IAChE,CAAC;IAED;;;;;;;;;;OAUG;IAEH,UAAU,CAAC,UAAkB;QAC3B,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,UAAU,EAAE,gCAAoB,CAAC,KAAK,CAAC,CAAC;IACrE,CAAC;IAED;;;;;;OAMG;IAEH,aAAa,CAAC,MAAmB;QAC/B,KAAK,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC;QAEnC,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM;YAAE,OAAO;QAEnC,IAAI,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE;YACvC,MAAM,IAAI,KAAK,CAAC,yCAAgC,CAAC,CAAC;SACnD;QAED,IAAI,CAAC,GAAG,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC;QAChC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED,sDAAsD;IAEtD,WAAW,CAAC,UAAkB,EAAE,KAAa,EAAE,YAAsB;QACnE,MAAM,IAAI,KAAK,CAAC,aAAM,CAAC,wBAAe,EAAE,UAAU,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC,CAAC;IAC5E,CAAC;IAED,sDAAsD;IAEtD,YAAY,CAAC,UAAkB,EAAE,KAAa,EAAE,YAAsB;QACpE,MAAM,IAAI,KAAK,CAAC,aAAM,CAAC,wBAAe,EAAE,UAAU,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC,CAAC;IAC5E,CAAC;IAED;;;;;;OAMG;IAEH,UAAU,CAAC,UAAkB,EAAE,GAAW;QACxC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,UAAU,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED;;;;;;OAMG;IAEH,UAAU,CAAC,UAAkB,EAAE,GAAW;QACxC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,UAAU,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED;;;;;;OAMG;IAEH,QAAQ,CAAC,UAAkB,EAAE,GAAW;QACtC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;IAC3C,CAAC;IAED;;;;;;OAMG;IAEH,QAAQ,CAAC,UAAkB,EAAE,GAAW;QACtC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;IAC3C,CAAC;IAED;;;;;;OAMG;IAEH,OAAO,CAAC,UAAkB,EAAE,GAAW;QACrC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;IACpC,CAAC;IAED;;;;;;OAMG;IAEH,QAAQ,CAAC,UAAkB,EAAE,GAAU;QACrC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC;IAED;;;;;;OAMG;IAEH,SAAS,CAAC,UAAkB,EAAE,GAAW;QACvC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;IAC5C,CAAC;IAED;;;;;;OAMG;IAEH,SAAS,CAAC,UAAkB,EAAE,GAAW;QACvC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;IAC5C,CAAC;IAED;;;;;;;OAOG;IAEH,SAAS,CAAC,UAAkB,EAAE,GAAW;QACvC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC;IAED;;;;;;OAMG;IAEH,QAAQ,CAAC,UAAkB,EAAE,GAAW;QACtC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;IACrC,CAAC;IAED;;;;;;;;OAQG;IAEH,WAAW,CAAC,UAAkB;QAC5B,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC,EAAE,gCAAoB,CAAC,CAAC;IAC3D,CAAC;IAED,QAAQ;QACN,OAAO,aAAM,CACX,oCAAoC,EACpC,IAAI,CAAC,EAAE,EACP,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,MAAM,CAAC,UAAU,CACvB,CAAC;IACJ,CAAC;CACF;AA3cD,0BA2cC;KA3bW,MAAM,CAAC,WAAW"}