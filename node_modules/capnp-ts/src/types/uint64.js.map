{"version": 3, "file": "uint64.js", "sourceRoot": "", "sources": ["uint64.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;;AAEH,0DAA8B;AAE9B,4CAAuD;AACvD,sCAAkD;AAClD,kCAA8B;AAE9B,MAAM,KAAK,GAAG,eAAS,CAAC,cAAc,CAAC,CAAC;AACxC,KAAK,CAAC,MAAM,CAAC,CAAC;AAEd;;;;;;;;;GASG;AAEH,MAAa,MAAM;IAGjB;;;;;;;;OAQG;IAEH,YAAY,MAAkB;QAC5B,IAAI,MAAM,CAAC,UAAU,GAAG,CAAC;YAAE,MAAM,IAAI,UAAU,CAAC,8BAAqB,CAAC,CAAC;QAEvE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED,MAAM,CAAC,eAAe,CAAC,MAAmB,EAAE,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,KAAK;QACpE,IAAI,MAAM;YAAE,OAAO,IAAI,IAAI,CAAC,IAAI,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;QAE/D,OAAO,IAAI,IAAI,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACpE,CAAC;IAED,MAAM,CAAC,YAAY,CAAC,MAAgB,EAAE,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,KAAK;QAC9D,IAAI,MAAM,EAAE;YACV,OAAO,IAAI,IAAI,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,UAAU,GAAG,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;SAC/E;QAED,OAAO,IAAI,IAAI,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,GAAG,MAAM,EAAE,MAAM,CAAC,UAAU,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACnH,CAAC;IAED;;;;;;OAMG;IAEH,MAAM,CAAC,aAAa,CAAC,MAAc;QACjC,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI;YAAE,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAE5D,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC;YAAE,OAAO,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAEnD,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG;YAAE,MAAM,IAAI,UAAU,CAAC,8BAA8B,CAAC,CAAC;QAE5E,MAAM,GAAG,UAAG,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAEzB,IAAI,MAAM,CAAC,MAAM,KAAK,EAAE,EAAE;YACxB,MAAM,IAAI,UAAU,CAAC,2DAA2D,CAAC,CAAC;SACnF;QAED,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;QAC5D,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QAE/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC1B,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;SACnD;QAED,OAAO,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC;IACzB,CAAC;IAED,MAAM,CAAC,UAAU,CAAC,MAAc;QAC9B,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAExC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAErB,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAC,cAAc,CAAC,MAAkB,EAAE,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,KAAK;QAClE,IAAI,MAAM;YAAE,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;QAEjE,OAAO,IAAI,IAAI,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,GAAG,MAAM,EAAE,MAAM,CAAC,UAAU,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACnH,CAAC;IAED,MAAM,CAAC,KAAa;QAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC1B,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;gBAAE,OAAO,KAAK,CAAC;SACtD;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO;QACL,OAAO,WAAW,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC;IACjE,CAAC;IAED;;;;;OAKG;IAEH,MAAM;QACJ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC1B,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC;gBAAE,OAAO,KAAK,CAAC;SACxC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,QAAQ,CAAC,MAAc,EAAE,MAAe;QACtC,IAAI,EAAE,GAAG,MAAM,CAAC;QAChB,IAAI,EAAE,GAAG,MAAM,CAAC;QAEhB,IAAI,EAAE,KAAK,SAAS,EAAE;YACpB,EAAE,GAAG,EAAE,CAAC;YACR,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAClB,EAAE,GAAG,EAAE,GAAG,iBAAK,CAAC;YAChB,EAAE,GAAG,EAAE,GAAG,iBAAK,CAAC;YAEhB,IAAI,EAAE,GAAG,iBAAK;gBAAE,MAAM,IAAI,UAAU,CAAC,GAAG,MAAM,0BAA0B,CAAC,CAAC;YAE1E,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;SACf;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC1B,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;YAC3B,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;SAC9B;IACH,CAAC;IAED;;;;;;;;OAQG;IAEH,QAAQ,CAAC,cAAwB;QAC/B,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;QACtB,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,IAAI,CAAC,GAAG,CAAC,CAAC;QAEV,OAAO,CAAC,GAAG,CAAC,EAAE;YACZ,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAEf,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACX,CAAC,IAAI,GAAG,CAAC;YACT,CAAC,EAAE,CAAC;SACL;QAED,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,4BAAgB,EAAE;YAC5C,KAAK,CAAC,6CAA6C,EAAE,CAAC,CAAC,CAAC;YAExD,OAAO,QAAQ,CAAC;SACjB;QAED,OAAO,CAAC,CAAC;IACX,CAAC;IAED,OAAO;QACL,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC9B,CAAC;IAED,aAAa;QACX,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IAC5B,CAAC;IAED,UAAU;QACR,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAC1C,CAAC;IAED,WAAW;QACT,IAAI,GAAG,GAAG,EAAE,CAAC;QAEb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YAC3B,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAEpC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC;gBAAE,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;YAEhC,GAAG,IAAI,CAAC,CAAC;SACV;QAED,OAAO,GAAG,CAAC;IACb,CAAC;IAED,QAAQ,CAAC,KAAc;QACrB,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC7C,CAAC;IAED,YAAY;QACV,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;CACF;AAhMD,wBAgMC"}