/**
 * This file is generated by hand in order to bootstrap compiler development. It is intended to be an exact match to
 * compiled output.
 */

import * as capnp from "../index";
import { ObjectSize as __O, Struct as __S } from "../index";
export const _capnpFileId = "b312981b2552a250";
export enum Message_Which {
    UNIMPLEMENTED = 0,
    ABORT = 1,
    BOOTSTRAP = 8,
    CALL = 2,
    RETURN = 3,
    FINISH = 4,
    RESOLVE = 5,
    RELEASE = 6,
    DISEMBARGO = 13,
    OBSOLETE_SAVE = 7,
    OBSOLETE_DELETE = 9,
    PROVIDE = 10,
    ACCEPT = 11,
    JOIN = 12
}
export class Message extends __S {
    static readonly UNIMPLEMENTED = Message_Which.UNIMPLEMENTED;
    static readonly ABORT = Message_Which.ABORT;
    static readonly BOOTSTRAP = Message_Which.BOOTSTRAP;
    static readonly CALL = Message_Which.CALL;
    static readonly RETURN = Message_Which.RETURN;
    static readonly FINISH = Message_Which.FINISH;
    static readonly RESOLVE = Message_Which.RESOLVE;
    static readonly RELEASE = Message_Which.RELEASE;
    static readonly DISEMBARGO = Message_Which.DISEMBARGO;
    static readonly OBSOLETE_SAVE = Message_Which.OBSOLETE_SAVE;
    static readonly OBSOLETE_DELETE = Message_Which.OBSOLETE_DELETE;
    static readonly PROVIDE = Message_Which.PROVIDE;
    static readonly ACCEPT = Message_Which.ACCEPT;
    static readonly JOIN = Message_Which.JOIN;
    static readonly _capnp = { displayName: "Message", id: "91b79f1f808db032", size: new __O(8, 1) };
    adoptUnimplemented(value: capnp.Orphan<Message>): void {
        __S.setUint16(0, 0, this);
        __S.adopt(value, __S.getPointer(0, this));
    }
    disownUnimplemented(): capnp.Orphan<Message> { return __S.disown(this.getUnimplemented()); }
    getUnimplemented(): Message {
        __S.testWhich("unimplemented", __S.getUint16(0, this), 0, this);
        return __S.getStruct(0, Message, this);
    }
    hasUnimplemented(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    initUnimplemented(): Message {
        __S.setUint16(0, 0, this);
        return __S.initStructAt(0, Message, this);
    }
    isUnimplemented(): boolean { return __S.getUint16(0, this) === 0; }
    setUnimplemented(value: Message): void {
        __S.setUint16(0, 0, this);
        __S.copyFrom(value, __S.getPointer(0, this));
    }
    adoptAbort(value: capnp.Orphan<Exception>): void {
        __S.setUint16(0, 1, this);
        __S.adopt(value, __S.getPointer(0, this));
    }
    disownAbort(): capnp.Orphan<Exception> { return __S.disown(this.getAbort()); }
    getAbort(): Exception {
        __S.testWhich("abort", __S.getUint16(0, this), 1, this);
        return __S.getStruct(0, Exception, this);
    }
    hasAbort(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    initAbort(): Exception {
        __S.setUint16(0, 1, this);
        return __S.initStructAt(0, Exception, this);
    }
    isAbort(): boolean { return __S.getUint16(0, this) === 1; }
    setAbort(value: Exception): void {
        __S.setUint16(0, 1, this);
        __S.copyFrom(value, __S.getPointer(0, this));
    }
    adoptBootstrap(value: capnp.Orphan<Bootstrap>): void {
        __S.setUint16(0, 8, this);
        __S.adopt(value, __S.getPointer(0, this));
    }
    disownBootstrap(): capnp.Orphan<Bootstrap> { return __S.disown(this.getBootstrap()); }
    getBootstrap(): Bootstrap {
        __S.testWhich("bootstrap", __S.getUint16(0, this), 8, this);
        return __S.getStruct(0, Bootstrap, this);
    }
    hasBootstrap(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    initBootstrap(): Bootstrap {
        __S.setUint16(0, 8, this);
        return __S.initStructAt(0, Bootstrap, this);
    }
    isBootstrap(): boolean { return __S.getUint16(0, this) === 8; }
    setBootstrap(value: Bootstrap): void {
        __S.setUint16(0, 8, this);
        __S.copyFrom(value, __S.getPointer(0, this));
    }
    adoptCall(value: capnp.Orphan<Call>): void {
        __S.setUint16(0, 2, this);
        __S.adopt(value, __S.getPointer(0, this));
    }
    disownCall(): capnp.Orphan<Call> { return __S.disown(this.getCall()); }
    getCall(): Call {
        __S.testWhich("call", __S.getUint16(0, this), 2, this);
        return __S.getStruct(0, Call, this);
    }
    hasCall(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    initCall(): Call {
        __S.setUint16(0, 2, this);
        return __S.initStructAt(0, Call, this);
    }
    isCall(): boolean { return __S.getUint16(0, this) === 2; }
    setCall(value: Call): void {
        __S.setUint16(0, 2, this);
        __S.copyFrom(value, __S.getPointer(0, this));
    }
    adoptReturn(value: capnp.Orphan<Return>): void {
        __S.setUint16(0, 3, this);
        __S.adopt(value, __S.getPointer(0, this));
    }
    disownReturn(): capnp.Orphan<Return> { return __S.disown(this.getReturn()); }
    getReturn(): Return {
        __S.testWhich("return", __S.getUint16(0, this), 3, this);
        return __S.getStruct(0, Return, this);
    }
    hasReturn(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    initReturn(): Return {
        __S.setUint16(0, 3, this);
        return __S.initStructAt(0, Return, this);
    }
    isReturn(): boolean { return __S.getUint16(0, this) === 3; }
    setReturn(value: Return): void {
        __S.setUint16(0, 3, this);
        __S.copyFrom(value, __S.getPointer(0, this));
    }
    adoptFinish(value: capnp.Orphan<Finish>): void {
        __S.setUint16(0, 4, this);
        __S.adopt(value, __S.getPointer(0, this));
    }
    disownFinish(): capnp.Orphan<Finish> { return __S.disown(this.getFinish()); }
    getFinish(): Finish {
        __S.testWhich("finish", __S.getUint16(0, this), 4, this);
        return __S.getStruct(0, Finish, this);
    }
    hasFinish(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    initFinish(): Finish {
        __S.setUint16(0, 4, this);
        return __S.initStructAt(0, Finish, this);
    }
    isFinish(): boolean { return __S.getUint16(0, this) === 4; }
    setFinish(value: Finish): void {
        __S.setUint16(0, 4, this);
        __S.copyFrom(value, __S.getPointer(0, this));
    }
    adoptResolve(value: capnp.Orphan<Resolve>): void {
        __S.setUint16(0, 5, this);
        __S.adopt(value, __S.getPointer(0, this));
    }
    disownResolve(): capnp.Orphan<Resolve> { return __S.disown(this.getResolve()); }
    getResolve(): Resolve {
        __S.testWhich("resolve", __S.getUint16(0, this), 5, this);
        return __S.getStruct(0, Resolve, this);
    }
    hasResolve(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    initResolve(): Resolve {
        __S.setUint16(0, 5, this);
        return __S.initStructAt(0, Resolve, this);
    }
    isResolve(): boolean { return __S.getUint16(0, this) === 5; }
    setResolve(value: Resolve): void {
        __S.setUint16(0, 5, this);
        __S.copyFrom(value, __S.getPointer(0, this));
    }
    adoptRelease(value: capnp.Orphan<Release>): void {
        __S.setUint16(0, 6, this);
        __S.adopt(value, __S.getPointer(0, this));
    }
    disownRelease(): capnp.Orphan<Release> { return __S.disown(this.getRelease()); }
    getRelease(): Release {
        __S.testWhich("release", __S.getUint16(0, this), 6, this);
        return __S.getStruct(0, Release, this);
    }
    hasRelease(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    initRelease(): Release {
        __S.setUint16(0, 6, this);
        return __S.initStructAt(0, Release, this);
    }
    isRelease(): boolean { return __S.getUint16(0, this) === 6; }
    setRelease(value: Release): void {
        __S.setUint16(0, 6, this);
        __S.copyFrom(value, __S.getPointer(0, this));
    }
    adoptDisembargo(value: capnp.Orphan<Disembargo>): void {
        __S.setUint16(0, 13, this);
        __S.adopt(value, __S.getPointer(0, this));
    }
    disownDisembargo(): capnp.Orphan<Disembargo> { return __S.disown(this.getDisembargo()); }
    getDisembargo(): Disembargo {
        __S.testWhich("disembargo", __S.getUint16(0, this), 13, this);
        return __S.getStruct(0, Disembargo, this);
    }
    hasDisembargo(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    initDisembargo(): Disembargo {
        __S.setUint16(0, 13, this);
        return __S.initStructAt(0, Disembargo, this);
    }
    isDisembargo(): boolean { return __S.getUint16(0, this) === 13; }
    setDisembargo(value: Disembargo): void {
        __S.setUint16(0, 13, this);
        __S.copyFrom(value, __S.getPointer(0, this));
    }
    adoptObsoleteSave(value: capnp.Orphan<capnp.Pointer>): void {
        __S.setUint16(0, 7, this);
        __S.adopt(value, __S.getPointer(0, this));
    }
    disownObsoleteSave(): capnp.Orphan<capnp.Pointer> { return __S.disown(this.getObsoleteSave()); }
    getObsoleteSave(): capnp.Pointer {
        __S.testWhich("obsoleteSave", __S.getUint16(0, this), 7, this);
        return __S.getPointer(0, this);
    }
    hasObsoleteSave(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    isObsoleteSave(): boolean { return __S.getUint16(0, this) === 7; }
    setObsoleteSave(value: capnp.Pointer): void {
        __S.setUint16(0, 7, this);
        __S.copyFrom(value, __S.getPointer(0, this));
    }
    adoptObsoleteDelete(value: capnp.Orphan<capnp.Pointer>): void {
        __S.setUint16(0, 9, this);
        __S.adopt(value, __S.getPointer(0, this));
    }
    disownObsoleteDelete(): capnp.Orphan<capnp.Pointer> { return __S.disown(this.getObsoleteDelete()); }
    getObsoleteDelete(): capnp.Pointer {
        __S.testWhich("obsoleteDelete", __S.getUint16(0, this), 9, this);
        return __S.getPointer(0, this);
    }
    hasObsoleteDelete(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    isObsoleteDelete(): boolean { return __S.getUint16(0, this) === 9; }
    setObsoleteDelete(value: capnp.Pointer): void {
        __S.setUint16(0, 9, this);
        __S.copyFrom(value, __S.getPointer(0, this));
    }
    adoptProvide(value: capnp.Orphan<Provide>): void {
        __S.setUint16(0, 10, this);
        __S.adopt(value, __S.getPointer(0, this));
    }
    disownProvide(): capnp.Orphan<Provide> { return __S.disown(this.getProvide()); }
    getProvide(): Provide {
        __S.testWhich("provide", __S.getUint16(0, this), 10, this);
        return __S.getStruct(0, Provide, this);
    }
    hasProvide(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    initProvide(): Provide {
        __S.setUint16(0, 10, this);
        return __S.initStructAt(0, Provide, this);
    }
    isProvide(): boolean { return __S.getUint16(0, this) === 10; }
    setProvide(value: Provide): void {
        __S.setUint16(0, 10, this);
        __S.copyFrom(value, __S.getPointer(0, this));
    }
    adoptAccept(value: capnp.Orphan<Accept>): void {
        __S.setUint16(0, 11, this);
        __S.adopt(value, __S.getPointer(0, this));
    }
    disownAccept(): capnp.Orphan<Accept> { return __S.disown(this.getAccept()); }
    getAccept(): Accept {
        __S.testWhich("accept", __S.getUint16(0, this), 11, this);
        return __S.getStruct(0, Accept, this);
    }
    hasAccept(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    initAccept(): Accept {
        __S.setUint16(0, 11, this);
        return __S.initStructAt(0, Accept, this);
    }
    isAccept(): boolean { return __S.getUint16(0, this) === 11; }
    setAccept(value: Accept): void {
        __S.setUint16(0, 11, this);
        __S.copyFrom(value, __S.getPointer(0, this));
    }
    adoptJoin(value: capnp.Orphan<Join>): void {
        __S.setUint16(0, 12, this);
        __S.adopt(value, __S.getPointer(0, this));
    }
    disownJoin(): capnp.Orphan<Join> { return __S.disown(this.getJoin()); }
    getJoin(): Join {
        __S.testWhich("join", __S.getUint16(0, this), 12, this);
        return __S.getStruct(0, Join, this);
    }
    hasJoin(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    initJoin(): Join {
        __S.setUint16(0, 12, this);
        return __S.initStructAt(0, Join, this);
    }
    isJoin(): boolean { return __S.getUint16(0, this) === 12; }
    setJoin(value: Join): void {
        __S.setUint16(0, 12, this);
        __S.copyFrom(value, __S.getPointer(0, this));
    }
    toString(): string { return "Message_" + super.toString(); }
    which(): Message_Which { return __S.getUint16(0, this); }
}
export class Bootstrap extends __S {
    static readonly _capnp = { displayName: "Bootstrap", id: "e94ccf8031176ec4", size: new __O(8, 1) };
    getQuestionId(): number { return __S.getUint32(0, this); }
    setQuestionId(value: number): void { __S.setUint32(0, value, this); }
    adoptDeprecatedObjectId(value: capnp.Orphan<capnp.Pointer>): void { __S.adopt(value, __S.getPointer(0, this)); }
    disownDeprecatedObjectId(): capnp.Orphan<capnp.Pointer> { return __S.disown(this.getDeprecatedObjectId()); }
    getDeprecatedObjectId(): capnp.Pointer { return __S.getPointer(0, this); }
    hasDeprecatedObjectId(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    setDeprecatedObjectId(value: capnp.Pointer): void { __S.copyFrom(value, __S.getPointer(0, this)); }
    toString(): string { return "Bootstrap_" + super.toString(); }
}
export enum Call_SendResultsTo_Which {
    CALLER = 0,
    YOURSELF = 1,
    THIRD_PARTY = 2
}
export class Call_SendResultsTo extends __S {
    static readonly CALLER = Call_SendResultsTo_Which.CALLER;
    static readonly YOURSELF = Call_SendResultsTo_Which.YOURSELF;
    static readonly THIRD_PARTY = Call_SendResultsTo_Which.THIRD_PARTY;
    static readonly _capnp = { displayName: "sendResultsTo", id: "dae8b0f61aab5f99", size: new __O(24, 3) };
    isCaller(): boolean { return __S.getUint16(6, this) === 0; }
    setCaller(): void { __S.setUint16(6, 0, this); }
    isYourself(): boolean { return __S.getUint16(6, this) === 1; }
    setYourself(): void { __S.setUint16(6, 1, this); }
    adoptThirdParty(value: capnp.Orphan<capnp.Pointer>): void {
        __S.setUint16(6, 2, this);
        __S.adopt(value, __S.getPointer(2, this));
    }
    disownThirdParty(): capnp.Orphan<capnp.Pointer> { return __S.disown(this.getThirdParty()); }
    getThirdParty(): capnp.Pointer {
        __S.testWhich("thirdParty", __S.getUint16(6, this), 2, this);
        return __S.getPointer(2, this);
    }
    hasThirdParty(): boolean { return !__S.isNull(__S.getPointer(2, this)); }
    isThirdParty(): boolean { return __S.getUint16(6, this) === 2; }
    setThirdParty(value: capnp.Pointer): void {
        __S.setUint16(6, 2, this);
        __S.copyFrom(value, __S.getPointer(2, this));
    }
    toString(): string { return "Call_SendResultsTo_" + super.toString(); }
    which(): Call_SendResultsTo_Which { return __S.getUint16(6, this); }
}
export class Call extends __S {
    static readonly _capnp = { displayName: "Call", id: "836a53ce789d4cd4", size: new __O(24, 3), defaultAllowThirdPartyTailCall: capnp.getBitMask(false, 0) };
    getQuestionId(): number { return __S.getUint32(0, this); }
    setQuestionId(value: number): void { __S.setUint32(0, value, this); }
    adoptTarget(value: capnp.Orphan<MessageTarget>): void { __S.adopt(value, __S.getPointer(0, this)); }
    disownTarget(): capnp.Orphan<MessageTarget> { return __S.disown(this.getTarget()); }
    getTarget(): MessageTarget { return __S.getStruct(0, MessageTarget, this); }
    hasTarget(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    initTarget(): MessageTarget { return __S.initStructAt(0, MessageTarget, this); }
    setTarget(value: MessageTarget): void { __S.copyFrom(value, __S.getPointer(0, this)); }
    getInterfaceId(): capnp.Uint64 { return __S.getUint64(8, this); }
    setInterfaceId(value: capnp.Uint64): void { __S.setUint64(8, value, this); }
    getMethodId(): number { return __S.getUint16(4, this); }
    setMethodId(value: number): void { __S.setUint16(4, value, this); }
    getAllowThirdPartyTailCall(): boolean { return __S.getBit(128, this, Call._capnp.defaultAllowThirdPartyTailCall); }
    setAllowThirdPartyTailCall(value: boolean): void { __S.setBit(128, value, this); }
    adoptParams(value: capnp.Orphan<Payload>): void { __S.adopt(value, __S.getPointer(1, this)); }
    disownParams(): capnp.Orphan<Payload> { return __S.disown(this.getParams()); }
    getParams(): Payload { return __S.getStruct(1, Payload, this); }
    hasParams(): boolean { return !__S.isNull(__S.getPointer(1, this)); }
    initParams(): Payload { return __S.initStructAt(1, Payload, this); }
    setParams(value: Payload): void { __S.copyFrom(value, __S.getPointer(1, this)); }
    getSendResultsTo(): Call_SendResultsTo { return __S.getAs(Call_SendResultsTo, this); }
    initSendResultsTo(): Call_SendResultsTo { return __S.getAs(Call_SendResultsTo, this); }
    toString(): string { return "Call_" + super.toString(); }
}
export enum Return_Which {
    RESULTS = 0,
    EXCEPTION = 1,
    CANCELED = 2,
    RESULTS_SENT_ELSEWHERE = 3,
    TAKE_FROM_OTHER_QUESTION = 4,
    ACCEPT_FROM_THIRD_PARTY = 5
}
export class Return extends __S {
    static readonly RESULTS = Return_Which.RESULTS;
    static readonly EXCEPTION = Return_Which.EXCEPTION;
    static readonly CANCELED = Return_Which.CANCELED;
    static readonly RESULTS_SENT_ELSEWHERE = Return_Which.RESULTS_SENT_ELSEWHERE;
    static readonly TAKE_FROM_OTHER_QUESTION = Return_Which.TAKE_FROM_OTHER_QUESTION;
    static readonly ACCEPT_FROM_THIRD_PARTY = Return_Which.ACCEPT_FROM_THIRD_PARTY;
    static readonly _capnp = { displayName: "Return", id: "9e19b28d3db3573a", size: new __O(16, 1), defaultReleaseParamCaps: capnp.getBitMask(true, 0) };
    getAnswerId(): number { return __S.getUint32(0, this); }
    setAnswerId(value: number): void { __S.setUint32(0, value, this); }
    getReleaseParamCaps(): boolean { return __S.getBit(32, this, Return._capnp.defaultReleaseParamCaps); }
    setReleaseParamCaps(value: boolean): void { __S.setBit(32, value, this); }
    adoptResults(value: capnp.Orphan<Payload>): void {
        __S.setUint16(6, 0, this);
        __S.adopt(value, __S.getPointer(0, this));
    }
    disownResults(): capnp.Orphan<Payload> { return __S.disown(this.getResults()); }
    getResults(): Payload {
        __S.testWhich("results", __S.getUint16(6, this), 0, this);
        return __S.getStruct(0, Payload, this);
    }
    hasResults(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    initResults(): Payload {
        __S.setUint16(6, 0, this);
        return __S.initStructAt(0, Payload, this);
    }
    isResults(): boolean { return __S.getUint16(6, this) === 0; }
    setResults(value: Payload): void {
        __S.setUint16(6, 0, this);
        __S.copyFrom(value, __S.getPointer(0, this));
    }
    adoptException(value: capnp.Orphan<Exception>): void {
        __S.setUint16(6, 1, this);
        __S.adopt(value, __S.getPointer(0, this));
    }
    disownException(): capnp.Orphan<Exception> { return __S.disown(this.getException()); }
    getException(): Exception {
        __S.testWhich("exception", __S.getUint16(6, this), 1, this);
        return __S.getStruct(0, Exception, this);
    }
    hasException(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    initException(): Exception {
        __S.setUint16(6, 1, this);
        return __S.initStructAt(0, Exception, this);
    }
    isException(): boolean { return __S.getUint16(6, this) === 1; }
    setException(value: Exception): void {
        __S.setUint16(6, 1, this);
        __S.copyFrom(value, __S.getPointer(0, this));
    }
    isCanceled(): boolean { return __S.getUint16(6, this) === 2; }
    setCanceled(): void { __S.setUint16(6, 2, this); }
    isResultsSentElsewhere(): boolean { return __S.getUint16(6, this) === 3; }
    setResultsSentElsewhere(): void { __S.setUint16(6, 3, this); }
    getTakeFromOtherQuestion(): number {
        __S.testWhich("takeFromOtherQuestion", __S.getUint16(6, this), 4, this);
        return __S.getUint32(8, this);
    }
    isTakeFromOtherQuestion(): boolean { return __S.getUint16(6, this) === 4; }
    setTakeFromOtherQuestion(value: number): void {
        __S.setUint16(6, 4, this);
        __S.setUint32(8, value, this);
    }
    adoptAcceptFromThirdParty(value: capnp.Orphan<capnp.Pointer>): void {
        __S.setUint16(6, 5, this);
        __S.adopt(value, __S.getPointer(0, this));
    }
    disownAcceptFromThirdParty(): capnp.Orphan<capnp.Pointer> { return __S.disown(this.getAcceptFromThirdParty()); }
    getAcceptFromThirdParty(): capnp.Pointer {
        __S.testWhich("acceptFromThirdParty", __S.getUint16(6, this), 5, this);
        return __S.getPointer(0, this);
    }
    hasAcceptFromThirdParty(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    isAcceptFromThirdParty(): boolean { return __S.getUint16(6, this) === 5; }
    setAcceptFromThirdParty(value: capnp.Pointer): void {
        __S.setUint16(6, 5, this);
        __S.copyFrom(value, __S.getPointer(0, this));
    }
    toString(): string { return "Return_" + super.toString(); }
    which(): Return_Which { return __S.getUint16(6, this); }
}
export class Finish extends __S {
    static readonly _capnp = { displayName: "Finish", id: "d37d2eb2c2f80e63", size: new __O(8, 0), defaultReleaseResultCaps: capnp.getBitMask(true, 0) };
    getQuestionId(): number { return __S.getUint32(0, this); }
    setQuestionId(value: number): void { __S.setUint32(0, value, this); }
    getReleaseResultCaps(): boolean { return __S.getBit(32, this, Finish._capnp.defaultReleaseResultCaps); }
    setReleaseResultCaps(value: boolean): void { __S.setBit(32, value, this); }
    toString(): string { return "Finish_" + super.toString(); }
}
export enum Resolve_Which {
    CAP = 0,
    EXCEPTION = 1
}
export class Resolve extends __S {
    static readonly CAP = Resolve_Which.CAP;
    static readonly EXCEPTION = Resolve_Which.EXCEPTION;
    static readonly _capnp = { displayName: "Resolve", id: "bbc29655fa89086e", size: new __O(8, 1) };
    getPromiseId(): number { return __S.getUint32(0, this); }
    setPromiseId(value: number): void { __S.setUint32(0, value, this); }
    adoptCap(value: capnp.Orphan<CapDescriptor>): void {
        __S.setUint16(4, 0, this);
        __S.adopt(value, __S.getPointer(0, this));
    }
    disownCap(): capnp.Orphan<CapDescriptor> { return __S.disown(this.getCap()); }
    getCap(): CapDescriptor {
        __S.testWhich("cap", __S.getUint16(4, this), 0, this);
        return __S.getStruct(0, CapDescriptor, this);
    }
    hasCap(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    initCap(): CapDescriptor {
        __S.setUint16(4, 0, this);
        return __S.initStructAt(0, CapDescriptor, this);
    }
    isCap(): boolean { return __S.getUint16(4, this) === 0; }
    setCap(value: CapDescriptor): void {
        __S.setUint16(4, 0, this);
        __S.copyFrom(value, __S.getPointer(0, this));
    }
    adoptException(value: capnp.Orphan<Exception>): void {
        __S.setUint16(4, 1, this);
        __S.adopt(value, __S.getPointer(0, this));
    }
    disownException(): capnp.Orphan<Exception> { return __S.disown(this.getException()); }
    getException(): Exception {
        __S.testWhich("exception", __S.getUint16(4, this), 1, this);
        return __S.getStruct(0, Exception, this);
    }
    hasException(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    initException(): Exception {
        __S.setUint16(4, 1, this);
        return __S.initStructAt(0, Exception, this);
    }
    isException(): boolean { return __S.getUint16(4, this) === 1; }
    setException(value: Exception): void {
        __S.setUint16(4, 1, this);
        __S.copyFrom(value, __S.getPointer(0, this));
    }
    toString(): string { return "Resolve_" + super.toString(); }
    which(): Resolve_Which { return __S.getUint16(4, this); }
}
export class Release extends __S {
    static readonly _capnp = { displayName: "Release", id: "ad1a6c0d7dd07497", size: new __O(8, 0) };
    getId(): number { return __S.getUint32(0, this); }
    setId(value: number): void { __S.setUint32(0, value, this); }
    getReferenceCount(): number { return __S.getUint32(4, this); }
    setReferenceCount(value: number): void { __S.setUint32(4, value, this); }
    toString(): string { return "Release_" + super.toString(); }
}
export enum Disembargo_Context_Which {
    SENDER_LOOPBACK = 0,
    RECEIVER_LOOPBACK = 1,
    ACCEPT = 2,
    PROVIDE = 3
}
export class Disembargo_Context extends __S {
    static readonly SENDER_LOOPBACK = Disembargo_Context_Which.SENDER_LOOPBACK;
    static readonly RECEIVER_LOOPBACK = Disembargo_Context_Which.RECEIVER_LOOPBACK;
    static readonly ACCEPT = Disembargo_Context_Which.ACCEPT;
    static readonly PROVIDE = Disembargo_Context_Which.PROVIDE;
    static readonly _capnp = { displayName: "context", id: "d562b4df655bdd4d", size: new __O(8, 1) };
    getSenderLoopback(): number {
        __S.testWhich("senderLoopback", __S.getUint16(4, this), 0, this);
        return __S.getUint32(0, this);
    }
    isSenderLoopback(): boolean { return __S.getUint16(4, this) === 0; }
    setSenderLoopback(value: number): void {
        __S.setUint16(4, 0, this);
        __S.setUint32(0, value, this);
    }
    getReceiverLoopback(): number {
        __S.testWhich("receiverLoopback", __S.getUint16(4, this), 1, this);
        return __S.getUint32(0, this);
    }
    isReceiverLoopback(): boolean { return __S.getUint16(4, this) === 1; }
    setReceiverLoopback(value: number): void {
        __S.setUint16(4, 1, this);
        __S.setUint32(0, value, this);
    }
    isAccept(): boolean { return __S.getUint16(4, this) === 2; }
    setAccept(): void { __S.setUint16(4, 2, this); }
    getProvide(): number {
        __S.testWhich("provide", __S.getUint16(4, this), 3, this);
        return __S.getUint32(0, this);
    }
    isProvide(): boolean { return __S.getUint16(4, this) === 3; }
    setProvide(value: number): void {
        __S.setUint16(4, 3, this);
        __S.setUint32(0, value, this);
    }
    toString(): string { return "Disembargo_Context_" + super.toString(); }
    which(): Disembargo_Context_Which { return __S.getUint16(4, this); }
}
export class Disembargo extends __S {
    static readonly _capnp = { displayName: "Disembargo", id: "f964368b0fbd3711", size: new __O(8, 1) };
    adoptTarget(value: capnp.Orphan<MessageTarget>): void { __S.adopt(value, __S.getPointer(0, this)); }
    disownTarget(): capnp.Orphan<MessageTarget> { return __S.disown(this.getTarget()); }
    getTarget(): MessageTarget { return __S.getStruct(0, MessageTarget, this); }
    hasTarget(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    initTarget(): MessageTarget { return __S.initStructAt(0, MessageTarget, this); }
    setTarget(value: MessageTarget): void { __S.copyFrom(value, __S.getPointer(0, this)); }
    getContext(): Disembargo_Context { return __S.getAs(Disembargo_Context, this); }
    initContext(): Disembargo_Context { return __S.getAs(Disembargo_Context, this); }
    toString(): string { return "Disembargo_" + super.toString(); }
}
export class Provide extends __S {
    static readonly _capnp = { displayName: "Provide", id: "9c6a046bfbc1ac5a", size: new __O(8, 2) };
    getQuestionId(): number { return __S.getUint32(0, this); }
    setQuestionId(value: number): void { __S.setUint32(0, value, this); }
    adoptTarget(value: capnp.Orphan<MessageTarget>): void { __S.adopt(value, __S.getPointer(0, this)); }
    disownTarget(): capnp.Orphan<MessageTarget> { return __S.disown(this.getTarget()); }
    getTarget(): MessageTarget { return __S.getStruct(0, MessageTarget, this); }
    hasTarget(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    initTarget(): MessageTarget { return __S.initStructAt(0, MessageTarget, this); }
    setTarget(value: MessageTarget): void { __S.copyFrom(value, __S.getPointer(0, this)); }
    adoptRecipient(value: capnp.Orphan<capnp.Pointer>): void { __S.adopt(value, __S.getPointer(1, this)); }
    disownRecipient(): capnp.Orphan<capnp.Pointer> { return __S.disown(this.getRecipient()); }
    getRecipient(): capnp.Pointer { return __S.getPointer(1, this); }
    hasRecipient(): boolean { return !__S.isNull(__S.getPointer(1, this)); }
    setRecipient(value: capnp.Pointer): void { __S.copyFrom(value, __S.getPointer(1, this)); }
    toString(): string { return "Provide_" + super.toString(); }
}
export class Accept extends __S {
    static readonly _capnp = { displayName: "Accept", id: "d4c9b56290554016", size: new __O(8, 1) };
    getQuestionId(): number { return __S.getUint32(0, this); }
    setQuestionId(value: number): void { __S.setUint32(0, value, this); }
    adoptProvision(value: capnp.Orphan<capnp.Pointer>): void { __S.adopt(value, __S.getPointer(0, this)); }
    disownProvision(): capnp.Orphan<capnp.Pointer> { return __S.disown(this.getProvision()); }
    getProvision(): capnp.Pointer { return __S.getPointer(0, this); }
    hasProvision(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    setProvision(value: capnp.Pointer): void { __S.copyFrom(value, __S.getPointer(0, this)); }
    getEmbargo(): boolean { return __S.getBit(32, this); }
    setEmbargo(value: boolean): void { __S.setBit(32, value, this); }
    toString(): string { return "Accept_" + super.toString(); }
}
export class Join extends __S {
    static readonly _capnp = { displayName: "Join", id: "fbe1980490e001af", size: new __O(8, 2) };
    getQuestionId(): number { return __S.getUint32(0, this); }
    setQuestionId(value: number): void { __S.setUint32(0, value, this); }
    adoptTarget(value: capnp.Orphan<MessageTarget>): void { __S.adopt(value, __S.getPointer(0, this)); }
    disownTarget(): capnp.Orphan<MessageTarget> { return __S.disown(this.getTarget()); }
    getTarget(): MessageTarget { return __S.getStruct(0, MessageTarget, this); }
    hasTarget(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    initTarget(): MessageTarget { return __S.initStructAt(0, MessageTarget, this); }
    setTarget(value: MessageTarget): void { __S.copyFrom(value, __S.getPointer(0, this)); }
    adoptKeyPart(value: capnp.Orphan<capnp.Pointer>): void { __S.adopt(value, __S.getPointer(1, this)); }
    disownKeyPart(): capnp.Orphan<capnp.Pointer> { return __S.disown(this.getKeyPart()); }
    getKeyPart(): capnp.Pointer { return __S.getPointer(1, this); }
    hasKeyPart(): boolean { return !__S.isNull(__S.getPointer(1, this)); }
    setKeyPart(value: capnp.Pointer): void { __S.copyFrom(value, __S.getPointer(1, this)); }
    toString(): string { return "Join_" + super.toString(); }
}
export enum MessageTarget_Which {
    IMPORTED_CAP = 0,
    PROMISED_ANSWER = 1
}
export class MessageTarget extends __S {
    static readonly IMPORTED_CAP = MessageTarget_Which.IMPORTED_CAP;
    static readonly PROMISED_ANSWER = MessageTarget_Which.PROMISED_ANSWER;
    static readonly _capnp = { displayName: "MessageTarget", id: "95bc14545813fbc1", size: new __O(8, 1) };
    getImportedCap(): number {
        __S.testWhich("importedCap", __S.getUint16(4, this), 0, this);
        return __S.getUint32(0, this);
    }
    isImportedCap(): boolean { return __S.getUint16(4, this) === 0; }
    setImportedCap(value: number): void {
        __S.setUint16(4, 0, this);
        __S.setUint32(0, value, this);
    }
    adoptPromisedAnswer(value: capnp.Orphan<PromisedAnswer>): void {
        __S.setUint16(4, 1, this);
        __S.adopt(value, __S.getPointer(0, this));
    }
    disownPromisedAnswer(): capnp.Orphan<PromisedAnswer> { return __S.disown(this.getPromisedAnswer()); }
    getPromisedAnswer(): PromisedAnswer {
        __S.testWhich("promisedAnswer", __S.getUint16(4, this), 1, this);
        return __S.getStruct(0, PromisedAnswer, this);
    }
    hasPromisedAnswer(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    initPromisedAnswer(): PromisedAnswer {
        __S.setUint16(4, 1, this);
        return __S.initStructAt(0, PromisedAnswer, this);
    }
    isPromisedAnswer(): boolean { return __S.getUint16(4, this) === 1; }
    setPromisedAnswer(value: PromisedAnswer): void {
        __S.setUint16(4, 1, this);
        __S.copyFrom(value, __S.getPointer(0, this));
    }
    toString(): string { return "MessageTarget_" + super.toString(); }
    which(): MessageTarget_Which { return __S.getUint16(4, this); }
}
export class Payload extends __S {
    static readonly _capnp = { displayName: "Payload", id: "9a0e61223d96743b", size: new __O(0, 2) };
    static _CapTable: capnp.ListCtor<CapDescriptor>;
    adoptContent(value: capnp.Orphan<capnp.Pointer>): void { __S.adopt(value, __S.getPointer(0, this)); }
    disownContent(): capnp.Orphan<capnp.Pointer> { return __S.disown(this.getContent()); }
    getContent(): capnp.Pointer { return __S.getPointer(0, this); }
    hasContent(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    setContent(value: capnp.Pointer): void { __S.copyFrom(value, __S.getPointer(0, this)); }
    adoptCapTable(value: capnp.Orphan<capnp.List<CapDescriptor>>): void { __S.adopt(value, __S.getPointer(1, this)); }
    disownCapTable(): capnp.Orphan<capnp.List<CapDescriptor>> { return __S.disown(this.getCapTable()); }
    getCapTable(): capnp.List<CapDescriptor> { return __S.getList(1, Payload._CapTable, this); }
    hasCapTable(): boolean { return !__S.isNull(__S.getPointer(1, this)); }
    initCapTable(length: number): capnp.List<CapDescriptor> { return __S.initList(1, Payload._CapTable, length, this); }
    setCapTable(value: capnp.List<CapDescriptor>): void { __S.copyFrom(value, __S.getPointer(1, this)); }
    toString(): string { return "Payload_" + super.toString(); }
}
export enum CapDescriptor_Which {
    NONE = 0,
    SENDER_HOSTED = 1,
    SENDER_PROMISE = 2,
    RECEIVER_HOSTED = 3,
    RECEIVER_ANSWER = 4,
    THIRD_PARTY_HOSTED = 5
}
export class CapDescriptor extends __S {
    static readonly NONE = CapDescriptor_Which.NONE;
    static readonly SENDER_HOSTED = CapDescriptor_Which.SENDER_HOSTED;
    static readonly SENDER_PROMISE = CapDescriptor_Which.SENDER_PROMISE;
    static readonly RECEIVER_HOSTED = CapDescriptor_Which.RECEIVER_HOSTED;
    static readonly RECEIVER_ANSWER = CapDescriptor_Which.RECEIVER_ANSWER;
    static readonly THIRD_PARTY_HOSTED = CapDescriptor_Which.THIRD_PARTY_HOSTED;
    static readonly _capnp = { displayName: "CapDescriptor", id: "8523ddc40b86b8b0", size: new __O(8, 1) };
    isNone(): boolean { return __S.getUint16(0, this) === 0; }
    setNone(): void { __S.setUint16(0, 0, this); }
    getSenderHosted(): number {
        __S.testWhich("senderHosted", __S.getUint16(0, this), 1, this);
        return __S.getUint32(4, this);
    }
    isSenderHosted(): boolean { return __S.getUint16(0, this) === 1; }
    setSenderHosted(value: number): void {
        __S.setUint16(0, 1, this);
        __S.setUint32(4, value, this);
    }
    getSenderPromise(): number {
        __S.testWhich("senderPromise", __S.getUint16(0, this), 2, this);
        return __S.getUint32(4, this);
    }
    isSenderPromise(): boolean { return __S.getUint16(0, this) === 2; }
    setSenderPromise(value: number): void {
        __S.setUint16(0, 2, this);
        __S.setUint32(4, value, this);
    }
    getReceiverHosted(): number {
        __S.testWhich("receiverHosted", __S.getUint16(0, this), 3, this);
        return __S.getUint32(4, this);
    }
    isReceiverHosted(): boolean { return __S.getUint16(0, this) === 3; }
    setReceiverHosted(value: number): void {
        __S.setUint16(0, 3, this);
        __S.setUint32(4, value, this);
    }
    adoptReceiverAnswer(value: capnp.Orphan<PromisedAnswer>): void {
        __S.setUint16(0, 4, this);
        __S.adopt(value, __S.getPointer(0, this));
    }
    disownReceiverAnswer(): capnp.Orphan<PromisedAnswer> { return __S.disown(this.getReceiverAnswer()); }
    getReceiverAnswer(): PromisedAnswer {
        __S.testWhich("receiverAnswer", __S.getUint16(0, this), 4, this);
        return __S.getStruct(0, PromisedAnswer, this);
    }
    hasReceiverAnswer(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    initReceiverAnswer(): PromisedAnswer {
        __S.setUint16(0, 4, this);
        return __S.initStructAt(0, PromisedAnswer, this);
    }
    isReceiverAnswer(): boolean { return __S.getUint16(0, this) === 4; }
    setReceiverAnswer(value: PromisedAnswer): void {
        __S.setUint16(0, 4, this);
        __S.copyFrom(value, __S.getPointer(0, this));
    }
    adoptThirdPartyHosted(value: capnp.Orphan<ThirdPartyCapDescriptor>): void {
        __S.setUint16(0, 5, this);
        __S.adopt(value, __S.getPointer(0, this));
    }
    disownThirdPartyHosted(): capnp.Orphan<ThirdPartyCapDescriptor> { return __S.disown(this.getThirdPartyHosted()); }
    getThirdPartyHosted(): ThirdPartyCapDescriptor {
        __S.testWhich("thirdPartyHosted", __S.getUint16(0, this), 5, this);
        return __S.getStruct(0, ThirdPartyCapDescriptor, this);
    }
    hasThirdPartyHosted(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    initThirdPartyHosted(): ThirdPartyCapDescriptor {
        __S.setUint16(0, 5, this);
        return __S.initStructAt(0, ThirdPartyCapDescriptor, this);
    }
    isThirdPartyHosted(): boolean { return __S.getUint16(0, this) === 5; }
    setThirdPartyHosted(value: ThirdPartyCapDescriptor): void {
        __S.setUint16(0, 5, this);
        __S.copyFrom(value, __S.getPointer(0, this));
    }
    toString(): string { return "CapDescriptor_" + super.toString(); }
    which(): CapDescriptor_Which { return __S.getUint16(0, this); }
}
export enum PromisedAnswer_Op_Which {
    NOOP = 0,
    GET_POINTER_FIELD = 1
}
export class PromisedAnswer_Op extends __S {
    static readonly NOOP = PromisedAnswer_Op_Which.NOOP;
    static readonly GET_POINTER_FIELD = PromisedAnswer_Op_Which.GET_POINTER_FIELD;
    static readonly _capnp = { displayName: "Op", id: "f316944415569081", size: new __O(8, 0) };
    isNoop(): boolean { return __S.getUint16(0, this) === 0; }
    setNoop(): void { __S.setUint16(0, 0, this); }
    getGetPointerField(): number {
        __S.testWhich("getPointerField", __S.getUint16(0, this), 1, this);
        return __S.getUint16(2, this);
    }
    isGetPointerField(): boolean { return __S.getUint16(0, this) === 1; }
    setGetPointerField(value: number): void {
        __S.setUint16(0, 1, this);
        __S.setUint16(2, value, this);
    }
    toString(): string { return "PromisedAnswer_Op_" + super.toString(); }
    which(): PromisedAnswer_Op_Which { return __S.getUint16(0, this); }
}
export class PromisedAnswer extends __S {
    static readonly Op = PromisedAnswer_Op;
    static readonly _capnp = { displayName: "PromisedAnswer", id: "d800b1d6cd6f1ca0", size: new __O(8, 1) };
    static _Transform: capnp.ListCtor<PromisedAnswer_Op>;
    getQuestionId(): number { return __S.getUint32(0, this); }
    setQuestionId(value: number): void { __S.setUint32(0, value, this); }
    adoptTransform(value: capnp.Orphan<capnp.List<PromisedAnswer_Op>>): void { __S.adopt(value, __S.getPointer(0, this)); }
    disownTransform(): capnp.Orphan<capnp.List<PromisedAnswer_Op>> { return __S.disown(this.getTransform()); }
    getTransform(): capnp.List<PromisedAnswer_Op> { return __S.getList(0, PromisedAnswer._Transform, this); }
    hasTransform(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    initTransform(length: number): capnp.List<PromisedAnswer_Op> { return __S.initList(0, PromisedAnswer._Transform, length, this); }
    setTransform(value: capnp.List<PromisedAnswer_Op>): void { __S.copyFrom(value, __S.getPointer(0, this)); }
    toString(): string { return "PromisedAnswer_" + super.toString(); }
}
export class ThirdPartyCapDescriptor extends __S {
    static readonly _capnp = { displayName: "ThirdPartyCapDescriptor", id: "d37007fde1f0027d", size: new __O(8, 1) };
    adoptId(value: capnp.Orphan<capnp.Pointer>): void { __S.adopt(value, __S.getPointer(0, this)); }
    disownId(): capnp.Orphan<capnp.Pointer> { return __S.disown(this.getId()); }
    getId(): capnp.Pointer { return __S.getPointer(0, this); }
    hasId(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    setId(value: capnp.Pointer): void { __S.copyFrom(value, __S.getPointer(0, this)); }
    getVineId(): number { return __S.getUint32(0, this); }
    setVineId(value: number): void { __S.setUint32(0, value, this); }
    toString(): string { return "ThirdPartyCapDescriptor_" + super.toString(); }
}
export enum Exception_Type {
    FAILED,
    OVERLOADED,
    DISCONNECTED,
    UNIMPLEMENTED
}
export class Exception extends __S {
    static readonly Type = Exception_Type;
    static readonly _capnp = { displayName: "Exception", id: "d625b7063acf691a", size: new __O(8, 1) };
    getReason(): string { return __S.getText(0, this); }
    setReason(value: string): void { __S.setText(0, value, this); }
    getType(): Exception_Type { return __S.getUint16(4, this); }
    setType(value: Exception_Type): void { __S.setUint16(4, value, this); }
    getObsoleteIsCallersFault(): boolean { return __S.getBit(0, this); }
    setObsoleteIsCallersFault(value: boolean): void { __S.setBit(0, value, this); }
    getObsoleteDurability(): number { return __S.getUint16(2, this); }
    setObsoleteDurability(value: number): void { __S.setUint16(2, value, this); }
    toString(): string { return "Exception_" + super.toString(); }
}
Payload._CapTable = capnp.CompositeList(CapDescriptor);
PromisedAnswer._Transform = capnp.CompositeList(PromisedAnswer_Op);
