{"version": 3, "file": "persistent.capnp.js", "sourceRoot": "", "sources": ["persistent.capnp.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAGH,oCAA4D;AAC/C,QAAA,YAAY,GAAG,kBAAkB,CAAC;AAC/C,MAAa,qBAAsB,SAAQ,cAAG;IAE1C,YAAY,CAAC,KAAkC,IAAU,cAAG,CAAC,KAAK,CAAC,KAAK,EAAE,cAAG,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACrG,aAAa,KAAkC,OAAO,cAAG,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC;IACtF,UAAU,KAAoB,OAAO,cAAG,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IAC/D,UAAU,KAAc,OAAO,CAAC,cAAG,CAAC,MAAM,CAAC,cAAG,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACtE,UAAU,CAAC,KAAoB,IAAU,cAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,cAAG,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACxF,QAAQ,KAAa,OAAO,wBAAwB,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;;AAP9E,sDAQC;AAPmB,4BAAM,GAAG,EAAE,WAAW,EAAE,YAAY,EAAE,EAAE,EAAE,kBAAkB,EAAE,IAAI,EAAE,IAAI,kBAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;AAQxG,MAAa,sBAAuB,SAAQ,cAAG;IAE3C,cAAc,CAAC,KAAkC,IAAU,cAAG,CAAC,KAAK,CAAC,KAAK,EAAE,cAAG,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACvG,eAAe,KAAkC,OAAO,cAAG,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,CAAC;IAC1F,YAAY,KAAoB,OAAO,cAAG,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IACjE,YAAY,KAAc,OAAO,CAAC,cAAG,CAAC,MAAM,CAAC,cAAG,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACxE,YAAY,CAAC,KAAoB,IAAU,cAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,cAAG,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1F,QAAQ,KAAa,OAAO,yBAAyB,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;;AAP/E,wDAQC;AAPmB,6BAAM,GAAG,EAAE,WAAW,EAAE,aAAa,EAAE,EAAE,EAAE,kBAAkB,EAAE,IAAI,EAAE,IAAI,kBAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;AAQzG,MAAa,UAAW,SAAQ,cAAG;IAI/B,QAAQ,KAAa,OAAO,aAAa,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;;AAJnE,gCAKC;AAJmB,qBAAU,GAAG,qBAAqB,CAAC;AACnC,sBAAW,GAAG,sBAAsB,CAAC;AACrC,iBAAM,GAAG,EAAE,WAAW,EAAE,YAAY,EAAE,EAAE,EAAE,kBAAkB,EAAE,IAAI,EAAE,IAAI,kBAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;AAGxG,MAAa,YAAa,SAAQ,cAAG;IAEjC,QAAQ,KAAa,OAAO,eAAe,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;;AAFrE,oCAGC;AAFmB,mBAAM,GAAG,EAAE,WAAW,EAAE,cAAc,EAAE,EAAE,EAAE,kBAAkB,EAAE,IAAI,EAAE,IAAI,kBAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC"}