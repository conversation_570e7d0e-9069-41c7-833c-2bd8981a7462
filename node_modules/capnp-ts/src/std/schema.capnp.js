"use strict";
/**
 * This file is generated by hand in order to bootstrap compiler development. It is intended to be an exact match to
 * compiled output.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.CodeGeneratorRequest = exports.CodeGeneratorRequest_RequestedFile = exports.CodeGeneratorRequest_RequestedFile_Import = exports.CapnpVersion = exports.ElementSize = exports.Annotation = exports.Value = exports.Value_Which = exports.Brand = exports.Brand_Binding = exports.Brand_Binding_Which = exports.Brand_Scope = exports.Brand_Scope_Which = exports.Type = exports.Type_Which = exports.Type_AnyPointer = exports.Type_AnyPointer_Which = exports.Type_AnyPointer_ImplicitMethodParameter = exports.Type_AnyPointer_Parameter = exports.Type_AnyPointer_Unconstrained = exports.Type_AnyPointer_Unconstrained_Which = exports.Type_Interface = exports.Type_Struct = exports.Type_Enum = exports.Type_List = exports.Method = exports.Superclass = exports.Enumerant = exports.Field = exports.Field_Which = exports.Field_Ordinal = exports.Field_Ordinal_Which = exports.Field_Group = exports.Field_Slot = exports.Node = exports.Node_Which = exports.Node_Annotation = exports.Node_Const = exports.Node_Interface = exports.Node_Enum = exports.Node_Struct = exports.Node_NestedNode = exports.Node_Parameter = exports._capnpFileId = void 0;
const tslib_1 = require("tslib");
const capnp = tslib_1.__importStar(require("../index"));
const index_1 = require("../index");
exports._capnpFileId = "a93fc509624c72d9";
class Node_Parameter extends index_1.Struct {
    getName() { return index_1.Struct.getText(0, this); }
    setName(value) { index_1.Struct.setText(0, value, this); }
    toString() { return "Node_Parameter_" + super.toString(); }
}
exports.Node_Parameter = Node_Parameter;
Node_Parameter._capnp = { displayName: "Parameter", id: "b9521bccf10fa3b1", size: new index_1.ObjectSize(0, 1) };
class Node_NestedNode extends index_1.Struct {
    getName() { return index_1.Struct.getText(0, this); }
    setName(value) { index_1.Struct.setText(0, value, this); }
    getId() { return index_1.Struct.getUint64(0, this); }
    setId(value) { index_1.Struct.setUint64(0, value, this); }
    toString() { return "Node_NestedNode_" + super.toString(); }
}
exports.Node_NestedNode = Node_NestedNode;
Node_NestedNode._capnp = { displayName: "NestedNode", id: "debf55bbfa0fc242", size: new index_1.ObjectSize(8, 1) };
class Node_Struct extends index_1.Struct {
    getDataWordCount() { return index_1.Struct.getUint16(14, this); }
    setDataWordCount(value) { index_1.Struct.setUint16(14, value, this); }
    getPointerCount() { return index_1.Struct.getUint16(24, this); }
    setPointerCount(value) { index_1.Struct.setUint16(24, value, this); }
    getPreferredListEncoding() { return index_1.Struct.getUint16(26, this); }
    setPreferredListEncoding(value) { index_1.Struct.setUint16(26, value, this); }
    getIsGroup() { return index_1.Struct.getBit(224, this); }
    setIsGroup(value) { index_1.Struct.setBit(224, value, this); }
    getDiscriminantCount() { return index_1.Struct.getUint16(30, this); }
    setDiscriminantCount(value) { index_1.Struct.setUint16(30, value, this); }
    getDiscriminantOffset() { return index_1.Struct.getUint32(32, this); }
    setDiscriminantOffset(value) { index_1.Struct.setUint32(32, value, this); }
    adoptFields(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(3, this)); }
    disownFields() { return index_1.Struct.disown(this.getFields()); }
    getFields() { return index_1.Struct.getList(3, Node_Struct._Fields, this); }
    hasFields() { return !index_1.Struct.isNull(index_1.Struct.getPointer(3, this)); }
    initFields(length) { return index_1.Struct.initList(3, Node_Struct._Fields, length, this); }
    setFields(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(3, this)); }
    toString() { return "Node_Struct_" + super.toString(); }
}
exports.Node_Struct = Node_Struct;
Node_Struct._capnp = { displayName: "struct", id: "9ea0b19b37fb4435", size: new index_1.ObjectSize(40, 6) };
class Node_Enum extends index_1.Struct {
    adoptEnumerants(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(3, this)); }
    disownEnumerants() { return index_1.Struct.disown(this.getEnumerants()); }
    getEnumerants() { return index_1.Struct.getList(3, Node_Enum._Enumerants, this); }
    hasEnumerants() { return !index_1.Struct.isNull(index_1.Struct.getPointer(3, this)); }
    initEnumerants(length) { return index_1.Struct.initList(3, Node_Enum._Enumerants, length, this); }
    setEnumerants(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(3, this)); }
    toString() { return "Node_Enum_" + super.toString(); }
}
exports.Node_Enum = Node_Enum;
Node_Enum._capnp = { displayName: "enum", id: "b54ab3364333f598", size: new index_1.ObjectSize(40, 6) };
class Node_Interface extends index_1.Struct {
    adoptMethods(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(3, this)); }
    disownMethods() { return index_1.Struct.disown(this.getMethods()); }
    getMethods() { return index_1.Struct.getList(3, Node_Interface._Methods, this); }
    hasMethods() { return !index_1.Struct.isNull(index_1.Struct.getPointer(3, this)); }
    initMethods(length) { return index_1.Struct.initList(3, Node_Interface._Methods, length, this); }
    setMethods(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(3, this)); }
    adoptSuperclasses(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(4, this)); }
    disownSuperclasses() { return index_1.Struct.disown(this.getSuperclasses()); }
    getSuperclasses() { return index_1.Struct.getList(4, Node_Interface._Superclasses, this); }
    hasSuperclasses() { return !index_1.Struct.isNull(index_1.Struct.getPointer(4, this)); }
    initSuperclasses(length) { return index_1.Struct.initList(4, Node_Interface._Superclasses, length, this); }
    setSuperclasses(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(4, this)); }
    toString() { return "Node_Interface_" + super.toString(); }
}
exports.Node_Interface = Node_Interface;
Node_Interface._capnp = { displayName: "interface", id: "e82753cff0c2218f", size: new index_1.ObjectSize(40, 6) };
class Node_Const extends index_1.Struct {
    adoptType(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(3, this)); }
    disownType() { return index_1.Struct.disown(this.getType()); }
    getType() { return index_1.Struct.getStruct(3, Type, this); }
    hasType() { return !index_1.Struct.isNull(index_1.Struct.getPointer(3, this)); }
    initType() { return index_1.Struct.initStructAt(3, Type, this); }
    setType(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(3, this)); }
    adoptValue(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(4, this)); }
    disownValue() { return index_1.Struct.disown(this.getValue()); }
    getValue() { return index_1.Struct.getStruct(4, Value, this); }
    hasValue() { return !index_1.Struct.isNull(index_1.Struct.getPointer(4, this)); }
    initValue() { return index_1.Struct.initStructAt(4, Value, this); }
    setValue(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(4, this)); }
    toString() { return "Node_Const_" + super.toString(); }
}
exports.Node_Const = Node_Const;
Node_Const._capnp = { displayName: "const", id: "b18aa5ac7a0d9420", size: new index_1.ObjectSize(40, 6) };
class Node_Annotation extends index_1.Struct {
    adoptType(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(3, this)); }
    disownType() { return index_1.Struct.disown(this.getType()); }
    getType() { return index_1.Struct.getStruct(3, Type, this); }
    hasType() { return !index_1.Struct.isNull(index_1.Struct.getPointer(3, this)); }
    initType() { return index_1.Struct.initStructAt(3, Type, this); }
    setType(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(3, this)); }
    getTargetsFile() { return index_1.Struct.getBit(112, this); }
    setTargetsFile(value) { index_1.Struct.setBit(112, value, this); }
    getTargetsConst() { return index_1.Struct.getBit(113, this); }
    setTargetsConst(value) { index_1.Struct.setBit(113, value, this); }
    getTargetsEnum() { return index_1.Struct.getBit(114, this); }
    setTargetsEnum(value) { index_1.Struct.setBit(114, value, this); }
    getTargetsEnumerant() { return index_1.Struct.getBit(115, this); }
    setTargetsEnumerant(value) { index_1.Struct.setBit(115, value, this); }
    getTargetsStruct() { return index_1.Struct.getBit(116, this); }
    setTargetsStruct(value) { index_1.Struct.setBit(116, value, this); }
    getTargetsField() { return index_1.Struct.getBit(117, this); }
    setTargetsField(value) { index_1.Struct.setBit(117, value, this); }
    getTargetsUnion() { return index_1.Struct.getBit(118, this); }
    setTargetsUnion(value) { index_1.Struct.setBit(118, value, this); }
    getTargetsGroup() { return index_1.Struct.getBit(119, this); }
    setTargetsGroup(value) { index_1.Struct.setBit(119, value, this); }
    getTargetsInterface() { return index_1.Struct.getBit(120, this); }
    setTargetsInterface(value) { index_1.Struct.setBit(120, value, this); }
    getTargetsMethod() { return index_1.Struct.getBit(121, this); }
    setTargetsMethod(value) { index_1.Struct.setBit(121, value, this); }
    getTargetsParam() { return index_1.Struct.getBit(122, this); }
    setTargetsParam(value) { index_1.Struct.setBit(122, value, this); }
    getTargetsAnnotation() { return index_1.Struct.getBit(123, this); }
    setTargetsAnnotation(value) { index_1.Struct.setBit(123, value, this); }
    toString() { return "Node_Annotation_" + super.toString(); }
}
exports.Node_Annotation = Node_Annotation;
Node_Annotation._capnp = { displayName: "annotation", id: "ec1619d4400a0290", size: new index_1.ObjectSize(40, 6) };
var Node_Which;
(function (Node_Which) {
    Node_Which[Node_Which["FILE"] = 0] = "FILE";
    Node_Which[Node_Which["STRUCT"] = 1] = "STRUCT";
    Node_Which[Node_Which["ENUM"] = 2] = "ENUM";
    Node_Which[Node_Which["INTERFACE"] = 3] = "INTERFACE";
    Node_Which[Node_Which["CONST"] = 4] = "CONST";
    Node_Which[Node_Which["ANNOTATION"] = 5] = "ANNOTATION";
})(Node_Which = exports.Node_Which || (exports.Node_Which = {}));
class Node extends index_1.Struct {
    getId() { return index_1.Struct.getUint64(0, this); }
    setId(value) { index_1.Struct.setUint64(0, value, this); }
    getDisplayName() { return index_1.Struct.getText(0, this); }
    setDisplayName(value) { index_1.Struct.setText(0, value, this); }
    getDisplayNamePrefixLength() { return index_1.Struct.getUint32(8, this); }
    setDisplayNamePrefixLength(value) { index_1.Struct.setUint32(8, value, this); }
    getScopeId() { return index_1.Struct.getUint64(16, this); }
    setScopeId(value) { index_1.Struct.setUint64(16, value, this); }
    adoptParameters(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(5, this)); }
    disownParameters() { return index_1.Struct.disown(this.getParameters()); }
    getParameters() { return index_1.Struct.getList(5, Node._Parameters, this); }
    hasParameters() { return !index_1.Struct.isNull(index_1.Struct.getPointer(5, this)); }
    initParameters(length) { return index_1.Struct.initList(5, Node._Parameters, length, this); }
    setParameters(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(5, this)); }
    getIsGeneric() { return index_1.Struct.getBit(288, this); }
    setIsGeneric(value) { index_1.Struct.setBit(288, value, this); }
    adoptNestedNodes(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(1, this)); }
    disownNestedNodes() { return index_1.Struct.disown(this.getNestedNodes()); }
    getNestedNodes() { return index_1.Struct.getList(1, Node._NestedNodes, this); }
    hasNestedNodes() { return !index_1.Struct.isNull(index_1.Struct.getPointer(1, this)); }
    initNestedNodes(length) { return index_1.Struct.initList(1, Node._NestedNodes, length, this); }
    setNestedNodes(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(1, this)); }
    adoptAnnotations(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(2, this)); }
    disownAnnotations() { return index_1.Struct.disown(this.getAnnotations()); }
    getAnnotations() { return index_1.Struct.getList(2, Node._Annotations, this); }
    hasAnnotations() { return !index_1.Struct.isNull(index_1.Struct.getPointer(2, this)); }
    initAnnotations(length) { return index_1.Struct.initList(2, Node._Annotations, length, this); }
    setAnnotations(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(2, this)); }
    isFile() { return index_1.Struct.getUint16(12, this) === 0; }
    setFile() { index_1.Struct.setUint16(12, 0, this); }
    getStruct() {
        index_1.Struct.testWhich("struct", index_1.Struct.getUint16(12, this), 1, this);
        return index_1.Struct.getAs(Node_Struct, this);
    }
    initStruct() {
        index_1.Struct.setUint16(12, 1, this);
        return index_1.Struct.getAs(Node_Struct, this);
    }
    isStruct() { return index_1.Struct.getUint16(12, this) === 1; }
    setStruct() { index_1.Struct.setUint16(12, 1, this); }
    getEnum() {
        index_1.Struct.testWhich("enum", index_1.Struct.getUint16(12, this), 2, this);
        return index_1.Struct.getAs(Node_Enum, this);
    }
    initEnum() {
        index_1.Struct.setUint16(12, 2, this);
        return index_1.Struct.getAs(Node_Enum, this);
    }
    isEnum() { return index_1.Struct.getUint16(12, this) === 2; }
    setEnum() { index_1.Struct.setUint16(12, 2, this); }
    getInterface() {
        index_1.Struct.testWhich("interface", index_1.Struct.getUint16(12, this), 3, this);
        return index_1.Struct.getAs(Node_Interface, this);
    }
    initInterface() {
        index_1.Struct.setUint16(12, 3, this);
        return index_1.Struct.getAs(Node_Interface, this);
    }
    isInterface() { return index_1.Struct.getUint16(12, this) === 3; }
    setInterface() { index_1.Struct.setUint16(12, 3, this); }
    getConst() {
        index_1.Struct.testWhich("const", index_1.Struct.getUint16(12, this), 4, this);
        return index_1.Struct.getAs(Node_Const, this);
    }
    initConst() {
        index_1.Struct.setUint16(12, 4, this);
        return index_1.Struct.getAs(Node_Const, this);
    }
    isConst() { return index_1.Struct.getUint16(12, this) === 4; }
    setConst() { index_1.Struct.setUint16(12, 4, this); }
    getAnnotation() {
        index_1.Struct.testWhich("annotation", index_1.Struct.getUint16(12, this), 5, this);
        return index_1.Struct.getAs(Node_Annotation, this);
    }
    initAnnotation() {
        index_1.Struct.setUint16(12, 5, this);
        return index_1.Struct.getAs(Node_Annotation, this);
    }
    isAnnotation() { return index_1.Struct.getUint16(12, this) === 5; }
    setAnnotation() { index_1.Struct.setUint16(12, 5, this); }
    toString() { return "Node_" + super.toString(); }
    which() { return index_1.Struct.getUint16(12, this); }
}
exports.Node = Node;
Node.FILE = Node_Which.FILE;
Node.STRUCT = Node_Which.STRUCT;
Node.ENUM = Node_Which.ENUM;
Node.INTERFACE = Node_Which.INTERFACE;
Node.CONST = Node_Which.CONST;
Node.ANNOTATION = Node_Which.ANNOTATION;
Node.Parameter = Node_Parameter;
Node.NestedNode = Node_NestedNode;
Node._capnp = { displayName: "Node", id: "e682ab4cf923a417", size: new index_1.ObjectSize(40, 6) };
class Field_Slot extends index_1.Struct {
    getOffset() { return index_1.Struct.getUint32(4, this); }
    setOffset(value) { index_1.Struct.setUint32(4, value, this); }
    adoptType(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(2, this)); }
    disownType() { return index_1.Struct.disown(this.getType()); }
    getType() { return index_1.Struct.getStruct(2, Type, this); }
    hasType() { return !index_1.Struct.isNull(index_1.Struct.getPointer(2, this)); }
    initType() { return index_1.Struct.initStructAt(2, Type, this); }
    setType(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(2, this)); }
    adoptDefaultValue(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(3, this)); }
    disownDefaultValue() { return index_1.Struct.disown(this.getDefaultValue()); }
    getDefaultValue() { return index_1.Struct.getStruct(3, Value, this); }
    hasDefaultValue() { return !index_1.Struct.isNull(index_1.Struct.getPointer(3, this)); }
    initDefaultValue() { return index_1.Struct.initStructAt(3, Value, this); }
    setDefaultValue(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(3, this)); }
    getHadExplicitDefault() { return index_1.Struct.getBit(128, this); }
    setHadExplicitDefault(value) { index_1.Struct.setBit(128, value, this); }
    toString() { return "Field_Slot_" + super.toString(); }
}
exports.Field_Slot = Field_Slot;
Field_Slot._capnp = { displayName: "slot", id: "c42305476bb4746f", size: new index_1.ObjectSize(24, 4) };
class Field_Group extends index_1.Struct {
    getTypeId() { return index_1.Struct.getUint64(16, this); }
    setTypeId(value) { index_1.Struct.setUint64(16, value, this); }
    toString() { return "Field_Group_" + super.toString(); }
}
exports.Field_Group = Field_Group;
Field_Group._capnp = { displayName: "group", id: "cafccddb68db1d11", size: new index_1.ObjectSize(24, 4) };
var Field_Ordinal_Which;
(function (Field_Ordinal_Which) {
    Field_Ordinal_Which[Field_Ordinal_Which["IMPLICIT"] = 0] = "IMPLICIT";
    Field_Ordinal_Which[Field_Ordinal_Which["EXPLICIT"] = 1] = "EXPLICIT";
})(Field_Ordinal_Which = exports.Field_Ordinal_Which || (exports.Field_Ordinal_Which = {}));
class Field_Ordinal extends index_1.Struct {
    isImplicit() { return index_1.Struct.getUint16(10, this) === 0; }
    setImplicit() { index_1.Struct.setUint16(10, 0, this); }
    getExplicit() {
        index_1.Struct.testWhich("explicit", index_1.Struct.getUint16(10, this), 1, this);
        return index_1.Struct.getUint16(12, this);
    }
    isExplicit() { return index_1.Struct.getUint16(10, this) === 1; }
    setExplicit(value) {
        index_1.Struct.setUint16(10, 1, this);
        index_1.Struct.setUint16(12, value, this);
    }
    toString() { return "Field_Ordinal_" + super.toString(); }
    which() { return index_1.Struct.getUint16(10, this); }
}
exports.Field_Ordinal = Field_Ordinal;
Field_Ordinal.IMPLICIT = Field_Ordinal_Which.IMPLICIT;
Field_Ordinal.EXPLICIT = Field_Ordinal_Which.EXPLICIT;
Field_Ordinal._capnp = { displayName: "ordinal", id: "bb90d5c287870be6", size: new index_1.ObjectSize(24, 4) };
var Field_Which;
(function (Field_Which) {
    Field_Which[Field_Which["SLOT"] = 0] = "SLOT";
    Field_Which[Field_Which["GROUP"] = 1] = "GROUP";
})(Field_Which = exports.Field_Which || (exports.Field_Which = {}));
class Field extends index_1.Struct {
    getName() { return index_1.Struct.getText(0, this); }
    setName(value) { index_1.Struct.setText(0, value, this); }
    getCodeOrder() { return index_1.Struct.getUint16(0, this); }
    setCodeOrder(value) { index_1.Struct.setUint16(0, value, this); }
    adoptAnnotations(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(1, this)); }
    disownAnnotations() { return index_1.Struct.disown(this.getAnnotations()); }
    getAnnotations() { return index_1.Struct.getList(1, Field._Annotations, this); }
    hasAnnotations() { return !index_1.Struct.isNull(index_1.Struct.getPointer(1, this)); }
    initAnnotations(length) { return index_1.Struct.initList(1, Field._Annotations, length, this); }
    setAnnotations(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(1, this)); }
    getDiscriminantValue() { return index_1.Struct.getUint16(2, this, Field._capnp.defaultDiscriminantValue); }
    setDiscriminantValue(value) { index_1.Struct.setUint16(2, value, this); }
    getSlot() {
        index_1.Struct.testWhich("slot", index_1.Struct.getUint16(8, this), 0, this);
        return index_1.Struct.getAs(Field_Slot, this);
    }
    initSlot() {
        index_1.Struct.setUint16(8, 0, this);
        return index_1.Struct.getAs(Field_Slot, this);
    }
    isSlot() { return index_1.Struct.getUint16(8, this) === 0; }
    setSlot() { index_1.Struct.setUint16(8, 0, this); }
    getGroup() {
        index_1.Struct.testWhich("group", index_1.Struct.getUint16(8, this), 1, this);
        return index_1.Struct.getAs(Field_Group, this);
    }
    initGroup() {
        index_1.Struct.setUint16(8, 1, this);
        return index_1.Struct.getAs(Field_Group, this);
    }
    isGroup() { return index_1.Struct.getUint16(8, this) === 1; }
    setGroup() { index_1.Struct.setUint16(8, 1, this); }
    getOrdinal() { return index_1.Struct.getAs(Field_Ordinal, this); }
    initOrdinal() { return index_1.Struct.getAs(Field_Ordinal, this); }
    toString() { return "Field_" + super.toString(); }
    which() { return index_1.Struct.getUint16(8, this); }
}
exports.Field = Field;
Field.NO_DISCRIMINANT = 65535;
Field.SLOT = Field_Which.SLOT;
Field.GROUP = Field_Which.GROUP;
Field._capnp = { displayName: "Field", id: "9aad50a41f4af45f", size: new index_1.ObjectSize(24, 4), defaultDiscriminantValue: capnp.getUint16Mask(65535) };
class Enumerant extends index_1.Struct {
    getName() { return index_1.Struct.getText(0, this); }
    setName(value) { index_1.Struct.setText(0, value, this); }
    getCodeOrder() { return index_1.Struct.getUint16(0, this); }
    setCodeOrder(value) { index_1.Struct.setUint16(0, value, this); }
    adoptAnnotations(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(1, this)); }
    disownAnnotations() { return index_1.Struct.disown(this.getAnnotations()); }
    getAnnotations() { return index_1.Struct.getList(1, Enumerant._Annotations, this); }
    hasAnnotations() { return !index_1.Struct.isNull(index_1.Struct.getPointer(1, this)); }
    initAnnotations(length) { return index_1.Struct.initList(1, Enumerant._Annotations, length, this); }
    setAnnotations(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(1, this)); }
    toString() { return "Enumerant_" + super.toString(); }
}
exports.Enumerant = Enumerant;
Enumerant._capnp = { displayName: "Enumerant", id: "978a7cebdc549a4d", size: new index_1.ObjectSize(8, 2) };
class Superclass extends index_1.Struct {
    getId() { return index_1.Struct.getUint64(0, this); }
    setId(value) { index_1.Struct.setUint64(0, value, this); }
    adoptBrand(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this)); }
    disownBrand() { return index_1.Struct.disown(this.getBrand()); }
    getBrand() { return index_1.Struct.getStruct(0, Brand, this); }
    hasBrand() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initBrand() { return index_1.Struct.initStructAt(0, Brand, this); }
    setBrand(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this)); }
    toString() { return "Superclass_" + super.toString(); }
}
exports.Superclass = Superclass;
Superclass._capnp = { displayName: "Superclass", id: "a9962a9ed0a4d7f8", size: new index_1.ObjectSize(8, 1) };
class Method extends index_1.Struct {
    getName() { return index_1.Struct.getText(0, this); }
    setName(value) { index_1.Struct.setText(0, value, this); }
    getCodeOrder() { return index_1.Struct.getUint16(0, this); }
    setCodeOrder(value) { index_1.Struct.setUint16(0, value, this); }
    adoptImplicitParameters(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(4, this)); }
    disownImplicitParameters() { return index_1.Struct.disown(this.getImplicitParameters()); }
    getImplicitParameters() { return index_1.Struct.getList(4, Method._ImplicitParameters, this); }
    hasImplicitParameters() { return !index_1.Struct.isNull(index_1.Struct.getPointer(4, this)); }
    initImplicitParameters(length) { return index_1.Struct.initList(4, Method._ImplicitParameters, length, this); }
    setImplicitParameters(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(4, this)); }
    getParamStructType() { return index_1.Struct.getUint64(8, this); }
    setParamStructType(value) { index_1.Struct.setUint64(8, value, this); }
    adoptParamBrand(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(2, this)); }
    disownParamBrand() { return index_1.Struct.disown(this.getParamBrand()); }
    getParamBrand() { return index_1.Struct.getStruct(2, Brand, this); }
    hasParamBrand() { return !index_1.Struct.isNull(index_1.Struct.getPointer(2, this)); }
    initParamBrand() { return index_1.Struct.initStructAt(2, Brand, this); }
    setParamBrand(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(2, this)); }
    getResultStructType() { return index_1.Struct.getUint64(16, this); }
    setResultStructType(value) { index_1.Struct.setUint64(16, value, this); }
    adoptResultBrand(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(3, this)); }
    disownResultBrand() { return index_1.Struct.disown(this.getResultBrand()); }
    getResultBrand() { return index_1.Struct.getStruct(3, Brand, this); }
    hasResultBrand() { return !index_1.Struct.isNull(index_1.Struct.getPointer(3, this)); }
    initResultBrand() { return index_1.Struct.initStructAt(3, Brand, this); }
    setResultBrand(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(3, this)); }
    adoptAnnotations(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(1, this)); }
    disownAnnotations() { return index_1.Struct.disown(this.getAnnotations()); }
    getAnnotations() { return index_1.Struct.getList(1, Method._Annotations, this); }
    hasAnnotations() { return !index_1.Struct.isNull(index_1.Struct.getPointer(1, this)); }
    initAnnotations(length) { return index_1.Struct.initList(1, Method._Annotations, length, this); }
    setAnnotations(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(1, this)); }
    toString() { return "Method_" + super.toString(); }
}
exports.Method = Method;
Method._capnp = { displayName: "Method", id: "9500cce23b334d80", size: new index_1.ObjectSize(24, 5) };
class Type_List extends index_1.Struct {
    adoptElementType(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this)); }
    disownElementType() { return index_1.Struct.disown(this.getElementType()); }
    getElementType() { return index_1.Struct.getStruct(0, Type, this); }
    hasElementType() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initElementType() { return index_1.Struct.initStructAt(0, Type, this); }
    setElementType(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this)); }
    toString() { return "Type_List_" + super.toString(); }
}
exports.Type_List = Type_List;
Type_List._capnp = { displayName: "list", id: "87e739250a60ea97", size: new index_1.ObjectSize(24, 1) };
class Type_Enum extends index_1.Struct {
    getTypeId() { return index_1.Struct.getUint64(8, this); }
    setTypeId(value) { index_1.Struct.setUint64(8, value, this); }
    adoptBrand(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this)); }
    disownBrand() { return index_1.Struct.disown(this.getBrand()); }
    getBrand() { return index_1.Struct.getStruct(0, Brand, this); }
    hasBrand() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initBrand() { return index_1.Struct.initStructAt(0, Brand, this); }
    setBrand(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this)); }
    toString() { return "Type_Enum_" + super.toString(); }
}
exports.Type_Enum = Type_Enum;
Type_Enum._capnp = { displayName: "enum", id: "9e0e78711a7f87a9", size: new index_1.ObjectSize(24, 1) };
class Type_Struct extends index_1.Struct {
    getTypeId() { return index_1.Struct.getUint64(8, this); }
    setTypeId(value) { index_1.Struct.setUint64(8, value, this); }
    adoptBrand(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this)); }
    disownBrand() { return index_1.Struct.disown(this.getBrand()); }
    getBrand() { return index_1.Struct.getStruct(0, Brand, this); }
    hasBrand() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initBrand() { return index_1.Struct.initStructAt(0, Brand, this); }
    setBrand(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this)); }
    toString() { return "Type_Struct_" + super.toString(); }
}
exports.Type_Struct = Type_Struct;
Type_Struct._capnp = { displayName: "struct", id: "ac3a6f60ef4cc6d3", size: new index_1.ObjectSize(24, 1) };
class Type_Interface extends index_1.Struct {
    getTypeId() { return index_1.Struct.getUint64(8, this); }
    setTypeId(value) { index_1.Struct.setUint64(8, value, this); }
    adoptBrand(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this)); }
    disownBrand() { return index_1.Struct.disown(this.getBrand()); }
    getBrand() { return index_1.Struct.getStruct(0, Brand, this); }
    hasBrand() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initBrand() { return index_1.Struct.initStructAt(0, Brand, this); }
    setBrand(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this)); }
    toString() { return "Type_Interface_" + super.toString(); }
}
exports.Type_Interface = Type_Interface;
Type_Interface._capnp = { displayName: "interface", id: "ed8bca69f7fb0cbf", size: new index_1.ObjectSize(24, 1) };
var Type_AnyPointer_Unconstrained_Which;
(function (Type_AnyPointer_Unconstrained_Which) {
    Type_AnyPointer_Unconstrained_Which[Type_AnyPointer_Unconstrained_Which["ANY_KIND"] = 0] = "ANY_KIND";
    Type_AnyPointer_Unconstrained_Which[Type_AnyPointer_Unconstrained_Which["STRUCT"] = 1] = "STRUCT";
    Type_AnyPointer_Unconstrained_Which[Type_AnyPointer_Unconstrained_Which["LIST"] = 2] = "LIST";
    Type_AnyPointer_Unconstrained_Which[Type_AnyPointer_Unconstrained_Which["CAPABILITY"] = 3] = "CAPABILITY";
})(Type_AnyPointer_Unconstrained_Which = exports.Type_AnyPointer_Unconstrained_Which || (exports.Type_AnyPointer_Unconstrained_Which = {}));
class Type_AnyPointer_Unconstrained extends index_1.Struct {
    isAnyKind() { return index_1.Struct.getUint16(10, this) === 0; }
    setAnyKind() { index_1.Struct.setUint16(10, 0, this); }
    isStruct() { return index_1.Struct.getUint16(10, this) === 1; }
    setStruct() { index_1.Struct.setUint16(10, 1, this); }
    isList() { return index_1.Struct.getUint16(10, this) === 2; }
    setList() { index_1.Struct.setUint16(10, 2, this); }
    isCapability() { return index_1.Struct.getUint16(10, this) === 3; }
    setCapability() { index_1.Struct.setUint16(10, 3, this); }
    toString() { return "Type_AnyPointer_Unconstrained_" + super.toString(); }
    which() { return index_1.Struct.getUint16(10, this); }
}
exports.Type_AnyPointer_Unconstrained = Type_AnyPointer_Unconstrained;
Type_AnyPointer_Unconstrained.ANY_KIND = Type_AnyPointer_Unconstrained_Which.ANY_KIND;
Type_AnyPointer_Unconstrained.STRUCT = Type_AnyPointer_Unconstrained_Which.STRUCT;
Type_AnyPointer_Unconstrained.LIST = Type_AnyPointer_Unconstrained_Which.LIST;
Type_AnyPointer_Unconstrained.CAPABILITY = Type_AnyPointer_Unconstrained_Which.CAPABILITY;
Type_AnyPointer_Unconstrained._capnp = { displayName: "unconstrained", id: "8e3b5f79fe593656", size: new index_1.ObjectSize(24, 1) };
class Type_AnyPointer_Parameter extends index_1.Struct {
    getScopeId() { return index_1.Struct.getUint64(16, this); }
    setScopeId(value) { index_1.Struct.setUint64(16, value, this); }
    getParameterIndex() { return index_1.Struct.getUint16(10, this); }
    setParameterIndex(value) { index_1.Struct.setUint16(10, value, this); }
    toString() { return "Type_AnyPointer_Parameter_" + super.toString(); }
}
exports.Type_AnyPointer_Parameter = Type_AnyPointer_Parameter;
Type_AnyPointer_Parameter._capnp = { displayName: "parameter", id: "9dd1f724f4614a85", size: new index_1.ObjectSize(24, 1) };
class Type_AnyPointer_ImplicitMethodParameter extends index_1.Struct {
    getParameterIndex() { return index_1.Struct.getUint16(10, this); }
    setParameterIndex(value) { index_1.Struct.setUint16(10, value, this); }
    toString() { return "Type_AnyPointer_ImplicitMethodParameter_" + super.toString(); }
}
exports.Type_AnyPointer_ImplicitMethodParameter = Type_AnyPointer_ImplicitMethodParameter;
Type_AnyPointer_ImplicitMethodParameter._capnp = { displayName: "implicitMethodParameter", id: "baefc9120c56e274", size: new index_1.ObjectSize(24, 1) };
var Type_AnyPointer_Which;
(function (Type_AnyPointer_Which) {
    Type_AnyPointer_Which[Type_AnyPointer_Which["UNCONSTRAINED"] = 0] = "UNCONSTRAINED";
    Type_AnyPointer_Which[Type_AnyPointer_Which["PARAMETER"] = 1] = "PARAMETER";
    Type_AnyPointer_Which[Type_AnyPointer_Which["IMPLICIT_METHOD_PARAMETER"] = 2] = "IMPLICIT_METHOD_PARAMETER";
})(Type_AnyPointer_Which = exports.Type_AnyPointer_Which || (exports.Type_AnyPointer_Which = {}));
class Type_AnyPointer extends index_1.Struct {
    getUnconstrained() {
        index_1.Struct.testWhich("unconstrained", index_1.Struct.getUint16(8, this), 0, this);
        return index_1.Struct.getAs(Type_AnyPointer_Unconstrained, this);
    }
    initUnconstrained() {
        index_1.Struct.setUint16(8, 0, this);
        return index_1.Struct.getAs(Type_AnyPointer_Unconstrained, this);
    }
    isUnconstrained() { return index_1.Struct.getUint16(8, this) === 0; }
    setUnconstrained() { index_1.Struct.setUint16(8, 0, this); }
    getParameter() {
        index_1.Struct.testWhich("parameter", index_1.Struct.getUint16(8, this), 1, this);
        return index_1.Struct.getAs(Type_AnyPointer_Parameter, this);
    }
    initParameter() {
        index_1.Struct.setUint16(8, 1, this);
        return index_1.Struct.getAs(Type_AnyPointer_Parameter, this);
    }
    isParameter() { return index_1.Struct.getUint16(8, this) === 1; }
    setParameter() { index_1.Struct.setUint16(8, 1, this); }
    getImplicitMethodParameter() {
        index_1.Struct.testWhich("implicitMethodParameter", index_1.Struct.getUint16(8, this), 2, this);
        return index_1.Struct.getAs(Type_AnyPointer_ImplicitMethodParameter, this);
    }
    initImplicitMethodParameter() {
        index_1.Struct.setUint16(8, 2, this);
        return index_1.Struct.getAs(Type_AnyPointer_ImplicitMethodParameter, this);
    }
    isImplicitMethodParameter() { return index_1.Struct.getUint16(8, this) === 2; }
    setImplicitMethodParameter() { index_1.Struct.setUint16(8, 2, this); }
    toString() { return "Type_AnyPointer_" + super.toString(); }
    which() { return index_1.Struct.getUint16(8, this); }
}
exports.Type_AnyPointer = Type_AnyPointer;
Type_AnyPointer.UNCONSTRAINED = Type_AnyPointer_Which.UNCONSTRAINED;
Type_AnyPointer.PARAMETER = Type_AnyPointer_Which.PARAMETER;
Type_AnyPointer.IMPLICIT_METHOD_PARAMETER = Type_AnyPointer_Which.IMPLICIT_METHOD_PARAMETER;
Type_AnyPointer._capnp = { displayName: "anyPointer", id: "c2573fe8a23e49f1", size: new index_1.ObjectSize(24, 1) };
var Type_Which;
(function (Type_Which) {
    Type_Which[Type_Which["VOID"] = 0] = "VOID";
    Type_Which[Type_Which["BOOL"] = 1] = "BOOL";
    Type_Which[Type_Which["INT8"] = 2] = "INT8";
    Type_Which[Type_Which["INT16"] = 3] = "INT16";
    Type_Which[Type_Which["INT32"] = 4] = "INT32";
    Type_Which[Type_Which["INT64"] = 5] = "INT64";
    Type_Which[Type_Which["UINT8"] = 6] = "UINT8";
    Type_Which[Type_Which["UINT16"] = 7] = "UINT16";
    Type_Which[Type_Which["UINT32"] = 8] = "UINT32";
    Type_Which[Type_Which["UINT64"] = 9] = "UINT64";
    Type_Which[Type_Which["FLOAT32"] = 10] = "FLOAT32";
    Type_Which[Type_Which["FLOAT64"] = 11] = "FLOAT64";
    Type_Which[Type_Which["TEXT"] = 12] = "TEXT";
    Type_Which[Type_Which["DATA"] = 13] = "DATA";
    Type_Which[Type_Which["LIST"] = 14] = "LIST";
    Type_Which[Type_Which["ENUM"] = 15] = "ENUM";
    Type_Which[Type_Which["STRUCT"] = 16] = "STRUCT";
    Type_Which[Type_Which["INTERFACE"] = 17] = "INTERFACE";
    Type_Which[Type_Which["ANY_POINTER"] = 18] = "ANY_POINTER";
})(Type_Which = exports.Type_Which || (exports.Type_Which = {}));
class Type extends index_1.Struct {
    isVoid() { return index_1.Struct.getUint16(0, this) === 0; }
    setVoid() { index_1.Struct.setUint16(0, 0, this); }
    isBool() { return index_1.Struct.getUint16(0, this) === 1; }
    setBool() { index_1.Struct.setUint16(0, 1, this); }
    isInt8() { return index_1.Struct.getUint16(0, this) === 2; }
    setInt8() { index_1.Struct.setUint16(0, 2, this); }
    isInt16() { return index_1.Struct.getUint16(0, this) === 3; }
    setInt16() { index_1.Struct.setUint16(0, 3, this); }
    isInt32() { return index_1.Struct.getUint16(0, this) === 4; }
    setInt32() { index_1.Struct.setUint16(0, 4, this); }
    isInt64() { return index_1.Struct.getUint16(0, this) === 5; }
    setInt64() { index_1.Struct.setUint16(0, 5, this); }
    isUint8() { return index_1.Struct.getUint16(0, this) === 6; }
    setUint8() { index_1.Struct.setUint16(0, 6, this); }
    isUint16() { return index_1.Struct.getUint16(0, this) === 7; }
    setUint16() { index_1.Struct.setUint16(0, 7, this); }
    isUint32() { return index_1.Struct.getUint16(0, this) === 8; }
    setUint32() { index_1.Struct.setUint16(0, 8, this); }
    isUint64() { return index_1.Struct.getUint16(0, this) === 9; }
    setUint64() { index_1.Struct.setUint16(0, 9, this); }
    isFloat32() { return index_1.Struct.getUint16(0, this) === 10; }
    setFloat32() { index_1.Struct.setUint16(0, 10, this); }
    isFloat64() { return index_1.Struct.getUint16(0, this) === 11; }
    setFloat64() { index_1.Struct.setUint16(0, 11, this); }
    isText() { return index_1.Struct.getUint16(0, this) === 12; }
    setText() { index_1.Struct.setUint16(0, 12, this); }
    isData() { return index_1.Struct.getUint16(0, this) === 13; }
    setData() { index_1.Struct.setUint16(0, 13, this); }
    getList() {
        index_1.Struct.testWhich("list", index_1.Struct.getUint16(0, this), 14, this);
        return index_1.Struct.getAs(Type_List, this);
    }
    initList() {
        index_1.Struct.setUint16(0, 14, this);
        return index_1.Struct.getAs(Type_List, this);
    }
    isList() { return index_1.Struct.getUint16(0, this) === 14; }
    setList() { index_1.Struct.setUint16(0, 14, this); }
    getEnum() {
        index_1.Struct.testWhich("enum", index_1.Struct.getUint16(0, this), 15, this);
        return index_1.Struct.getAs(Type_Enum, this);
    }
    initEnum() {
        index_1.Struct.setUint16(0, 15, this);
        return index_1.Struct.getAs(Type_Enum, this);
    }
    isEnum() { return index_1.Struct.getUint16(0, this) === 15; }
    setEnum() { index_1.Struct.setUint16(0, 15, this); }
    getStruct() {
        index_1.Struct.testWhich("struct", index_1.Struct.getUint16(0, this), 16, this);
        return index_1.Struct.getAs(Type_Struct, this);
    }
    initStruct() {
        index_1.Struct.setUint16(0, 16, this);
        return index_1.Struct.getAs(Type_Struct, this);
    }
    isStruct() { return index_1.Struct.getUint16(0, this) === 16; }
    setStruct() { index_1.Struct.setUint16(0, 16, this); }
    getInterface() {
        index_1.Struct.testWhich("interface", index_1.Struct.getUint16(0, this), 17, this);
        return index_1.Struct.getAs(Type_Interface, this);
    }
    initInterface() {
        index_1.Struct.setUint16(0, 17, this);
        return index_1.Struct.getAs(Type_Interface, this);
    }
    isInterface() { return index_1.Struct.getUint16(0, this) === 17; }
    setInterface() { index_1.Struct.setUint16(0, 17, this); }
    getAnyPointer() {
        index_1.Struct.testWhich("anyPointer", index_1.Struct.getUint16(0, this), 18, this);
        return index_1.Struct.getAs(Type_AnyPointer, this);
    }
    initAnyPointer() {
        index_1.Struct.setUint16(0, 18, this);
        return index_1.Struct.getAs(Type_AnyPointer, this);
    }
    isAnyPointer() { return index_1.Struct.getUint16(0, this) === 18; }
    setAnyPointer() { index_1.Struct.setUint16(0, 18, this); }
    toString() { return "Type_" + super.toString(); }
    which() { return index_1.Struct.getUint16(0, this); }
}
exports.Type = Type;
Type.VOID = Type_Which.VOID;
Type.BOOL = Type_Which.BOOL;
Type.INT8 = Type_Which.INT8;
Type.INT16 = Type_Which.INT16;
Type.INT32 = Type_Which.INT32;
Type.INT64 = Type_Which.INT64;
Type.UINT8 = Type_Which.UINT8;
Type.UINT16 = Type_Which.UINT16;
Type.UINT32 = Type_Which.UINT32;
Type.UINT64 = Type_Which.UINT64;
Type.FLOAT32 = Type_Which.FLOAT32;
Type.FLOAT64 = Type_Which.FLOAT64;
Type.TEXT = Type_Which.TEXT;
Type.DATA = Type_Which.DATA;
Type.LIST = Type_Which.LIST;
Type.ENUM = Type_Which.ENUM;
Type.STRUCT = Type_Which.STRUCT;
Type.INTERFACE = Type_Which.INTERFACE;
Type.ANY_POINTER = Type_Which.ANY_POINTER;
Type._capnp = { displayName: "Type", id: "d07378ede1f9cc60", size: new index_1.ObjectSize(24, 1) };
var Brand_Scope_Which;
(function (Brand_Scope_Which) {
    Brand_Scope_Which[Brand_Scope_Which["BIND"] = 0] = "BIND";
    Brand_Scope_Which[Brand_Scope_Which["INHERIT"] = 1] = "INHERIT";
})(Brand_Scope_Which = exports.Brand_Scope_Which || (exports.Brand_Scope_Which = {}));
class Brand_Scope extends index_1.Struct {
    getScopeId() { return index_1.Struct.getUint64(0, this); }
    setScopeId(value) { index_1.Struct.setUint64(0, value, this); }
    adoptBind(value) {
        index_1.Struct.setUint16(8, 0, this);
        index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this));
    }
    disownBind() { return index_1.Struct.disown(this.getBind()); }
    getBind() {
        index_1.Struct.testWhich("bind", index_1.Struct.getUint16(8, this), 0, this);
        return index_1.Struct.getList(0, Brand_Scope._Bind, this);
    }
    hasBind() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initBind(length) {
        index_1.Struct.setUint16(8, 0, this);
        return index_1.Struct.initList(0, Brand_Scope._Bind, length, this);
    }
    isBind() { return index_1.Struct.getUint16(8, this) === 0; }
    setBind(value) {
        index_1.Struct.setUint16(8, 0, this);
        index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this));
    }
    isInherit() { return index_1.Struct.getUint16(8, this) === 1; }
    setInherit() { index_1.Struct.setUint16(8, 1, this); }
    toString() { return "Brand_Scope_" + super.toString(); }
    which() { return index_1.Struct.getUint16(8, this); }
}
exports.Brand_Scope = Brand_Scope;
Brand_Scope.BIND = Brand_Scope_Which.BIND;
Brand_Scope.INHERIT = Brand_Scope_Which.INHERIT;
Brand_Scope._capnp = { displayName: "Scope", id: "abd73485a9636bc9", size: new index_1.ObjectSize(16, 1) };
var Brand_Binding_Which;
(function (Brand_Binding_Which) {
    Brand_Binding_Which[Brand_Binding_Which["UNBOUND"] = 0] = "UNBOUND";
    Brand_Binding_Which[Brand_Binding_Which["TYPE"] = 1] = "TYPE";
})(Brand_Binding_Which = exports.Brand_Binding_Which || (exports.Brand_Binding_Which = {}));
class Brand_Binding extends index_1.Struct {
    isUnbound() { return index_1.Struct.getUint16(0, this) === 0; }
    setUnbound() { index_1.Struct.setUint16(0, 0, this); }
    adoptType(value) {
        index_1.Struct.setUint16(0, 1, this);
        index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this));
    }
    disownType() { return index_1.Struct.disown(this.getType()); }
    getType() {
        index_1.Struct.testWhich("type", index_1.Struct.getUint16(0, this), 1, this);
        return index_1.Struct.getStruct(0, Type, this);
    }
    hasType() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initType() {
        index_1.Struct.setUint16(0, 1, this);
        return index_1.Struct.initStructAt(0, Type, this);
    }
    isType() { return index_1.Struct.getUint16(0, this) === 1; }
    setType(value) {
        index_1.Struct.setUint16(0, 1, this);
        index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this));
    }
    toString() { return "Brand_Binding_" + super.toString(); }
    which() { return index_1.Struct.getUint16(0, this); }
}
exports.Brand_Binding = Brand_Binding;
Brand_Binding.UNBOUND = Brand_Binding_Which.UNBOUND;
Brand_Binding.TYPE = Brand_Binding_Which.TYPE;
Brand_Binding._capnp = { displayName: "Binding", id: "c863cd16969ee7fc", size: new index_1.ObjectSize(8, 1) };
class Brand extends index_1.Struct {
    adoptScopes(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this)); }
    disownScopes() { return index_1.Struct.disown(this.getScopes()); }
    getScopes() { return index_1.Struct.getList(0, Brand._Scopes, this); }
    hasScopes() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initScopes(length) { return index_1.Struct.initList(0, Brand._Scopes, length, this); }
    setScopes(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this)); }
    toString() { return "Brand_" + super.toString(); }
}
exports.Brand = Brand;
Brand.Scope = Brand_Scope;
Brand.Binding = Brand_Binding;
Brand._capnp = { displayName: "Brand", id: "903455f06065422b", size: new index_1.ObjectSize(0, 1) };
var Value_Which;
(function (Value_Which) {
    Value_Which[Value_Which["VOID"] = 0] = "VOID";
    Value_Which[Value_Which["BOOL"] = 1] = "BOOL";
    Value_Which[Value_Which["INT8"] = 2] = "INT8";
    Value_Which[Value_Which["INT16"] = 3] = "INT16";
    Value_Which[Value_Which["INT32"] = 4] = "INT32";
    Value_Which[Value_Which["INT64"] = 5] = "INT64";
    Value_Which[Value_Which["UINT8"] = 6] = "UINT8";
    Value_Which[Value_Which["UINT16"] = 7] = "UINT16";
    Value_Which[Value_Which["UINT32"] = 8] = "UINT32";
    Value_Which[Value_Which["UINT64"] = 9] = "UINT64";
    Value_Which[Value_Which["FLOAT32"] = 10] = "FLOAT32";
    Value_Which[Value_Which["FLOAT64"] = 11] = "FLOAT64";
    Value_Which[Value_Which["TEXT"] = 12] = "TEXT";
    Value_Which[Value_Which["DATA"] = 13] = "DATA";
    Value_Which[Value_Which["LIST"] = 14] = "LIST";
    Value_Which[Value_Which["ENUM"] = 15] = "ENUM";
    Value_Which[Value_Which["STRUCT"] = 16] = "STRUCT";
    Value_Which[Value_Which["INTERFACE"] = 17] = "INTERFACE";
    Value_Which[Value_Which["ANY_POINTER"] = 18] = "ANY_POINTER";
})(Value_Which = exports.Value_Which || (exports.Value_Which = {}));
class Value extends index_1.Struct {
    isVoid() { return index_1.Struct.getUint16(0, this) === 0; }
    setVoid() { index_1.Struct.setUint16(0, 0, this); }
    getBool() {
        index_1.Struct.testWhich("bool", index_1.Struct.getUint16(0, this), 1, this);
        return index_1.Struct.getBit(16, this);
    }
    isBool() { return index_1.Struct.getUint16(0, this) === 1; }
    setBool(value) {
        index_1.Struct.setUint16(0, 1, this);
        index_1.Struct.setBit(16, value, this);
    }
    getInt8() {
        index_1.Struct.testWhich("int8", index_1.Struct.getUint16(0, this), 2, this);
        return index_1.Struct.getInt8(2, this);
    }
    isInt8() { return index_1.Struct.getUint16(0, this) === 2; }
    setInt8(value) {
        index_1.Struct.setUint16(0, 2, this);
        index_1.Struct.setInt8(2, value, this);
    }
    getInt16() {
        index_1.Struct.testWhich("int16", index_1.Struct.getUint16(0, this), 3, this);
        return index_1.Struct.getInt16(2, this);
    }
    isInt16() { return index_1.Struct.getUint16(0, this) === 3; }
    setInt16(value) {
        index_1.Struct.setUint16(0, 3, this);
        index_1.Struct.setInt16(2, value, this);
    }
    getInt32() {
        index_1.Struct.testWhich("int32", index_1.Struct.getUint16(0, this), 4, this);
        return index_1.Struct.getInt32(4, this);
    }
    isInt32() { return index_1.Struct.getUint16(0, this) === 4; }
    setInt32(value) {
        index_1.Struct.setUint16(0, 4, this);
        index_1.Struct.setInt32(4, value, this);
    }
    getInt64() {
        index_1.Struct.testWhich("int64", index_1.Struct.getUint16(0, this), 5, this);
        return index_1.Struct.getInt64(8, this);
    }
    isInt64() { return index_1.Struct.getUint16(0, this) === 5; }
    setInt64(value) {
        index_1.Struct.setUint16(0, 5, this);
        index_1.Struct.setInt64(8, value, this);
    }
    getUint8() {
        index_1.Struct.testWhich("uint8", index_1.Struct.getUint16(0, this), 6, this);
        return index_1.Struct.getUint8(2, this);
    }
    isUint8() { return index_1.Struct.getUint16(0, this) === 6; }
    setUint8(value) {
        index_1.Struct.setUint16(0, 6, this);
        index_1.Struct.setUint8(2, value, this);
    }
    getUint16() {
        index_1.Struct.testWhich("uint16", index_1.Struct.getUint16(0, this), 7, this);
        return index_1.Struct.getUint16(2, this);
    }
    isUint16() { return index_1.Struct.getUint16(0, this) === 7; }
    setUint16(value) {
        index_1.Struct.setUint16(0, 7, this);
        index_1.Struct.setUint16(2, value, this);
    }
    getUint32() {
        index_1.Struct.testWhich("uint32", index_1.Struct.getUint16(0, this), 8, this);
        return index_1.Struct.getUint32(4, this);
    }
    isUint32() { return index_1.Struct.getUint16(0, this) === 8; }
    setUint32(value) {
        index_1.Struct.setUint16(0, 8, this);
        index_1.Struct.setUint32(4, value, this);
    }
    getUint64() {
        index_1.Struct.testWhich("uint64", index_1.Struct.getUint16(0, this), 9, this);
        return index_1.Struct.getUint64(8, this);
    }
    isUint64() { return index_1.Struct.getUint16(0, this) === 9; }
    setUint64(value) {
        index_1.Struct.setUint16(0, 9, this);
        index_1.Struct.setUint64(8, value, this);
    }
    getFloat32() {
        index_1.Struct.testWhich("float32", index_1.Struct.getUint16(0, this), 10, this);
        return index_1.Struct.getFloat32(4, this);
    }
    isFloat32() { return index_1.Struct.getUint16(0, this) === 10; }
    setFloat32(value) {
        index_1.Struct.setUint16(0, 10, this);
        index_1.Struct.setFloat32(4, value, this);
    }
    getFloat64() {
        index_1.Struct.testWhich("float64", index_1.Struct.getUint16(0, this), 11, this);
        return index_1.Struct.getFloat64(8, this);
    }
    isFloat64() { return index_1.Struct.getUint16(0, this) === 11; }
    setFloat64(value) {
        index_1.Struct.setUint16(0, 11, this);
        index_1.Struct.setFloat64(8, value, this);
    }
    getText() {
        index_1.Struct.testWhich("text", index_1.Struct.getUint16(0, this), 12, this);
        return index_1.Struct.getText(0, this);
    }
    isText() { return index_1.Struct.getUint16(0, this) === 12; }
    setText(value) {
        index_1.Struct.setUint16(0, 12, this);
        index_1.Struct.setText(0, value, this);
    }
    adoptData(value) {
        index_1.Struct.setUint16(0, 13, this);
        index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this));
    }
    disownData() { return index_1.Struct.disown(this.getData()); }
    getData() {
        index_1.Struct.testWhich("data", index_1.Struct.getUint16(0, this), 13, this);
        return index_1.Struct.getData(0, this);
    }
    hasData() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initData(length) {
        index_1.Struct.setUint16(0, 13, this);
        return index_1.Struct.initData(0, length, this);
    }
    isData() { return index_1.Struct.getUint16(0, this) === 13; }
    setData(value) {
        index_1.Struct.setUint16(0, 13, this);
        index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this));
    }
    adoptList(value) {
        index_1.Struct.setUint16(0, 14, this);
        index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this));
    }
    disownList() { return index_1.Struct.disown(this.getList()); }
    getList() {
        index_1.Struct.testWhich("list", index_1.Struct.getUint16(0, this), 14, this);
        return index_1.Struct.getPointer(0, this);
    }
    hasList() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    isList() { return index_1.Struct.getUint16(0, this) === 14; }
    setList(value) {
        index_1.Struct.setUint16(0, 14, this);
        index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this));
    }
    getEnum() {
        index_1.Struct.testWhich("enum", index_1.Struct.getUint16(0, this), 15, this);
        return index_1.Struct.getUint16(2, this);
    }
    isEnum() { return index_1.Struct.getUint16(0, this) === 15; }
    setEnum(value) {
        index_1.Struct.setUint16(0, 15, this);
        index_1.Struct.setUint16(2, value, this);
    }
    adoptStruct(value) {
        index_1.Struct.setUint16(0, 16, this);
        index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this));
    }
    disownStruct() { return index_1.Struct.disown(this.getStruct()); }
    getStruct() {
        index_1.Struct.testWhich("struct", index_1.Struct.getUint16(0, this), 16, this);
        return index_1.Struct.getPointer(0, this);
    }
    hasStruct() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    isStruct() { return index_1.Struct.getUint16(0, this) === 16; }
    setStruct(value) {
        index_1.Struct.setUint16(0, 16, this);
        index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this));
    }
    isInterface() { return index_1.Struct.getUint16(0, this) === 17; }
    setInterface() { index_1.Struct.setUint16(0, 17, this); }
    adoptAnyPointer(value) {
        index_1.Struct.setUint16(0, 18, this);
        index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this));
    }
    disownAnyPointer() { return index_1.Struct.disown(this.getAnyPointer()); }
    getAnyPointer() {
        index_1.Struct.testWhich("anyPointer", index_1.Struct.getUint16(0, this), 18, this);
        return index_1.Struct.getPointer(0, this);
    }
    hasAnyPointer() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    isAnyPointer() { return index_1.Struct.getUint16(0, this) === 18; }
    setAnyPointer(value) {
        index_1.Struct.setUint16(0, 18, this);
        index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this));
    }
    toString() { return "Value_" + super.toString(); }
    which() { return index_1.Struct.getUint16(0, this); }
}
exports.Value = Value;
Value.VOID = Value_Which.VOID;
Value.BOOL = Value_Which.BOOL;
Value.INT8 = Value_Which.INT8;
Value.INT16 = Value_Which.INT16;
Value.INT32 = Value_Which.INT32;
Value.INT64 = Value_Which.INT64;
Value.UINT8 = Value_Which.UINT8;
Value.UINT16 = Value_Which.UINT16;
Value.UINT32 = Value_Which.UINT32;
Value.UINT64 = Value_Which.UINT64;
Value.FLOAT32 = Value_Which.FLOAT32;
Value.FLOAT64 = Value_Which.FLOAT64;
Value.TEXT = Value_Which.TEXT;
Value.DATA = Value_Which.DATA;
Value.LIST = Value_Which.LIST;
Value.ENUM = Value_Which.ENUM;
Value.STRUCT = Value_Which.STRUCT;
Value.INTERFACE = Value_Which.INTERFACE;
Value.ANY_POINTER = Value_Which.ANY_POINTER;
Value._capnp = { displayName: "Value", id: "ce23dcd2d7b00c9b", size: new index_1.ObjectSize(16, 1) };
class Annotation extends index_1.Struct {
    getId() { return index_1.Struct.getUint64(0, this); }
    setId(value) { index_1.Struct.setUint64(0, value, this); }
    adoptBrand(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(1, this)); }
    disownBrand() { return index_1.Struct.disown(this.getBrand()); }
    getBrand() { return index_1.Struct.getStruct(1, Brand, this); }
    hasBrand() { return !index_1.Struct.isNull(index_1.Struct.getPointer(1, this)); }
    initBrand() { return index_1.Struct.initStructAt(1, Brand, this); }
    setBrand(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(1, this)); }
    adoptValue(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this)); }
    disownValue() { return index_1.Struct.disown(this.getValue()); }
    getValue() { return index_1.Struct.getStruct(0, Value, this); }
    hasValue() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initValue() { return index_1.Struct.initStructAt(0, Value, this); }
    setValue(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this)); }
    toString() { return "Annotation_" + super.toString(); }
}
exports.Annotation = Annotation;
Annotation._capnp = { displayName: "Annotation", id: "f1c8950dab257542", size: new index_1.ObjectSize(8, 2) };
var ElementSize;
(function (ElementSize) {
    ElementSize[ElementSize["EMPTY"] = 0] = "EMPTY";
    ElementSize[ElementSize["BIT"] = 1] = "BIT";
    ElementSize[ElementSize["BYTE"] = 2] = "BYTE";
    ElementSize[ElementSize["TWO_BYTES"] = 3] = "TWO_BYTES";
    ElementSize[ElementSize["FOUR_BYTES"] = 4] = "FOUR_BYTES";
    ElementSize[ElementSize["EIGHT_BYTES"] = 5] = "EIGHT_BYTES";
    ElementSize[ElementSize["POINTER"] = 6] = "POINTER";
    ElementSize[ElementSize["INLINE_COMPOSITE"] = 7] = "INLINE_COMPOSITE";
})(ElementSize = exports.ElementSize || (exports.ElementSize = {}));
class CapnpVersion extends index_1.Struct {
    getMajor() { return index_1.Struct.getUint16(0, this); }
    setMajor(value) { index_1.Struct.setUint16(0, value, this); }
    getMinor() { return index_1.Struct.getUint8(2, this); }
    setMinor(value) { index_1.Struct.setUint8(2, value, this); }
    getMicro() { return index_1.Struct.getUint8(3, this); }
    setMicro(value) { index_1.Struct.setUint8(3, value, this); }
    toString() { return "CapnpVersion_" + super.toString(); }
}
exports.CapnpVersion = CapnpVersion;
CapnpVersion._capnp = { displayName: "CapnpVersion", id: "d85d305b7d839963", size: new index_1.ObjectSize(8, 0) };
class CodeGeneratorRequest_RequestedFile_Import extends index_1.Struct {
    getId() { return index_1.Struct.getUint64(0, this); }
    setId(value) { index_1.Struct.setUint64(0, value, this); }
    getName() { return index_1.Struct.getText(0, this); }
    setName(value) { index_1.Struct.setText(0, value, this); }
    toString() { return "CodeGeneratorRequest_RequestedFile_Import_" + super.toString(); }
}
exports.CodeGeneratorRequest_RequestedFile_Import = CodeGeneratorRequest_RequestedFile_Import;
CodeGeneratorRequest_RequestedFile_Import._capnp = { displayName: "Import", id: "ae504193122357e5", size: new index_1.ObjectSize(8, 1) };
class CodeGeneratorRequest_RequestedFile extends index_1.Struct {
    getId() { return index_1.Struct.getUint64(0, this); }
    setId(value) { index_1.Struct.setUint64(0, value, this); }
    getFilename() { return index_1.Struct.getText(0, this); }
    setFilename(value) { index_1.Struct.setText(0, value, this); }
    adoptImports(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(1, this)); }
    disownImports() { return index_1.Struct.disown(this.getImports()); }
    getImports() { return index_1.Struct.getList(1, CodeGeneratorRequest_RequestedFile._Imports, this); }
    hasImports() { return !index_1.Struct.isNull(index_1.Struct.getPointer(1, this)); }
    initImports(length) { return index_1.Struct.initList(1, CodeGeneratorRequest_RequestedFile._Imports, length, this); }
    setImports(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(1, this)); }
    toString() { return "CodeGeneratorRequest_RequestedFile_" + super.toString(); }
}
exports.CodeGeneratorRequest_RequestedFile = CodeGeneratorRequest_RequestedFile;
CodeGeneratorRequest_RequestedFile.Import = CodeGeneratorRequest_RequestedFile_Import;
CodeGeneratorRequest_RequestedFile._capnp = { displayName: "RequestedFile", id: "cfea0eb02e810062", size: new index_1.ObjectSize(8, 2) };
class CodeGeneratorRequest extends index_1.Struct {
    adoptCapnpVersion(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(2, this)); }
    disownCapnpVersion() { return index_1.Struct.disown(this.getCapnpVersion()); }
    getCapnpVersion() { return index_1.Struct.getStruct(2, CapnpVersion, this); }
    hasCapnpVersion() { return !index_1.Struct.isNull(index_1.Struct.getPointer(2, this)); }
    initCapnpVersion() { return index_1.Struct.initStructAt(2, CapnpVersion, this); }
    setCapnpVersion(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(2, this)); }
    adoptNodes(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this)); }
    disownNodes() { return index_1.Struct.disown(this.getNodes()); }
    getNodes() { return index_1.Struct.getList(0, CodeGeneratorRequest._Nodes, this); }
    hasNodes() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initNodes(length) { return index_1.Struct.initList(0, CodeGeneratorRequest._Nodes, length, this); }
    setNodes(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this)); }
    adoptRequestedFiles(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(1, this)); }
    disownRequestedFiles() { return index_1.Struct.disown(this.getRequestedFiles()); }
    getRequestedFiles() { return index_1.Struct.getList(1, CodeGeneratorRequest._RequestedFiles, this); }
    hasRequestedFiles() { return !index_1.Struct.isNull(index_1.Struct.getPointer(1, this)); }
    initRequestedFiles(length) { return index_1.Struct.initList(1, CodeGeneratorRequest._RequestedFiles, length, this); }
    setRequestedFiles(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(1, this)); }
    toString() { return "CodeGeneratorRequest_" + super.toString(); }
}
exports.CodeGeneratorRequest = CodeGeneratorRequest;
CodeGeneratorRequest.RequestedFile = CodeGeneratorRequest_RequestedFile;
CodeGeneratorRequest._capnp = { displayName: "CodeGeneratorRequest", id: "bfc546f6210ad7ce", size: new index_1.ObjectSize(0, 3) };
Node_Struct._Fields = capnp.CompositeList(Field);
Node_Enum._Enumerants = capnp.CompositeList(Enumerant);
Node_Interface._Methods = capnp.CompositeList(Method);
Node_Interface._Superclasses = capnp.CompositeList(Superclass);
Node._Parameters = capnp.CompositeList(Node_Parameter);
Node._NestedNodes = capnp.CompositeList(Node_NestedNode);
Node._Annotations = capnp.CompositeList(Annotation);
Field._Annotations = capnp.CompositeList(Annotation);
Enumerant._Annotations = capnp.CompositeList(Annotation);
Method._ImplicitParameters = capnp.CompositeList(Node_Parameter);
Method._Annotations = capnp.CompositeList(Annotation);
Brand_Scope._Bind = capnp.CompositeList(Brand_Binding);
Brand._Scopes = capnp.CompositeList(Brand_Scope);
CodeGeneratorRequest_RequestedFile._Imports = capnp.CompositeList(CodeGeneratorRequest_RequestedFile_Import);
CodeGeneratorRequest._Nodes = capnp.CompositeList(Node);
CodeGeneratorRequest._RequestedFiles = capnp.CompositeList(CodeGeneratorRequest_RequestedFile);
//# sourceMappingURL=schema.capnp.js.map