/**
 * This file is generated by hand in order to bootstrap compiler development. It is intended to be an exact match to
 * compiled output.
 */

import * as capnp from "../index";
import { ObjectSize as __O, Struct as __S } from "../index";
export const _capnpFileId = "a184c7885cdaf2a1";
export enum Side {
    SERVER,
    CLIENT
}
export class VatId extends __S {
    static readonly _capnp = { displayName: "VatId", id: "d20b909fee733a8e", size: new __O(8, 0) };
    getSide(): Side { return __S.getUint16(0, this); }
    setSide(value: Side): void { __S.setUint16(0, value, this); }
    toString(): string { return "VatId_" + super.toString(); }
}
export class ProvisionId extends __S {
    static readonly _capnp = { displayName: "ProvisionId", id: "b88d09a9c5f39817", size: new __O(8, 0) };
    getJoinId(): number { return __S.getUint32(0, this); }
    setJoinId(value: number): void { __S.setUint32(0, value, this); }
    toString(): string { return "ProvisionId_" + super.toString(); }
}
export class RecipientId extends __S {
    static readonly _capnp = { displayName: "RecipientId", id: "89f389b6fd4082c1", size: new __O(0, 0) };
    toString(): string { return "RecipientId_" + super.toString(); }
}
export class ThirdPartyCapId extends __S {
    static readonly _capnp = { displayName: "ThirdPartyCapId", id: "b47f4979672cb59d", size: new __O(0, 0) };
    toString(): string { return "ThirdPartyCapId_" + super.toString(); }
}
export class JoinKeyPart extends __S {
    static readonly _capnp = { displayName: "JoinKeyPart", id: "95b29059097fca83", size: new __O(8, 0) };
    getJoinId(): number { return __S.getUint32(0, this); }
    setJoinId(value: number): void { __S.setUint32(0, value, this); }
    getPartCount(): number { return __S.getUint16(4, this); }
    setPartCount(value: number): void { __S.setUint16(4, value, this); }
    getPartNum(): number { return __S.getUint16(6, this); }
    setPartNum(value: number): void { __S.setUint16(6, value, this); }
    toString(): string { return "JoinKeyPart_" + super.toString(); }
}
export class JoinResult extends __S {
    static readonly _capnp = { displayName: "JoinResult", id: "9d263a3630b7ebee", size: new __O(8, 1) };
    getJoinId(): number { return __S.getUint32(0, this); }
    setJoinId(value: number): void { __S.setUint32(0, value, this); }
    getSucceeded(): boolean { return __S.getBit(32, this); }
    setSucceeded(value: boolean): void { __S.setBit(32, value, this); }
    adoptCap(value: capnp.Orphan<capnp.Pointer>): void { __S.adopt(value, __S.getPointer(0, this)); }
    disownCap(): capnp.Orphan<capnp.Pointer> { return __S.disown(this.getCap()); }
    getCap(): capnp.Pointer { return __S.getPointer(0, this); }
    hasCap(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    setCap(value: capnp.Pointer): void { __S.copyFrom(value, __S.getPointer(0, this)); }
    toString(): string { return "JoinResult_" + super.toString(); }
}
