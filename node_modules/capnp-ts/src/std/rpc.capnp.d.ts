/**
 * This file is generated by hand in order to bootstrap compiler development. It is intended to be an exact match to
 * compiled output.
 */
import * as capnp from "../index";
import { Struct as __S } from "../index";
export declare const _capnpFileId = "b312981b2552a250";
export declare enum Message_Which {
    UNIMPLEMENTED = 0,
    ABORT = 1,
    BOOTSTRAP = 8,
    CALL = 2,
    RETURN = 3,
    FINISH = 4,
    RESOLVE = 5,
    RELEASE = 6,
    DISEMBARGO = 13,
    OBSOLETE_SAVE = 7,
    OBSOLETE_DELETE = 9,
    PROVIDE = 10,
    ACCEPT = 11,
    JOIN = 12
}
export declare class Message extends __S {
    static readonly UNIMPLEMENTED = Message_Which.UNIMPLEMENTED;
    static readonly ABORT = Message_Which.ABORT;
    static readonly BOOTSTRAP = Message_Which.BOOTSTRAP;
    static readonly CALL = Message_Which.CALL;
    static readonly RETURN = Message_Which.RETURN;
    static readonly FINISH = Message_Which.FINISH;
    static readonly RESOLVE = Message_Which.RESOLVE;
    static readonly RELEASE = Message_Which.RELEASE;
    static readonly DISEMBARGO = Message_Which.DISEMBARGO;
    static readonly OBSOLETE_SAVE = Message_Which.OBSOLETE_SAVE;
    static readonly OBSOLETE_DELETE = Message_Which.OBSOLETE_DELETE;
    static readonly PROVIDE = Message_Which.PROVIDE;
    static readonly ACCEPT = Message_Which.ACCEPT;
    static readonly JOIN = Message_Which.JOIN;
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    adoptUnimplemented(value: capnp.Orphan<Message>): void;
    disownUnimplemented(): capnp.Orphan<Message>;
    getUnimplemented(): Message;
    hasUnimplemented(): boolean;
    initUnimplemented(): Message;
    isUnimplemented(): boolean;
    setUnimplemented(value: Message): void;
    adoptAbort(value: capnp.Orphan<Exception>): void;
    disownAbort(): capnp.Orphan<Exception>;
    getAbort(): Exception;
    hasAbort(): boolean;
    initAbort(): Exception;
    isAbort(): boolean;
    setAbort(value: Exception): void;
    adoptBootstrap(value: capnp.Orphan<Bootstrap>): void;
    disownBootstrap(): capnp.Orphan<Bootstrap>;
    getBootstrap(): Bootstrap;
    hasBootstrap(): boolean;
    initBootstrap(): Bootstrap;
    isBootstrap(): boolean;
    setBootstrap(value: Bootstrap): void;
    adoptCall(value: capnp.Orphan<Call>): void;
    disownCall(): capnp.Orphan<Call>;
    getCall(): Call;
    hasCall(): boolean;
    initCall(): Call;
    isCall(): boolean;
    setCall(value: Call): void;
    adoptReturn(value: capnp.Orphan<Return>): void;
    disownReturn(): capnp.Orphan<Return>;
    getReturn(): Return;
    hasReturn(): boolean;
    initReturn(): Return;
    isReturn(): boolean;
    setReturn(value: Return): void;
    adoptFinish(value: capnp.Orphan<Finish>): void;
    disownFinish(): capnp.Orphan<Finish>;
    getFinish(): Finish;
    hasFinish(): boolean;
    initFinish(): Finish;
    isFinish(): boolean;
    setFinish(value: Finish): void;
    adoptResolve(value: capnp.Orphan<Resolve>): void;
    disownResolve(): capnp.Orphan<Resolve>;
    getResolve(): Resolve;
    hasResolve(): boolean;
    initResolve(): Resolve;
    isResolve(): boolean;
    setResolve(value: Resolve): void;
    adoptRelease(value: capnp.Orphan<Release>): void;
    disownRelease(): capnp.Orphan<Release>;
    getRelease(): Release;
    hasRelease(): boolean;
    initRelease(): Release;
    isRelease(): boolean;
    setRelease(value: Release): void;
    adoptDisembargo(value: capnp.Orphan<Disembargo>): void;
    disownDisembargo(): capnp.Orphan<Disembargo>;
    getDisembargo(): Disembargo;
    hasDisembargo(): boolean;
    initDisembargo(): Disembargo;
    isDisembargo(): boolean;
    setDisembargo(value: Disembargo): void;
    adoptObsoleteSave(value: capnp.Orphan<capnp.Pointer>): void;
    disownObsoleteSave(): capnp.Orphan<capnp.Pointer>;
    getObsoleteSave(): capnp.Pointer;
    hasObsoleteSave(): boolean;
    isObsoleteSave(): boolean;
    setObsoleteSave(value: capnp.Pointer): void;
    adoptObsoleteDelete(value: capnp.Orphan<capnp.Pointer>): void;
    disownObsoleteDelete(): capnp.Orphan<capnp.Pointer>;
    getObsoleteDelete(): capnp.Pointer;
    hasObsoleteDelete(): boolean;
    isObsoleteDelete(): boolean;
    setObsoleteDelete(value: capnp.Pointer): void;
    adoptProvide(value: capnp.Orphan<Provide>): void;
    disownProvide(): capnp.Orphan<Provide>;
    getProvide(): Provide;
    hasProvide(): boolean;
    initProvide(): Provide;
    isProvide(): boolean;
    setProvide(value: Provide): void;
    adoptAccept(value: capnp.Orphan<Accept>): void;
    disownAccept(): capnp.Orphan<Accept>;
    getAccept(): Accept;
    hasAccept(): boolean;
    initAccept(): Accept;
    isAccept(): boolean;
    setAccept(value: Accept): void;
    adoptJoin(value: capnp.Orphan<Join>): void;
    disownJoin(): capnp.Orphan<Join>;
    getJoin(): Join;
    hasJoin(): boolean;
    initJoin(): Join;
    isJoin(): boolean;
    setJoin(value: Join): void;
    toString(): string;
    which(): Message_Which;
}
export declare class Bootstrap extends __S {
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    getQuestionId(): number;
    setQuestionId(value: number): void;
    adoptDeprecatedObjectId(value: capnp.Orphan<capnp.Pointer>): void;
    disownDeprecatedObjectId(): capnp.Orphan<capnp.Pointer>;
    getDeprecatedObjectId(): capnp.Pointer;
    hasDeprecatedObjectId(): boolean;
    setDeprecatedObjectId(value: capnp.Pointer): void;
    toString(): string;
}
export declare enum Call_SendResultsTo_Which {
    CALLER = 0,
    YOURSELF = 1,
    THIRD_PARTY = 2
}
export declare class Call_SendResultsTo extends __S {
    static readonly CALLER = Call_SendResultsTo_Which.CALLER;
    static readonly YOURSELF = Call_SendResultsTo_Which.YOURSELF;
    static readonly THIRD_PARTY = Call_SendResultsTo_Which.THIRD_PARTY;
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    isCaller(): boolean;
    setCaller(): void;
    isYourself(): boolean;
    setYourself(): void;
    adoptThirdParty(value: capnp.Orphan<capnp.Pointer>): void;
    disownThirdParty(): capnp.Orphan<capnp.Pointer>;
    getThirdParty(): capnp.Pointer;
    hasThirdParty(): boolean;
    isThirdParty(): boolean;
    setThirdParty(value: capnp.Pointer): void;
    toString(): string;
    which(): Call_SendResultsTo_Which;
}
export declare class Call extends __S {
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
        defaultAllowThirdPartyTailCall: DataView;
    };
    getQuestionId(): number;
    setQuestionId(value: number): void;
    adoptTarget(value: capnp.Orphan<MessageTarget>): void;
    disownTarget(): capnp.Orphan<MessageTarget>;
    getTarget(): MessageTarget;
    hasTarget(): boolean;
    initTarget(): MessageTarget;
    setTarget(value: MessageTarget): void;
    getInterfaceId(): capnp.Uint64;
    setInterfaceId(value: capnp.Uint64): void;
    getMethodId(): number;
    setMethodId(value: number): void;
    getAllowThirdPartyTailCall(): boolean;
    setAllowThirdPartyTailCall(value: boolean): void;
    adoptParams(value: capnp.Orphan<Payload>): void;
    disownParams(): capnp.Orphan<Payload>;
    getParams(): Payload;
    hasParams(): boolean;
    initParams(): Payload;
    setParams(value: Payload): void;
    getSendResultsTo(): Call_SendResultsTo;
    initSendResultsTo(): Call_SendResultsTo;
    toString(): string;
}
export declare enum Return_Which {
    RESULTS = 0,
    EXCEPTION = 1,
    CANCELED = 2,
    RESULTS_SENT_ELSEWHERE = 3,
    TAKE_FROM_OTHER_QUESTION = 4,
    ACCEPT_FROM_THIRD_PARTY = 5
}
export declare class Return extends __S {
    static readonly RESULTS = Return_Which.RESULTS;
    static readonly EXCEPTION = Return_Which.EXCEPTION;
    static readonly CANCELED = Return_Which.CANCELED;
    static readonly RESULTS_SENT_ELSEWHERE = Return_Which.RESULTS_SENT_ELSEWHERE;
    static readonly TAKE_FROM_OTHER_QUESTION = Return_Which.TAKE_FROM_OTHER_QUESTION;
    static readonly ACCEPT_FROM_THIRD_PARTY = Return_Which.ACCEPT_FROM_THIRD_PARTY;
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
        defaultReleaseParamCaps: DataView;
    };
    getAnswerId(): number;
    setAnswerId(value: number): void;
    getReleaseParamCaps(): boolean;
    setReleaseParamCaps(value: boolean): void;
    adoptResults(value: capnp.Orphan<Payload>): void;
    disownResults(): capnp.Orphan<Payload>;
    getResults(): Payload;
    hasResults(): boolean;
    initResults(): Payload;
    isResults(): boolean;
    setResults(value: Payload): void;
    adoptException(value: capnp.Orphan<Exception>): void;
    disownException(): capnp.Orphan<Exception>;
    getException(): Exception;
    hasException(): boolean;
    initException(): Exception;
    isException(): boolean;
    setException(value: Exception): void;
    isCanceled(): boolean;
    setCanceled(): void;
    isResultsSentElsewhere(): boolean;
    setResultsSentElsewhere(): void;
    getTakeFromOtherQuestion(): number;
    isTakeFromOtherQuestion(): boolean;
    setTakeFromOtherQuestion(value: number): void;
    adoptAcceptFromThirdParty(value: capnp.Orphan<capnp.Pointer>): void;
    disownAcceptFromThirdParty(): capnp.Orphan<capnp.Pointer>;
    getAcceptFromThirdParty(): capnp.Pointer;
    hasAcceptFromThirdParty(): boolean;
    isAcceptFromThirdParty(): boolean;
    setAcceptFromThirdParty(value: capnp.Pointer): void;
    toString(): string;
    which(): Return_Which;
}
export declare class Finish extends __S {
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
        defaultReleaseResultCaps: DataView;
    };
    getQuestionId(): number;
    setQuestionId(value: number): void;
    getReleaseResultCaps(): boolean;
    setReleaseResultCaps(value: boolean): void;
    toString(): string;
}
export declare enum Resolve_Which {
    CAP = 0,
    EXCEPTION = 1
}
export declare class Resolve extends __S {
    static readonly CAP = Resolve_Which.CAP;
    static readonly EXCEPTION = Resolve_Which.EXCEPTION;
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    getPromiseId(): number;
    setPromiseId(value: number): void;
    adoptCap(value: capnp.Orphan<CapDescriptor>): void;
    disownCap(): capnp.Orphan<CapDescriptor>;
    getCap(): CapDescriptor;
    hasCap(): boolean;
    initCap(): CapDescriptor;
    isCap(): boolean;
    setCap(value: CapDescriptor): void;
    adoptException(value: capnp.Orphan<Exception>): void;
    disownException(): capnp.Orphan<Exception>;
    getException(): Exception;
    hasException(): boolean;
    initException(): Exception;
    isException(): boolean;
    setException(value: Exception): void;
    toString(): string;
    which(): Resolve_Which;
}
export declare class Release extends __S {
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    getId(): number;
    setId(value: number): void;
    getReferenceCount(): number;
    setReferenceCount(value: number): void;
    toString(): string;
}
export declare enum Disembargo_Context_Which {
    SENDER_LOOPBACK = 0,
    RECEIVER_LOOPBACK = 1,
    ACCEPT = 2,
    PROVIDE = 3
}
export declare class Disembargo_Context extends __S {
    static readonly SENDER_LOOPBACK = Disembargo_Context_Which.SENDER_LOOPBACK;
    static readonly RECEIVER_LOOPBACK = Disembargo_Context_Which.RECEIVER_LOOPBACK;
    static readonly ACCEPT = Disembargo_Context_Which.ACCEPT;
    static readonly PROVIDE = Disembargo_Context_Which.PROVIDE;
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    getSenderLoopback(): number;
    isSenderLoopback(): boolean;
    setSenderLoopback(value: number): void;
    getReceiverLoopback(): number;
    isReceiverLoopback(): boolean;
    setReceiverLoopback(value: number): void;
    isAccept(): boolean;
    setAccept(): void;
    getProvide(): number;
    isProvide(): boolean;
    setProvide(value: number): void;
    toString(): string;
    which(): Disembargo_Context_Which;
}
export declare class Disembargo extends __S {
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    adoptTarget(value: capnp.Orphan<MessageTarget>): void;
    disownTarget(): capnp.Orphan<MessageTarget>;
    getTarget(): MessageTarget;
    hasTarget(): boolean;
    initTarget(): MessageTarget;
    setTarget(value: MessageTarget): void;
    getContext(): Disembargo_Context;
    initContext(): Disembargo_Context;
    toString(): string;
}
export declare class Provide extends __S {
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    getQuestionId(): number;
    setQuestionId(value: number): void;
    adoptTarget(value: capnp.Orphan<MessageTarget>): void;
    disownTarget(): capnp.Orphan<MessageTarget>;
    getTarget(): MessageTarget;
    hasTarget(): boolean;
    initTarget(): MessageTarget;
    setTarget(value: MessageTarget): void;
    adoptRecipient(value: capnp.Orphan<capnp.Pointer>): void;
    disownRecipient(): capnp.Orphan<capnp.Pointer>;
    getRecipient(): capnp.Pointer;
    hasRecipient(): boolean;
    setRecipient(value: capnp.Pointer): void;
    toString(): string;
}
export declare class Accept extends __S {
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    getQuestionId(): number;
    setQuestionId(value: number): void;
    adoptProvision(value: capnp.Orphan<capnp.Pointer>): void;
    disownProvision(): capnp.Orphan<capnp.Pointer>;
    getProvision(): capnp.Pointer;
    hasProvision(): boolean;
    setProvision(value: capnp.Pointer): void;
    getEmbargo(): boolean;
    setEmbargo(value: boolean): void;
    toString(): string;
}
export declare class Join extends __S {
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    getQuestionId(): number;
    setQuestionId(value: number): void;
    adoptTarget(value: capnp.Orphan<MessageTarget>): void;
    disownTarget(): capnp.Orphan<MessageTarget>;
    getTarget(): MessageTarget;
    hasTarget(): boolean;
    initTarget(): MessageTarget;
    setTarget(value: MessageTarget): void;
    adoptKeyPart(value: capnp.Orphan<capnp.Pointer>): void;
    disownKeyPart(): capnp.Orphan<capnp.Pointer>;
    getKeyPart(): capnp.Pointer;
    hasKeyPart(): boolean;
    setKeyPart(value: capnp.Pointer): void;
    toString(): string;
}
export declare enum MessageTarget_Which {
    IMPORTED_CAP = 0,
    PROMISED_ANSWER = 1
}
export declare class MessageTarget extends __S {
    static readonly IMPORTED_CAP = MessageTarget_Which.IMPORTED_CAP;
    static readonly PROMISED_ANSWER = MessageTarget_Which.PROMISED_ANSWER;
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    getImportedCap(): number;
    isImportedCap(): boolean;
    setImportedCap(value: number): void;
    adoptPromisedAnswer(value: capnp.Orphan<PromisedAnswer>): void;
    disownPromisedAnswer(): capnp.Orphan<PromisedAnswer>;
    getPromisedAnswer(): PromisedAnswer;
    hasPromisedAnswer(): boolean;
    initPromisedAnswer(): PromisedAnswer;
    isPromisedAnswer(): boolean;
    setPromisedAnswer(value: PromisedAnswer): void;
    toString(): string;
    which(): MessageTarget_Which;
}
export declare class Payload extends __S {
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    static _CapTable: capnp.ListCtor<CapDescriptor>;
    adoptContent(value: capnp.Orphan<capnp.Pointer>): void;
    disownContent(): capnp.Orphan<capnp.Pointer>;
    getContent(): capnp.Pointer;
    hasContent(): boolean;
    setContent(value: capnp.Pointer): void;
    adoptCapTable(value: capnp.Orphan<capnp.List<CapDescriptor>>): void;
    disownCapTable(): capnp.Orphan<capnp.List<CapDescriptor>>;
    getCapTable(): capnp.List<CapDescriptor>;
    hasCapTable(): boolean;
    initCapTable(length: number): capnp.List<CapDescriptor>;
    setCapTable(value: capnp.List<CapDescriptor>): void;
    toString(): string;
}
export declare enum CapDescriptor_Which {
    NONE = 0,
    SENDER_HOSTED = 1,
    SENDER_PROMISE = 2,
    RECEIVER_HOSTED = 3,
    RECEIVER_ANSWER = 4,
    THIRD_PARTY_HOSTED = 5
}
export declare class CapDescriptor extends __S {
    static readonly NONE = CapDescriptor_Which.NONE;
    static readonly SENDER_HOSTED = CapDescriptor_Which.SENDER_HOSTED;
    static readonly SENDER_PROMISE = CapDescriptor_Which.SENDER_PROMISE;
    static readonly RECEIVER_HOSTED = CapDescriptor_Which.RECEIVER_HOSTED;
    static readonly RECEIVER_ANSWER = CapDescriptor_Which.RECEIVER_ANSWER;
    static readonly THIRD_PARTY_HOSTED = CapDescriptor_Which.THIRD_PARTY_HOSTED;
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    isNone(): boolean;
    setNone(): void;
    getSenderHosted(): number;
    isSenderHosted(): boolean;
    setSenderHosted(value: number): void;
    getSenderPromise(): number;
    isSenderPromise(): boolean;
    setSenderPromise(value: number): void;
    getReceiverHosted(): number;
    isReceiverHosted(): boolean;
    setReceiverHosted(value: number): void;
    adoptReceiverAnswer(value: capnp.Orphan<PromisedAnswer>): void;
    disownReceiverAnswer(): capnp.Orphan<PromisedAnswer>;
    getReceiverAnswer(): PromisedAnswer;
    hasReceiverAnswer(): boolean;
    initReceiverAnswer(): PromisedAnswer;
    isReceiverAnswer(): boolean;
    setReceiverAnswer(value: PromisedAnswer): void;
    adoptThirdPartyHosted(value: capnp.Orphan<ThirdPartyCapDescriptor>): void;
    disownThirdPartyHosted(): capnp.Orphan<ThirdPartyCapDescriptor>;
    getThirdPartyHosted(): ThirdPartyCapDescriptor;
    hasThirdPartyHosted(): boolean;
    initThirdPartyHosted(): ThirdPartyCapDescriptor;
    isThirdPartyHosted(): boolean;
    setThirdPartyHosted(value: ThirdPartyCapDescriptor): void;
    toString(): string;
    which(): CapDescriptor_Which;
}
export declare enum PromisedAnswer_Op_Which {
    NOOP = 0,
    GET_POINTER_FIELD = 1
}
export declare class PromisedAnswer_Op extends __S {
    static readonly NOOP = PromisedAnswer_Op_Which.NOOP;
    static readonly GET_POINTER_FIELD = PromisedAnswer_Op_Which.GET_POINTER_FIELD;
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    isNoop(): boolean;
    setNoop(): void;
    getGetPointerField(): number;
    isGetPointerField(): boolean;
    setGetPointerField(value: number): void;
    toString(): string;
    which(): PromisedAnswer_Op_Which;
}
export declare class PromisedAnswer extends __S {
    static readonly Op: typeof PromisedAnswer_Op;
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    static _Transform: capnp.ListCtor<PromisedAnswer_Op>;
    getQuestionId(): number;
    setQuestionId(value: number): void;
    adoptTransform(value: capnp.Orphan<capnp.List<PromisedAnswer_Op>>): void;
    disownTransform(): capnp.Orphan<capnp.List<PromisedAnswer_Op>>;
    getTransform(): capnp.List<PromisedAnswer_Op>;
    hasTransform(): boolean;
    initTransform(length: number): capnp.List<PromisedAnswer_Op>;
    setTransform(value: capnp.List<PromisedAnswer_Op>): void;
    toString(): string;
}
export declare class ThirdPartyCapDescriptor extends __S {
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    adoptId(value: capnp.Orphan<capnp.Pointer>): void;
    disownId(): capnp.Orphan<capnp.Pointer>;
    getId(): capnp.Pointer;
    hasId(): boolean;
    setId(value: capnp.Pointer): void;
    getVineId(): number;
    setVineId(value: number): void;
    toString(): string;
}
export declare enum Exception_Type {
    FAILED = 0,
    OVERLOADED = 1,
    DISCONNECTED = 2,
    UNIMPLEMENTED = 3
}
export declare class Exception extends __S {
    static readonly Type: typeof Exception_Type;
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    getReason(): string;
    setReason(value: string): void;
    getType(): Exception_Type;
    setType(value: Exception_Type): void;
    getObsoleteIsCallersFault(): boolean;
    setObsoleteIsCallersFault(value: boolean): void;
    getObsoleteDurability(): number;
    setObsoleteDurability(value: number): void;
    toString(): string;
}
