/**
 * This file is generated by hand in order to bootstrap compiler development. It is intended to be an exact match to
 * compiled output.
 */

import * as capnp from "../index";
import { ObjectSize as __O, Struct as __S } from "../index";
export const _capnpFileId = "b8630836983feed7";
export class Persistent_SaveParams extends __S {
    static readonly _capnp = { displayName: "SaveParams", id: "f76fba59183073a5", size: new __O(0, 1) };
    adoptSealFor(value: capnp.Orphan<capnp.Pointer>): void { __S.adopt(value, __S.getPointer(0, this)); }
    disownSealFor(): capnp.Orphan<capnp.Pointer> { return __S.disown(this.getSealFor()); }
    getSealFor(): capnp.Pointer { return __S.getPointer(0, this); }
    hasSealFor(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    setSealFor(value: capnp.Pointer): void { __S.copyFrom(value, __S.getPointer(0, this)); }
    toString(): string { return "Persistent_SaveParams_" + super.toString(); }
}
export class Persistent_SaveResults extends __S {
    static readonly _capnp = { displayName: "SaveResults", id: "b76848c18c40efbf", size: new __O(0, 1) };
    adoptSturdyRef(value: capnp.Orphan<capnp.Pointer>): void { __S.adopt(value, __S.getPointer(0, this)); }
    disownSturdyRef(): capnp.Orphan<capnp.Pointer> { return __S.disown(this.getSturdyRef()); }
    getSturdyRef(): capnp.Pointer { return __S.getPointer(0, this); }
    hasSturdyRef(): boolean { return !__S.isNull(__S.getPointer(0, this)); }
    setSturdyRef(value: capnp.Pointer): void { __S.copyFrom(value, __S.getPointer(0, this)); }
    toString(): string { return "Persistent_SaveResults_" + super.toString(); }
}
export class Persistent extends __S {
    static readonly SaveParams = Persistent_SaveParams;
    static readonly SaveResults = Persistent_SaveResults;
    static readonly _capnp = { displayName: "Persistent", id: "c8cb212fcd9f5691", size: new __O(0, 0) };
    toString(): string { return "Persistent_" + super.toString(); }
}
export class RealmGateway extends __S {
    static readonly _capnp = { displayName: "RealmGateway", id: "84ff286cd00a3ed4", size: new __O(0, 0) };
    toString(): string { return "RealmGateway_" + super.toString(); }
}
