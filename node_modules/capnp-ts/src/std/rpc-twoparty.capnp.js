"use strict";
/**
 * This file is generated by hand in order to bootstrap compiler development. It is intended to be an exact match to
 * compiled output.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.JoinResult = exports.JoinKeyPart = exports.ThirdPartyCapId = exports.RecipientId = exports.ProvisionId = exports.VatId = exports.Side = exports._capnpFileId = void 0;
const index_1 = require("../index");
exports._capnpFileId = "a184c7885cdaf2a1";
var Side;
(function (Side) {
    Side[Side["SERVER"] = 0] = "SERVER";
    Side[Side["CLIENT"] = 1] = "CLIENT";
})(Side = exports.Side || (exports.Side = {}));
class VatId extends index_1.Struct {
    getSide() { return index_1.Struct.getUint16(0, this); }
    setSide(value) { index_1.Struct.setUint16(0, value, this); }
    toString() { return "VatId_" + super.toString(); }
}
exports.VatId = VatId;
VatId._capnp = { displayName: "VatId", id: "d20b909fee733a8e", size: new index_1.ObjectSize(8, 0) };
class ProvisionId extends index_1.Struct {
    getJoinId() { return index_1.Struct.getUint32(0, this); }
    setJoinId(value) { index_1.Struct.setUint32(0, value, this); }
    toString() { return "ProvisionId_" + super.toString(); }
}
exports.ProvisionId = ProvisionId;
ProvisionId._capnp = { displayName: "ProvisionId", id: "b88d09a9c5f39817", size: new index_1.ObjectSize(8, 0) };
class RecipientId extends index_1.Struct {
    toString() { return "RecipientId_" + super.toString(); }
}
exports.RecipientId = RecipientId;
RecipientId._capnp = { displayName: "RecipientId", id: "89f389b6fd4082c1", size: new index_1.ObjectSize(0, 0) };
class ThirdPartyCapId extends index_1.Struct {
    toString() { return "ThirdPartyCapId_" + super.toString(); }
}
exports.ThirdPartyCapId = ThirdPartyCapId;
ThirdPartyCapId._capnp = { displayName: "ThirdPartyCapId", id: "b47f4979672cb59d", size: new index_1.ObjectSize(0, 0) };
class JoinKeyPart extends index_1.Struct {
    getJoinId() { return index_1.Struct.getUint32(0, this); }
    setJoinId(value) { index_1.Struct.setUint32(0, value, this); }
    getPartCount() { return index_1.Struct.getUint16(4, this); }
    setPartCount(value) { index_1.Struct.setUint16(4, value, this); }
    getPartNum() { return index_1.Struct.getUint16(6, this); }
    setPartNum(value) { index_1.Struct.setUint16(6, value, this); }
    toString() { return "JoinKeyPart_" + super.toString(); }
}
exports.JoinKeyPart = JoinKeyPart;
JoinKeyPart._capnp = { displayName: "JoinKeyPart", id: "95b29059097fca83", size: new index_1.ObjectSize(8, 0) };
class JoinResult extends index_1.Struct {
    getJoinId() { return index_1.Struct.getUint32(0, this); }
    setJoinId(value) { index_1.Struct.setUint32(0, value, this); }
    getSucceeded() { return index_1.Struct.getBit(32, this); }
    setSucceeded(value) { index_1.Struct.setBit(32, value, this); }
    adoptCap(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this)); }
    disownCap() { return index_1.Struct.disown(this.getCap()); }
    getCap() { return index_1.Struct.getPointer(0, this); }
    hasCap() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    setCap(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this)); }
    toString() { return "JoinResult_" + super.toString(); }
}
exports.JoinResult = JoinResult;
JoinResult._capnp = { displayName: "JoinResult", id: "9d263a3630b7ebee", size: new index_1.ObjectSize(8, 1) };
//# sourceMappingURL=rpc-twoparty.capnp.js.map