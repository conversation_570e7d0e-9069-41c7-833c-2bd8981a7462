{"version": 3, "file": "rpc-twoparty.capnp.js", "sourceRoot": "", "sources": ["rpc-twoparty.capnp.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAGH,oCAA4D;AAC/C,QAAA,YAAY,GAAG,kBAAkB,CAAC;AAC/C,IAAY,IAGX;AAHD,WAAY,IAAI;IACZ,mCAAM,CAAA;IACN,mCAAM,CAAA;AACV,CAAC,EAHW,IAAI,GAAJ,YAAI,KAAJ,YAAI,QAGf;AACD,MAAa,KAAM,SAAQ,cAAG;IAE1B,OAAO,KAAW,OAAO,cAAG,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IAClD,OAAO,CAAC,KAAW,IAAU,cAAG,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IAC7D,QAAQ,KAAa,OAAO,QAAQ,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;;AAJ9D,sBAKC;AAJmB,YAAM,GAAG,EAAE,WAAW,EAAE,OAAO,EAAE,EAAE,EAAE,kBAAkB,EAAE,IAAI,EAAE,IAAI,kBAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;AAKnG,MAAa,WAAY,SAAQ,cAAG;IAEhC,SAAS,KAAa,OAAO,cAAG,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IACtD,SAAS,CAAC,KAAa,IAAU,cAAG,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IACjE,QAAQ,KAAa,OAAO,cAAc,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;;AAJpE,kCAKC;AAJmB,kBAAM,GAAG,EAAE,WAAW,EAAE,aAAa,EAAE,EAAE,EAAE,kBAAkB,EAAE,IAAI,EAAE,IAAI,kBAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;AAKzG,MAAa,WAAY,SAAQ,cAAG;IAEhC,QAAQ,KAAa,OAAO,cAAc,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;;AAFpE,kCAGC;AAFmB,kBAAM,GAAG,EAAE,WAAW,EAAE,aAAa,EAAE,EAAE,EAAE,kBAAkB,EAAE,IAAI,EAAE,IAAI,kBAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;AAGzG,MAAa,eAAgB,SAAQ,cAAG;IAEpC,QAAQ,KAAa,OAAO,kBAAkB,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;;AAFxE,0CAGC;AAFmB,sBAAM,GAAG,EAAE,WAAW,EAAE,iBAAiB,EAAE,EAAE,EAAE,kBAAkB,EAAE,IAAI,EAAE,IAAI,kBAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;AAG7G,MAAa,WAAY,SAAQ,cAAG;IAEhC,SAAS,KAAa,OAAO,cAAG,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IACtD,SAAS,CAAC,KAAa,IAAU,cAAG,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IACjE,YAAY,KAAa,OAAO,cAAG,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IACzD,YAAY,CAAC,KAAa,IAAU,cAAG,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IACpE,UAAU,KAAa,OAAO,cAAG,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IACvD,UAAU,CAAC,KAAa,IAAU,cAAG,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IAClE,QAAQ,KAAa,OAAO,cAAc,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;;AARpE,kCASC;AARmB,kBAAM,GAAG,EAAE,WAAW,EAAE,aAAa,EAAE,EAAE,EAAE,kBAAkB,EAAE,IAAI,EAAE,IAAI,kBAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;AASzG,MAAa,UAAW,SAAQ,cAAG;IAE/B,SAAS,KAAa,OAAO,cAAG,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IACtD,SAAS,CAAC,KAAa,IAAU,cAAG,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IACjE,YAAY,KAAc,OAAO,cAAG,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IACxD,YAAY,CAAC,KAAc,IAAU,cAAG,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IACnE,QAAQ,CAAC,KAAkC,IAAU,cAAG,CAAC,KAAK,CAAC,KAAK,EAAE,cAAG,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACjG,SAAS,KAAkC,OAAO,cAAG,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;IAC9E,MAAM,KAAoB,OAAO,cAAG,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IAC3D,MAAM,KAAc,OAAO,CAAC,cAAG,CAAC,MAAM,CAAC,cAAG,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAClE,MAAM,CAAC,KAAoB,IAAU,cAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,cAAG,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACpF,QAAQ,KAAa,OAAO,aAAa,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;;AAXnE,gCAYC;AAXmB,iBAAM,GAAG,EAAE,WAAW,EAAE,YAAY,EAAE,EAAE,EAAE,kBAAkB,EAAE,IAAI,EAAE,IAAI,kBAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC"}