"use strict";
/**
 * This file is generated by hand in order to bootstrap compiler development. It is intended to be an exact match to
 * compiled output.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Exception = exports.Exception_Type = exports.ThirdPartyCapDescriptor = exports.PromisedAnswer = exports.PromisedAnswer_Op = exports.PromisedAnswer_Op_Which = exports.CapDescriptor = exports.CapDescriptor_Which = exports.Payload = exports.MessageTarget = exports.MessageTarget_Which = exports.Join = exports.Accept = exports.Provide = exports.Disembargo = exports.Disembargo_Context = exports.Disembargo_Context_Which = exports.Release = exports.Resolve = exports.Resolve_Which = exports.Finish = exports.Return = exports.Return_Which = exports.Call = exports.Call_SendResultsTo = exports.Call_SendResultsTo_Which = exports.Bootstrap = exports.Message = exports.Message_Which = exports._capnpFileId = void 0;
const tslib_1 = require("tslib");
const capnp = tslib_1.__importStar(require("../index"));
const index_1 = require("../index");
exports._capnpFileId = "b312981b2552a250";
var Message_Which;
(function (Message_Which) {
    Message_Which[Message_Which["UNIMPLEMENTED"] = 0] = "UNIMPLEMENTED";
    Message_Which[Message_Which["ABORT"] = 1] = "ABORT";
    Message_Which[Message_Which["BOOTSTRAP"] = 8] = "BOOTSTRAP";
    Message_Which[Message_Which["CALL"] = 2] = "CALL";
    Message_Which[Message_Which["RETURN"] = 3] = "RETURN";
    Message_Which[Message_Which["FINISH"] = 4] = "FINISH";
    Message_Which[Message_Which["RESOLVE"] = 5] = "RESOLVE";
    Message_Which[Message_Which["RELEASE"] = 6] = "RELEASE";
    Message_Which[Message_Which["DISEMBARGO"] = 13] = "DISEMBARGO";
    Message_Which[Message_Which["OBSOLETE_SAVE"] = 7] = "OBSOLETE_SAVE";
    Message_Which[Message_Which["OBSOLETE_DELETE"] = 9] = "OBSOLETE_DELETE";
    Message_Which[Message_Which["PROVIDE"] = 10] = "PROVIDE";
    Message_Which[Message_Which["ACCEPT"] = 11] = "ACCEPT";
    Message_Which[Message_Which["JOIN"] = 12] = "JOIN";
})(Message_Which = exports.Message_Which || (exports.Message_Which = {}));
class Message extends index_1.Struct {
    adoptUnimplemented(value) {
        index_1.Struct.setUint16(0, 0, this);
        index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this));
    }
    disownUnimplemented() { return index_1.Struct.disown(this.getUnimplemented()); }
    getUnimplemented() {
        index_1.Struct.testWhich("unimplemented", index_1.Struct.getUint16(0, this), 0, this);
        return index_1.Struct.getStruct(0, Message, this);
    }
    hasUnimplemented() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initUnimplemented() {
        index_1.Struct.setUint16(0, 0, this);
        return index_1.Struct.initStructAt(0, Message, this);
    }
    isUnimplemented() { return index_1.Struct.getUint16(0, this) === 0; }
    setUnimplemented(value) {
        index_1.Struct.setUint16(0, 0, this);
        index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this));
    }
    adoptAbort(value) {
        index_1.Struct.setUint16(0, 1, this);
        index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this));
    }
    disownAbort() { return index_1.Struct.disown(this.getAbort()); }
    getAbort() {
        index_1.Struct.testWhich("abort", index_1.Struct.getUint16(0, this), 1, this);
        return index_1.Struct.getStruct(0, Exception, this);
    }
    hasAbort() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initAbort() {
        index_1.Struct.setUint16(0, 1, this);
        return index_1.Struct.initStructAt(0, Exception, this);
    }
    isAbort() { return index_1.Struct.getUint16(0, this) === 1; }
    setAbort(value) {
        index_1.Struct.setUint16(0, 1, this);
        index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this));
    }
    adoptBootstrap(value) {
        index_1.Struct.setUint16(0, 8, this);
        index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this));
    }
    disownBootstrap() { return index_1.Struct.disown(this.getBootstrap()); }
    getBootstrap() {
        index_1.Struct.testWhich("bootstrap", index_1.Struct.getUint16(0, this), 8, this);
        return index_1.Struct.getStruct(0, Bootstrap, this);
    }
    hasBootstrap() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initBootstrap() {
        index_1.Struct.setUint16(0, 8, this);
        return index_1.Struct.initStructAt(0, Bootstrap, this);
    }
    isBootstrap() { return index_1.Struct.getUint16(0, this) === 8; }
    setBootstrap(value) {
        index_1.Struct.setUint16(0, 8, this);
        index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this));
    }
    adoptCall(value) {
        index_1.Struct.setUint16(0, 2, this);
        index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this));
    }
    disownCall() { return index_1.Struct.disown(this.getCall()); }
    getCall() {
        index_1.Struct.testWhich("call", index_1.Struct.getUint16(0, this), 2, this);
        return index_1.Struct.getStruct(0, Call, this);
    }
    hasCall() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initCall() {
        index_1.Struct.setUint16(0, 2, this);
        return index_1.Struct.initStructAt(0, Call, this);
    }
    isCall() { return index_1.Struct.getUint16(0, this) === 2; }
    setCall(value) {
        index_1.Struct.setUint16(0, 2, this);
        index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this));
    }
    adoptReturn(value) {
        index_1.Struct.setUint16(0, 3, this);
        index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this));
    }
    disownReturn() { return index_1.Struct.disown(this.getReturn()); }
    getReturn() {
        index_1.Struct.testWhich("return", index_1.Struct.getUint16(0, this), 3, this);
        return index_1.Struct.getStruct(0, Return, this);
    }
    hasReturn() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initReturn() {
        index_1.Struct.setUint16(0, 3, this);
        return index_1.Struct.initStructAt(0, Return, this);
    }
    isReturn() { return index_1.Struct.getUint16(0, this) === 3; }
    setReturn(value) {
        index_1.Struct.setUint16(0, 3, this);
        index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this));
    }
    adoptFinish(value) {
        index_1.Struct.setUint16(0, 4, this);
        index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this));
    }
    disownFinish() { return index_1.Struct.disown(this.getFinish()); }
    getFinish() {
        index_1.Struct.testWhich("finish", index_1.Struct.getUint16(0, this), 4, this);
        return index_1.Struct.getStruct(0, Finish, this);
    }
    hasFinish() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initFinish() {
        index_1.Struct.setUint16(0, 4, this);
        return index_1.Struct.initStructAt(0, Finish, this);
    }
    isFinish() { return index_1.Struct.getUint16(0, this) === 4; }
    setFinish(value) {
        index_1.Struct.setUint16(0, 4, this);
        index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this));
    }
    adoptResolve(value) {
        index_1.Struct.setUint16(0, 5, this);
        index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this));
    }
    disownResolve() { return index_1.Struct.disown(this.getResolve()); }
    getResolve() {
        index_1.Struct.testWhich("resolve", index_1.Struct.getUint16(0, this), 5, this);
        return index_1.Struct.getStruct(0, Resolve, this);
    }
    hasResolve() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initResolve() {
        index_1.Struct.setUint16(0, 5, this);
        return index_1.Struct.initStructAt(0, Resolve, this);
    }
    isResolve() { return index_1.Struct.getUint16(0, this) === 5; }
    setResolve(value) {
        index_1.Struct.setUint16(0, 5, this);
        index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this));
    }
    adoptRelease(value) {
        index_1.Struct.setUint16(0, 6, this);
        index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this));
    }
    disownRelease() { return index_1.Struct.disown(this.getRelease()); }
    getRelease() {
        index_1.Struct.testWhich("release", index_1.Struct.getUint16(0, this), 6, this);
        return index_1.Struct.getStruct(0, Release, this);
    }
    hasRelease() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initRelease() {
        index_1.Struct.setUint16(0, 6, this);
        return index_1.Struct.initStructAt(0, Release, this);
    }
    isRelease() { return index_1.Struct.getUint16(0, this) === 6; }
    setRelease(value) {
        index_1.Struct.setUint16(0, 6, this);
        index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this));
    }
    adoptDisembargo(value) {
        index_1.Struct.setUint16(0, 13, this);
        index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this));
    }
    disownDisembargo() { return index_1.Struct.disown(this.getDisembargo()); }
    getDisembargo() {
        index_1.Struct.testWhich("disembargo", index_1.Struct.getUint16(0, this), 13, this);
        return index_1.Struct.getStruct(0, Disembargo, this);
    }
    hasDisembargo() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initDisembargo() {
        index_1.Struct.setUint16(0, 13, this);
        return index_1.Struct.initStructAt(0, Disembargo, this);
    }
    isDisembargo() { return index_1.Struct.getUint16(0, this) === 13; }
    setDisembargo(value) {
        index_1.Struct.setUint16(0, 13, this);
        index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this));
    }
    adoptObsoleteSave(value) {
        index_1.Struct.setUint16(0, 7, this);
        index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this));
    }
    disownObsoleteSave() { return index_1.Struct.disown(this.getObsoleteSave()); }
    getObsoleteSave() {
        index_1.Struct.testWhich("obsoleteSave", index_1.Struct.getUint16(0, this), 7, this);
        return index_1.Struct.getPointer(0, this);
    }
    hasObsoleteSave() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    isObsoleteSave() { return index_1.Struct.getUint16(0, this) === 7; }
    setObsoleteSave(value) {
        index_1.Struct.setUint16(0, 7, this);
        index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this));
    }
    adoptObsoleteDelete(value) {
        index_1.Struct.setUint16(0, 9, this);
        index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this));
    }
    disownObsoleteDelete() { return index_1.Struct.disown(this.getObsoleteDelete()); }
    getObsoleteDelete() {
        index_1.Struct.testWhich("obsoleteDelete", index_1.Struct.getUint16(0, this), 9, this);
        return index_1.Struct.getPointer(0, this);
    }
    hasObsoleteDelete() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    isObsoleteDelete() { return index_1.Struct.getUint16(0, this) === 9; }
    setObsoleteDelete(value) {
        index_1.Struct.setUint16(0, 9, this);
        index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this));
    }
    adoptProvide(value) {
        index_1.Struct.setUint16(0, 10, this);
        index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this));
    }
    disownProvide() { return index_1.Struct.disown(this.getProvide()); }
    getProvide() {
        index_1.Struct.testWhich("provide", index_1.Struct.getUint16(0, this), 10, this);
        return index_1.Struct.getStruct(0, Provide, this);
    }
    hasProvide() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initProvide() {
        index_1.Struct.setUint16(0, 10, this);
        return index_1.Struct.initStructAt(0, Provide, this);
    }
    isProvide() { return index_1.Struct.getUint16(0, this) === 10; }
    setProvide(value) {
        index_1.Struct.setUint16(0, 10, this);
        index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this));
    }
    adoptAccept(value) {
        index_1.Struct.setUint16(0, 11, this);
        index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this));
    }
    disownAccept() { return index_1.Struct.disown(this.getAccept()); }
    getAccept() {
        index_1.Struct.testWhich("accept", index_1.Struct.getUint16(0, this), 11, this);
        return index_1.Struct.getStruct(0, Accept, this);
    }
    hasAccept() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initAccept() {
        index_1.Struct.setUint16(0, 11, this);
        return index_1.Struct.initStructAt(0, Accept, this);
    }
    isAccept() { return index_1.Struct.getUint16(0, this) === 11; }
    setAccept(value) {
        index_1.Struct.setUint16(0, 11, this);
        index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this));
    }
    adoptJoin(value) {
        index_1.Struct.setUint16(0, 12, this);
        index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this));
    }
    disownJoin() { return index_1.Struct.disown(this.getJoin()); }
    getJoin() {
        index_1.Struct.testWhich("join", index_1.Struct.getUint16(0, this), 12, this);
        return index_1.Struct.getStruct(0, Join, this);
    }
    hasJoin() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initJoin() {
        index_1.Struct.setUint16(0, 12, this);
        return index_1.Struct.initStructAt(0, Join, this);
    }
    isJoin() { return index_1.Struct.getUint16(0, this) === 12; }
    setJoin(value) {
        index_1.Struct.setUint16(0, 12, this);
        index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this));
    }
    toString() { return "Message_" + super.toString(); }
    which() { return index_1.Struct.getUint16(0, this); }
}
exports.Message = Message;
Message.UNIMPLEMENTED = Message_Which.UNIMPLEMENTED;
Message.ABORT = Message_Which.ABORT;
Message.BOOTSTRAP = Message_Which.BOOTSTRAP;
Message.CALL = Message_Which.CALL;
Message.RETURN = Message_Which.RETURN;
Message.FINISH = Message_Which.FINISH;
Message.RESOLVE = Message_Which.RESOLVE;
Message.RELEASE = Message_Which.RELEASE;
Message.DISEMBARGO = Message_Which.DISEMBARGO;
Message.OBSOLETE_SAVE = Message_Which.OBSOLETE_SAVE;
Message.OBSOLETE_DELETE = Message_Which.OBSOLETE_DELETE;
Message.PROVIDE = Message_Which.PROVIDE;
Message.ACCEPT = Message_Which.ACCEPT;
Message.JOIN = Message_Which.JOIN;
Message._capnp = { displayName: "Message", id: "91b79f1f808db032", size: new index_1.ObjectSize(8, 1) };
class Bootstrap extends index_1.Struct {
    getQuestionId() { return index_1.Struct.getUint32(0, this); }
    setQuestionId(value) { index_1.Struct.setUint32(0, value, this); }
    adoptDeprecatedObjectId(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this)); }
    disownDeprecatedObjectId() { return index_1.Struct.disown(this.getDeprecatedObjectId()); }
    getDeprecatedObjectId() { return index_1.Struct.getPointer(0, this); }
    hasDeprecatedObjectId() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    setDeprecatedObjectId(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this)); }
    toString() { return "Bootstrap_" + super.toString(); }
}
exports.Bootstrap = Bootstrap;
Bootstrap._capnp = { displayName: "Bootstrap", id: "e94ccf8031176ec4", size: new index_1.ObjectSize(8, 1) };
var Call_SendResultsTo_Which;
(function (Call_SendResultsTo_Which) {
    Call_SendResultsTo_Which[Call_SendResultsTo_Which["CALLER"] = 0] = "CALLER";
    Call_SendResultsTo_Which[Call_SendResultsTo_Which["YOURSELF"] = 1] = "YOURSELF";
    Call_SendResultsTo_Which[Call_SendResultsTo_Which["THIRD_PARTY"] = 2] = "THIRD_PARTY";
})(Call_SendResultsTo_Which = exports.Call_SendResultsTo_Which || (exports.Call_SendResultsTo_Which = {}));
class Call_SendResultsTo extends index_1.Struct {
    isCaller() { return index_1.Struct.getUint16(6, this) === 0; }
    setCaller() { index_1.Struct.setUint16(6, 0, this); }
    isYourself() { return index_1.Struct.getUint16(6, this) === 1; }
    setYourself() { index_1.Struct.setUint16(6, 1, this); }
    adoptThirdParty(value) {
        index_1.Struct.setUint16(6, 2, this);
        index_1.Struct.adopt(value, index_1.Struct.getPointer(2, this));
    }
    disownThirdParty() { return index_1.Struct.disown(this.getThirdParty()); }
    getThirdParty() {
        index_1.Struct.testWhich("thirdParty", index_1.Struct.getUint16(6, this), 2, this);
        return index_1.Struct.getPointer(2, this);
    }
    hasThirdParty() { return !index_1.Struct.isNull(index_1.Struct.getPointer(2, this)); }
    isThirdParty() { return index_1.Struct.getUint16(6, this) === 2; }
    setThirdParty(value) {
        index_1.Struct.setUint16(6, 2, this);
        index_1.Struct.copyFrom(value, index_1.Struct.getPointer(2, this));
    }
    toString() { return "Call_SendResultsTo_" + super.toString(); }
    which() { return index_1.Struct.getUint16(6, this); }
}
exports.Call_SendResultsTo = Call_SendResultsTo;
Call_SendResultsTo.CALLER = Call_SendResultsTo_Which.CALLER;
Call_SendResultsTo.YOURSELF = Call_SendResultsTo_Which.YOURSELF;
Call_SendResultsTo.THIRD_PARTY = Call_SendResultsTo_Which.THIRD_PARTY;
Call_SendResultsTo._capnp = { displayName: "sendResultsTo", id: "dae8b0f61aab5f99", size: new index_1.ObjectSize(24, 3) };
class Call extends index_1.Struct {
    getQuestionId() { return index_1.Struct.getUint32(0, this); }
    setQuestionId(value) { index_1.Struct.setUint32(0, value, this); }
    adoptTarget(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this)); }
    disownTarget() { return index_1.Struct.disown(this.getTarget()); }
    getTarget() { return index_1.Struct.getStruct(0, MessageTarget, this); }
    hasTarget() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initTarget() { return index_1.Struct.initStructAt(0, MessageTarget, this); }
    setTarget(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this)); }
    getInterfaceId() { return index_1.Struct.getUint64(8, this); }
    setInterfaceId(value) { index_1.Struct.setUint64(8, value, this); }
    getMethodId() { return index_1.Struct.getUint16(4, this); }
    setMethodId(value) { index_1.Struct.setUint16(4, value, this); }
    getAllowThirdPartyTailCall() { return index_1.Struct.getBit(128, this, Call._capnp.defaultAllowThirdPartyTailCall); }
    setAllowThirdPartyTailCall(value) { index_1.Struct.setBit(128, value, this); }
    adoptParams(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(1, this)); }
    disownParams() { return index_1.Struct.disown(this.getParams()); }
    getParams() { return index_1.Struct.getStruct(1, Payload, this); }
    hasParams() { return !index_1.Struct.isNull(index_1.Struct.getPointer(1, this)); }
    initParams() { return index_1.Struct.initStructAt(1, Payload, this); }
    setParams(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(1, this)); }
    getSendResultsTo() { return index_1.Struct.getAs(Call_SendResultsTo, this); }
    initSendResultsTo() { return index_1.Struct.getAs(Call_SendResultsTo, this); }
    toString() { return "Call_" + super.toString(); }
}
exports.Call = Call;
Call._capnp = { displayName: "Call", id: "836a53ce789d4cd4", size: new index_1.ObjectSize(24, 3), defaultAllowThirdPartyTailCall: capnp.getBitMask(false, 0) };
var Return_Which;
(function (Return_Which) {
    Return_Which[Return_Which["RESULTS"] = 0] = "RESULTS";
    Return_Which[Return_Which["EXCEPTION"] = 1] = "EXCEPTION";
    Return_Which[Return_Which["CANCELED"] = 2] = "CANCELED";
    Return_Which[Return_Which["RESULTS_SENT_ELSEWHERE"] = 3] = "RESULTS_SENT_ELSEWHERE";
    Return_Which[Return_Which["TAKE_FROM_OTHER_QUESTION"] = 4] = "TAKE_FROM_OTHER_QUESTION";
    Return_Which[Return_Which["ACCEPT_FROM_THIRD_PARTY"] = 5] = "ACCEPT_FROM_THIRD_PARTY";
})(Return_Which = exports.Return_Which || (exports.Return_Which = {}));
class Return extends index_1.Struct {
    getAnswerId() { return index_1.Struct.getUint32(0, this); }
    setAnswerId(value) { index_1.Struct.setUint32(0, value, this); }
    getReleaseParamCaps() { return index_1.Struct.getBit(32, this, Return._capnp.defaultReleaseParamCaps); }
    setReleaseParamCaps(value) { index_1.Struct.setBit(32, value, this); }
    adoptResults(value) {
        index_1.Struct.setUint16(6, 0, this);
        index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this));
    }
    disownResults() { return index_1.Struct.disown(this.getResults()); }
    getResults() {
        index_1.Struct.testWhich("results", index_1.Struct.getUint16(6, this), 0, this);
        return index_1.Struct.getStruct(0, Payload, this);
    }
    hasResults() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initResults() {
        index_1.Struct.setUint16(6, 0, this);
        return index_1.Struct.initStructAt(0, Payload, this);
    }
    isResults() { return index_1.Struct.getUint16(6, this) === 0; }
    setResults(value) {
        index_1.Struct.setUint16(6, 0, this);
        index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this));
    }
    adoptException(value) {
        index_1.Struct.setUint16(6, 1, this);
        index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this));
    }
    disownException() { return index_1.Struct.disown(this.getException()); }
    getException() {
        index_1.Struct.testWhich("exception", index_1.Struct.getUint16(6, this), 1, this);
        return index_1.Struct.getStruct(0, Exception, this);
    }
    hasException() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initException() {
        index_1.Struct.setUint16(6, 1, this);
        return index_1.Struct.initStructAt(0, Exception, this);
    }
    isException() { return index_1.Struct.getUint16(6, this) === 1; }
    setException(value) {
        index_1.Struct.setUint16(6, 1, this);
        index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this));
    }
    isCanceled() { return index_1.Struct.getUint16(6, this) === 2; }
    setCanceled() { index_1.Struct.setUint16(6, 2, this); }
    isResultsSentElsewhere() { return index_1.Struct.getUint16(6, this) === 3; }
    setResultsSentElsewhere() { index_1.Struct.setUint16(6, 3, this); }
    getTakeFromOtherQuestion() {
        index_1.Struct.testWhich("takeFromOtherQuestion", index_1.Struct.getUint16(6, this), 4, this);
        return index_1.Struct.getUint32(8, this);
    }
    isTakeFromOtherQuestion() { return index_1.Struct.getUint16(6, this) === 4; }
    setTakeFromOtherQuestion(value) {
        index_1.Struct.setUint16(6, 4, this);
        index_1.Struct.setUint32(8, value, this);
    }
    adoptAcceptFromThirdParty(value) {
        index_1.Struct.setUint16(6, 5, this);
        index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this));
    }
    disownAcceptFromThirdParty() { return index_1.Struct.disown(this.getAcceptFromThirdParty()); }
    getAcceptFromThirdParty() {
        index_1.Struct.testWhich("acceptFromThirdParty", index_1.Struct.getUint16(6, this), 5, this);
        return index_1.Struct.getPointer(0, this);
    }
    hasAcceptFromThirdParty() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    isAcceptFromThirdParty() { return index_1.Struct.getUint16(6, this) === 5; }
    setAcceptFromThirdParty(value) {
        index_1.Struct.setUint16(6, 5, this);
        index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this));
    }
    toString() { return "Return_" + super.toString(); }
    which() { return index_1.Struct.getUint16(6, this); }
}
exports.Return = Return;
Return.RESULTS = Return_Which.RESULTS;
Return.EXCEPTION = Return_Which.EXCEPTION;
Return.CANCELED = Return_Which.CANCELED;
Return.RESULTS_SENT_ELSEWHERE = Return_Which.RESULTS_SENT_ELSEWHERE;
Return.TAKE_FROM_OTHER_QUESTION = Return_Which.TAKE_FROM_OTHER_QUESTION;
Return.ACCEPT_FROM_THIRD_PARTY = Return_Which.ACCEPT_FROM_THIRD_PARTY;
Return._capnp = { displayName: "Return", id: "9e19b28d3db3573a", size: new index_1.ObjectSize(16, 1), defaultReleaseParamCaps: capnp.getBitMask(true, 0) };
class Finish extends index_1.Struct {
    getQuestionId() { return index_1.Struct.getUint32(0, this); }
    setQuestionId(value) { index_1.Struct.setUint32(0, value, this); }
    getReleaseResultCaps() { return index_1.Struct.getBit(32, this, Finish._capnp.defaultReleaseResultCaps); }
    setReleaseResultCaps(value) { index_1.Struct.setBit(32, value, this); }
    toString() { return "Finish_" + super.toString(); }
}
exports.Finish = Finish;
Finish._capnp = { displayName: "Finish", id: "d37d2eb2c2f80e63", size: new index_1.ObjectSize(8, 0), defaultReleaseResultCaps: capnp.getBitMask(true, 0) };
var Resolve_Which;
(function (Resolve_Which) {
    Resolve_Which[Resolve_Which["CAP"] = 0] = "CAP";
    Resolve_Which[Resolve_Which["EXCEPTION"] = 1] = "EXCEPTION";
})(Resolve_Which = exports.Resolve_Which || (exports.Resolve_Which = {}));
class Resolve extends index_1.Struct {
    getPromiseId() { return index_1.Struct.getUint32(0, this); }
    setPromiseId(value) { index_1.Struct.setUint32(0, value, this); }
    adoptCap(value) {
        index_1.Struct.setUint16(4, 0, this);
        index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this));
    }
    disownCap() { return index_1.Struct.disown(this.getCap()); }
    getCap() {
        index_1.Struct.testWhich("cap", index_1.Struct.getUint16(4, this), 0, this);
        return index_1.Struct.getStruct(0, CapDescriptor, this);
    }
    hasCap() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initCap() {
        index_1.Struct.setUint16(4, 0, this);
        return index_1.Struct.initStructAt(0, CapDescriptor, this);
    }
    isCap() { return index_1.Struct.getUint16(4, this) === 0; }
    setCap(value) {
        index_1.Struct.setUint16(4, 0, this);
        index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this));
    }
    adoptException(value) {
        index_1.Struct.setUint16(4, 1, this);
        index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this));
    }
    disownException() { return index_1.Struct.disown(this.getException()); }
    getException() {
        index_1.Struct.testWhich("exception", index_1.Struct.getUint16(4, this), 1, this);
        return index_1.Struct.getStruct(0, Exception, this);
    }
    hasException() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initException() {
        index_1.Struct.setUint16(4, 1, this);
        return index_1.Struct.initStructAt(0, Exception, this);
    }
    isException() { return index_1.Struct.getUint16(4, this) === 1; }
    setException(value) {
        index_1.Struct.setUint16(4, 1, this);
        index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this));
    }
    toString() { return "Resolve_" + super.toString(); }
    which() { return index_1.Struct.getUint16(4, this); }
}
exports.Resolve = Resolve;
Resolve.CAP = Resolve_Which.CAP;
Resolve.EXCEPTION = Resolve_Which.EXCEPTION;
Resolve._capnp = { displayName: "Resolve", id: "bbc29655fa89086e", size: new index_1.ObjectSize(8, 1) };
class Release extends index_1.Struct {
    getId() { return index_1.Struct.getUint32(0, this); }
    setId(value) { index_1.Struct.setUint32(0, value, this); }
    getReferenceCount() { return index_1.Struct.getUint32(4, this); }
    setReferenceCount(value) { index_1.Struct.setUint32(4, value, this); }
    toString() { return "Release_" + super.toString(); }
}
exports.Release = Release;
Release._capnp = { displayName: "Release", id: "ad1a6c0d7dd07497", size: new index_1.ObjectSize(8, 0) };
var Disembargo_Context_Which;
(function (Disembargo_Context_Which) {
    Disembargo_Context_Which[Disembargo_Context_Which["SENDER_LOOPBACK"] = 0] = "SENDER_LOOPBACK";
    Disembargo_Context_Which[Disembargo_Context_Which["RECEIVER_LOOPBACK"] = 1] = "RECEIVER_LOOPBACK";
    Disembargo_Context_Which[Disembargo_Context_Which["ACCEPT"] = 2] = "ACCEPT";
    Disembargo_Context_Which[Disembargo_Context_Which["PROVIDE"] = 3] = "PROVIDE";
})(Disembargo_Context_Which = exports.Disembargo_Context_Which || (exports.Disembargo_Context_Which = {}));
class Disembargo_Context extends index_1.Struct {
    getSenderLoopback() {
        index_1.Struct.testWhich("senderLoopback", index_1.Struct.getUint16(4, this), 0, this);
        return index_1.Struct.getUint32(0, this);
    }
    isSenderLoopback() { return index_1.Struct.getUint16(4, this) === 0; }
    setSenderLoopback(value) {
        index_1.Struct.setUint16(4, 0, this);
        index_1.Struct.setUint32(0, value, this);
    }
    getReceiverLoopback() {
        index_1.Struct.testWhich("receiverLoopback", index_1.Struct.getUint16(4, this), 1, this);
        return index_1.Struct.getUint32(0, this);
    }
    isReceiverLoopback() { return index_1.Struct.getUint16(4, this) === 1; }
    setReceiverLoopback(value) {
        index_1.Struct.setUint16(4, 1, this);
        index_1.Struct.setUint32(0, value, this);
    }
    isAccept() { return index_1.Struct.getUint16(4, this) === 2; }
    setAccept() { index_1.Struct.setUint16(4, 2, this); }
    getProvide() {
        index_1.Struct.testWhich("provide", index_1.Struct.getUint16(4, this), 3, this);
        return index_1.Struct.getUint32(0, this);
    }
    isProvide() { return index_1.Struct.getUint16(4, this) === 3; }
    setProvide(value) {
        index_1.Struct.setUint16(4, 3, this);
        index_1.Struct.setUint32(0, value, this);
    }
    toString() { return "Disembargo_Context_" + super.toString(); }
    which() { return index_1.Struct.getUint16(4, this); }
}
exports.Disembargo_Context = Disembargo_Context;
Disembargo_Context.SENDER_LOOPBACK = Disembargo_Context_Which.SENDER_LOOPBACK;
Disembargo_Context.RECEIVER_LOOPBACK = Disembargo_Context_Which.RECEIVER_LOOPBACK;
Disembargo_Context.ACCEPT = Disembargo_Context_Which.ACCEPT;
Disembargo_Context.PROVIDE = Disembargo_Context_Which.PROVIDE;
Disembargo_Context._capnp = { displayName: "context", id: "d562b4df655bdd4d", size: new index_1.ObjectSize(8, 1) };
class Disembargo extends index_1.Struct {
    adoptTarget(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this)); }
    disownTarget() { return index_1.Struct.disown(this.getTarget()); }
    getTarget() { return index_1.Struct.getStruct(0, MessageTarget, this); }
    hasTarget() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initTarget() { return index_1.Struct.initStructAt(0, MessageTarget, this); }
    setTarget(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this)); }
    getContext() { return index_1.Struct.getAs(Disembargo_Context, this); }
    initContext() { return index_1.Struct.getAs(Disembargo_Context, this); }
    toString() { return "Disembargo_" + super.toString(); }
}
exports.Disembargo = Disembargo;
Disembargo._capnp = { displayName: "Disembargo", id: "f964368b0fbd3711", size: new index_1.ObjectSize(8, 1) };
class Provide extends index_1.Struct {
    getQuestionId() { return index_1.Struct.getUint32(0, this); }
    setQuestionId(value) { index_1.Struct.setUint32(0, value, this); }
    adoptTarget(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this)); }
    disownTarget() { return index_1.Struct.disown(this.getTarget()); }
    getTarget() { return index_1.Struct.getStruct(0, MessageTarget, this); }
    hasTarget() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initTarget() { return index_1.Struct.initStructAt(0, MessageTarget, this); }
    setTarget(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this)); }
    adoptRecipient(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(1, this)); }
    disownRecipient() { return index_1.Struct.disown(this.getRecipient()); }
    getRecipient() { return index_1.Struct.getPointer(1, this); }
    hasRecipient() { return !index_1.Struct.isNull(index_1.Struct.getPointer(1, this)); }
    setRecipient(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(1, this)); }
    toString() { return "Provide_" + super.toString(); }
}
exports.Provide = Provide;
Provide._capnp = { displayName: "Provide", id: "9c6a046bfbc1ac5a", size: new index_1.ObjectSize(8, 2) };
class Accept extends index_1.Struct {
    getQuestionId() { return index_1.Struct.getUint32(0, this); }
    setQuestionId(value) { index_1.Struct.setUint32(0, value, this); }
    adoptProvision(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this)); }
    disownProvision() { return index_1.Struct.disown(this.getProvision()); }
    getProvision() { return index_1.Struct.getPointer(0, this); }
    hasProvision() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    setProvision(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this)); }
    getEmbargo() { return index_1.Struct.getBit(32, this); }
    setEmbargo(value) { index_1.Struct.setBit(32, value, this); }
    toString() { return "Accept_" + super.toString(); }
}
exports.Accept = Accept;
Accept._capnp = { displayName: "Accept", id: "d4c9b56290554016", size: new index_1.ObjectSize(8, 1) };
class Join extends index_1.Struct {
    getQuestionId() { return index_1.Struct.getUint32(0, this); }
    setQuestionId(value) { index_1.Struct.setUint32(0, value, this); }
    adoptTarget(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this)); }
    disownTarget() { return index_1.Struct.disown(this.getTarget()); }
    getTarget() { return index_1.Struct.getStruct(0, MessageTarget, this); }
    hasTarget() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initTarget() { return index_1.Struct.initStructAt(0, MessageTarget, this); }
    setTarget(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this)); }
    adoptKeyPart(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(1, this)); }
    disownKeyPart() { return index_1.Struct.disown(this.getKeyPart()); }
    getKeyPart() { return index_1.Struct.getPointer(1, this); }
    hasKeyPart() { return !index_1.Struct.isNull(index_1.Struct.getPointer(1, this)); }
    setKeyPart(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(1, this)); }
    toString() { return "Join_" + super.toString(); }
}
exports.Join = Join;
Join._capnp = { displayName: "Join", id: "fbe1980490e001af", size: new index_1.ObjectSize(8, 2) };
var MessageTarget_Which;
(function (MessageTarget_Which) {
    MessageTarget_Which[MessageTarget_Which["IMPORTED_CAP"] = 0] = "IMPORTED_CAP";
    MessageTarget_Which[MessageTarget_Which["PROMISED_ANSWER"] = 1] = "PROMISED_ANSWER";
})(MessageTarget_Which = exports.MessageTarget_Which || (exports.MessageTarget_Which = {}));
class MessageTarget extends index_1.Struct {
    getImportedCap() {
        index_1.Struct.testWhich("importedCap", index_1.Struct.getUint16(4, this), 0, this);
        return index_1.Struct.getUint32(0, this);
    }
    isImportedCap() { return index_1.Struct.getUint16(4, this) === 0; }
    setImportedCap(value) {
        index_1.Struct.setUint16(4, 0, this);
        index_1.Struct.setUint32(0, value, this);
    }
    adoptPromisedAnswer(value) {
        index_1.Struct.setUint16(4, 1, this);
        index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this));
    }
    disownPromisedAnswer() { return index_1.Struct.disown(this.getPromisedAnswer()); }
    getPromisedAnswer() {
        index_1.Struct.testWhich("promisedAnswer", index_1.Struct.getUint16(4, this), 1, this);
        return index_1.Struct.getStruct(0, PromisedAnswer, this);
    }
    hasPromisedAnswer() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initPromisedAnswer() {
        index_1.Struct.setUint16(4, 1, this);
        return index_1.Struct.initStructAt(0, PromisedAnswer, this);
    }
    isPromisedAnswer() { return index_1.Struct.getUint16(4, this) === 1; }
    setPromisedAnswer(value) {
        index_1.Struct.setUint16(4, 1, this);
        index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this));
    }
    toString() { return "MessageTarget_" + super.toString(); }
    which() { return index_1.Struct.getUint16(4, this); }
}
exports.MessageTarget = MessageTarget;
MessageTarget.IMPORTED_CAP = MessageTarget_Which.IMPORTED_CAP;
MessageTarget.PROMISED_ANSWER = MessageTarget_Which.PROMISED_ANSWER;
MessageTarget._capnp = { displayName: "MessageTarget", id: "95bc14545813fbc1", size: new index_1.ObjectSize(8, 1) };
class Payload extends index_1.Struct {
    adoptContent(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this)); }
    disownContent() { return index_1.Struct.disown(this.getContent()); }
    getContent() { return index_1.Struct.getPointer(0, this); }
    hasContent() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    setContent(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this)); }
    adoptCapTable(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(1, this)); }
    disownCapTable() { return index_1.Struct.disown(this.getCapTable()); }
    getCapTable() { return index_1.Struct.getList(1, Payload._CapTable, this); }
    hasCapTable() { return !index_1.Struct.isNull(index_1.Struct.getPointer(1, this)); }
    initCapTable(length) { return index_1.Struct.initList(1, Payload._CapTable, length, this); }
    setCapTable(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(1, this)); }
    toString() { return "Payload_" + super.toString(); }
}
exports.Payload = Payload;
Payload._capnp = { displayName: "Payload", id: "9a0e61223d96743b", size: new index_1.ObjectSize(0, 2) };
var CapDescriptor_Which;
(function (CapDescriptor_Which) {
    CapDescriptor_Which[CapDescriptor_Which["NONE"] = 0] = "NONE";
    CapDescriptor_Which[CapDescriptor_Which["SENDER_HOSTED"] = 1] = "SENDER_HOSTED";
    CapDescriptor_Which[CapDescriptor_Which["SENDER_PROMISE"] = 2] = "SENDER_PROMISE";
    CapDescriptor_Which[CapDescriptor_Which["RECEIVER_HOSTED"] = 3] = "RECEIVER_HOSTED";
    CapDescriptor_Which[CapDescriptor_Which["RECEIVER_ANSWER"] = 4] = "RECEIVER_ANSWER";
    CapDescriptor_Which[CapDescriptor_Which["THIRD_PARTY_HOSTED"] = 5] = "THIRD_PARTY_HOSTED";
})(CapDescriptor_Which = exports.CapDescriptor_Which || (exports.CapDescriptor_Which = {}));
class CapDescriptor extends index_1.Struct {
    isNone() { return index_1.Struct.getUint16(0, this) === 0; }
    setNone() { index_1.Struct.setUint16(0, 0, this); }
    getSenderHosted() {
        index_1.Struct.testWhich("senderHosted", index_1.Struct.getUint16(0, this), 1, this);
        return index_1.Struct.getUint32(4, this);
    }
    isSenderHosted() { return index_1.Struct.getUint16(0, this) === 1; }
    setSenderHosted(value) {
        index_1.Struct.setUint16(0, 1, this);
        index_1.Struct.setUint32(4, value, this);
    }
    getSenderPromise() {
        index_1.Struct.testWhich("senderPromise", index_1.Struct.getUint16(0, this), 2, this);
        return index_1.Struct.getUint32(4, this);
    }
    isSenderPromise() { return index_1.Struct.getUint16(0, this) === 2; }
    setSenderPromise(value) {
        index_1.Struct.setUint16(0, 2, this);
        index_1.Struct.setUint32(4, value, this);
    }
    getReceiverHosted() {
        index_1.Struct.testWhich("receiverHosted", index_1.Struct.getUint16(0, this), 3, this);
        return index_1.Struct.getUint32(4, this);
    }
    isReceiverHosted() { return index_1.Struct.getUint16(0, this) === 3; }
    setReceiverHosted(value) {
        index_1.Struct.setUint16(0, 3, this);
        index_1.Struct.setUint32(4, value, this);
    }
    adoptReceiverAnswer(value) {
        index_1.Struct.setUint16(0, 4, this);
        index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this));
    }
    disownReceiverAnswer() { return index_1.Struct.disown(this.getReceiverAnswer()); }
    getReceiverAnswer() {
        index_1.Struct.testWhich("receiverAnswer", index_1.Struct.getUint16(0, this), 4, this);
        return index_1.Struct.getStruct(0, PromisedAnswer, this);
    }
    hasReceiverAnswer() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initReceiverAnswer() {
        index_1.Struct.setUint16(0, 4, this);
        return index_1.Struct.initStructAt(0, PromisedAnswer, this);
    }
    isReceiverAnswer() { return index_1.Struct.getUint16(0, this) === 4; }
    setReceiverAnswer(value) {
        index_1.Struct.setUint16(0, 4, this);
        index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this));
    }
    adoptThirdPartyHosted(value) {
        index_1.Struct.setUint16(0, 5, this);
        index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this));
    }
    disownThirdPartyHosted() { return index_1.Struct.disown(this.getThirdPartyHosted()); }
    getThirdPartyHosted() {
        index_1.Struct.testWhich("thirdPartyHosted", index_1.Struct.getUint16(0, this), 5, this);
        return index_1.Struct.getStruct(0, ThirdPartyCapDescriptor, this);
    }
    hasThirdPartyHosted() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initThirdPartyHosted() {
        index_1.Struct.setUint16(0, 5, this);
        return index_1.Struct.initStructAt(0, ThirdPartyCapDescriptor, this);
    }
    isThirdPartyHosted() { return index_1.Struct.getUint16(0, this) === 5; }
    setThirdPartyHosted(value) {
        index_1.Struct.setUint16(0, 5, this);
        index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this));
    }
    toString() { return "CapDescriptor_" + super.toString(); }
    which() { return index_1.Struct.getUint16(0, this); }
}
exports.CapDescriptor = CapDescriptor;
CapDescriptor.NONE = CapDescriptor_Which.NONE;
CapDescriptor.SENDER_HOSTED = CapDescriptor_Which.SENDER_HOSTED;
CapDescriptor.SENDER_PROMISE = CapDescriptor_Which.SENDER_PROMISE;
CapDescriptor.RECEIVER_HOSTED = CapDescriptor_Which.RECEIVER_HOSTED;
CapDescriptor.RECEIVER_ANSWER = CapDescriptor_Which.RECEIVER_ANSWER;
CapDescriptor.THIRD_PARTY_HOSTED = CapDescriptor_Which.THIRD_PARTY_HOSTED;
CapDescriptor._capnp = { displayName: "CapDescriptor", id: "8523ddc40b86b8b0", size: new index_1.ObjectSize(8, 1) };
var PromisedAnswer_Op_Which;
(function (PromisedAnswer_Op_Which) {
    PromisedAnswer_Op_Which[PromisedAnswer_Op_Which["NOOP"] = 0] = "NOOP";
    PromisedAnswer_Op_Which[PromisedAnswer_Op_Which["GET_POINTER_FIELD"] = 1] = "GET_POINTER_FIELD";
})(PromisedAnswer_Op_Which = exports.PromisedAnswer_Op_Which || (exports.PromisedAnswer_Op_Which = {}));
class PromisedAnswer_Op extends index_1.Struct {
    isNoop() { return index_1.Struct.getUint16(0, this) === 0; }
    setNoop() { index_1.Struct.setUint16(0, 0, this); }
    getGetPointerField() {
        index_1.Struct.testWhich("getPointerField", index_1.Struct.getUint16(0, this), 1, this);
        return index_1.Struct.getUint16(2, this);
    }
    isGetPointerField() { return index_1.Struct.getUint16(0, this) === 1; }
    setGetPointerField(value) {
        index_1.Struct.setUint16(0, 1, this);
        index_1.Struct.setUint16(2, value, this);
    }
    toString() { return "PromisedAnswer_Op_" + super.toString(); }
    which() { return index_1.Struct.getUint16(0, this); }
}
exports.PromisedAnswer_Op = PromisedAnswer_Op;
PromisedAnswer_Op.NOOP = PromisedAnswer_Op_Which.NOOP;
PromisedAnswer_Op.GET_POINTER_FIELD = PromisedAnswer_Op_Which.GET_POINTER_FIELD;
PromisedAnswer_Op._capnp = { displayName: "Op", id: "f316944415569081", size: new index_1.ObjectSize(8, 0) };
class PromisedAnswer extends index_1.Struct {
    getQuestionId() { return index_1.Struct.getUint32(0, this); }
    setQuestionId(value) { index_1.Struct.setUint32(0, value, this); }
    adoptTransform(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this)); }
    disownTransform() { return index_1.Struct.disown(this.getTransform()); }
    getTransform() { return index_1.Struct.getList(0, PromisedAnswer._Transform, this); }
    hasTransform() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    initTransform(length) { return index_1.Struct.initList(0, PromisedAnswer._Transform, length, this); }
    setTransform(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this)); }
    toString() { return "PromisedAnswer_" + super.toString(); }
}
exports.PromisedAnswer = PromisedAnswer;
PromisedAnswer.Op = PromisedAnswer_Op;
PromisedAnswer._capnp = { displayName: "PromisedAnswer", id: "d800b1d6cd6f1ca0", size: new index_1.ObjectSize(8, 1) };
class ThirdPartyCapDescriptor extends index_1.Struct {
    adoptId(value) { index_1.Struct.adopt(value, index_1.Struct.getPointer(0, this)); }
    disownId() { return index_1.Struct.disown(this.getId()); }
    getId() { return index_1.Struct.getPointer(0, this); }
    hasId() { return !index_1.Struct.isNull(index_1.Struct.getPointer(0, this)); }
    setId(value) { index_1.Struct.copyFrom(value, index_1.Struct.getPointer(0, this)); }
    getVineId() { return index_1.Struct.getUint32(0, this); }
    setVineId(value) { index_1.Struct.setUint32(0, value, this); }
    toString() { return "ThirdPartyCapDescriptor_" + super.toString(); }
}
exports.ThirdPartyCapDescriptor = ThirdPartyCapDescriptor;
ThirdPartyCapDescriptor._capnp = { displayName: "ThirdPartyCapDescriptor", id: "d37007fde1f0027d", size: new index_1.ObjectSize(8, 1) };
var Exception_Type;
(function (Exception_Type) {
    Exception_Type[Exception_Type["FAILED"] = 0] = "FAILED";
    Exception_Type[Exception_Type["OVERLOADED"] = 1] = "OVERLOADED";
    Exception_Type[Exception_Type["DISCONNECTED"] = 2] = "DISCONNECTED";
    Exception_Type[Exception_Type["UNIMPLEMENTED"] = 3] = "UNIMPLEMENTED";
})(Exception_Type = exports.Exception_Type || (exports.Exception_Type = {}));
class Exception extends index_1.Struct {
    getReason() { return index_1.Struct.getText(0, this); }
    setReason(value) { index_1.Struct.setText(0, value, this); }
    getType() { return index_1.Struct.getUint16(4, this); }
    setType(value) { index_1.Struct.setUint16(4, value, this); }
    getObsoleteIsCallersFault() { return index_1.Struct.getBit(0, this); }
    setObsoleteIsCallersFault(value) { index_1.Struct.setBit(0, value, this); }
    getObsoleteDurability() { return index_1.Struct.getUint16(2, this); }
    setObsoleteDurability(value) { index_1.Struct.setUint16(2, value, this); }
    toString() { return "Exception_" + super.toString(); }
}
exports.Exception = Exception;
Exception.Type = Exception_Type;
Exception._capnp = { displayName: "Exception", id: "d625b7063acf691a", size: new index_1.ObjectSize(8, 1) };
Payload._CapTable = capnp.CompositeList(CapDescriptor);
PromisedAnswer._Transform = capnp.CompositeList(PromisedAnswer_Op);
//# sourceMappingURL=rpc.capnp.js.map