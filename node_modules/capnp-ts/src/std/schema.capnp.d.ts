/**
 * This file is generated by hand in order to bootstrap compiler development. It is intended to be an exact match to
 * compiled output.
 */
import * as capnp from "../index";
import { Struct as __S } from "../index";
export declare const _capnpFileId = "a93fc509624c72d9";
export declare class Node_Parameter extends __S {
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    getName(): string;
    setName(value: string): void;
    toString(): string;
}
export declare class Node_NestedNode extends __S {
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    getName(): string;
    setName(value: string): void;
    getId(): capnp.Uint64;
    setId(value: capnp.Uint64): void;
    toString(): string;
}
export declare class Node_Struct extends __S {
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    static _Fields: capnp.ListCtor<Field>;
    getDataWordCount(): number;
    setDataWordCount(value: number): void;
    getPointerCount(): number;
    setPointerCount(value: number): void;
    getPreferredListEncoding(): ElementSize;
    setPreferredListEncoding(value: ElementSize): void;
    getIsGroup(): boolean;
    setIsGroup(value: boolean): void;
    getDiscriminantCount(): number;
    setDiscriminantCount(value: number): void;
    getDiscriminantOffset(): number;
    setDiscriminantOffset(value: number): void;
    adoptFields(value: capnp.Orphan<capnp.List<Field>>): void;
    disownFields(): capnp.Orphan<capnp.List<Field>>;
    getFields(): capnp.List<Field>;
    hasFields(): boolean;
    initFields(length: number): capnp.List<Field>;
    setFields(value: capnp.List<Field>): void;
    toString(): string;
}
export declare class Node_Enum extends __S {
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    static _Enumerants: capnp.ListCtor<Enumerant>;
    adoptEnumerants(value: capnp.Orphan<capnp.List<Enumerant>>): void;
    disownEnumerants(): capnp.Orphan<capnp.List<Enumerant>>;
    getEnumerants(): capnp.List<Enumerant>;
    hasEnumerants(): boolean;
    initEnumerants(length: number): capnp.List<Enumerant>;
    setEnumerants(value: capnp.List<Enumerant>): void;
    toString(): string;
}
export declare class Node_Interface extends __S {
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    static _Methods: capnp.ListCtor<Method>;
    static _Superclasses: capnp.ListCtor<Superclass>;
    adoptMethods(value: capnp.Orphan<capnp.List<Method>>): void;
    disownMethods(): capnp.Orphan<capnp.List<Method>>;
    getMethods(): capnp.List<Method>;
    hasMethods(): boolean;
    initMethods(length: number): capnp.List<Method>;
    setMethods(value: capnp.List<Method>): void;
    adoptSuperclasses(value: capnp.Orphan<capnp.List<Superclass>>): void;
    disownSuperclasses(): capnp.Orphan<capnp.List<Superclass>>;
    getSuperclasses(): capnp.List<Superclass>;
    hasSuperclasses(): boolean;
    initSuperclasses(length: number): capnp.List<Superclass>;
    setSuperclasses(value: capnp.List<Superclass>): void;
    toString(): string;
}
export declare class Node_Const extends __S {
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    adoptType(value: capnp.Orphan<Type>): void;
    disownType(): capnp.Orphan<Type>;
    getType(): Type;
    hasType(): boolean;
    initType(): Type;
    setType(value: Type): void;
    adoptValue(value: capnp.Orphan<Value>): void;
    disownValue(): capnp.Orphan<Value>;
    getValue(): Value;
    hasValue(): boolean;
    initValue(): Value;
    setValue(value: Value): void;
    toString(): string;
}
export declare class Node_Annotation extends __S {
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    adoptType(value: capnp.Orphan<Type>): void;
    disownType(): capnp.Orphan<Type>;
    getType(): Type;
    hasType(): boolean;
    initType(): Type;
    setType(value: Type): void;
    getTargetsFile(): boolean;
    setTargetsFile(value: boolean): void;
    getTargetsConst(): boolean;
    setTargetsConst(value: boolean): void;
    getTargetsEnum(): boolean;
    setTargetsEnum(value: boolean): void;
    getTargetsEnumerant(): boolean;
    setTargetsEnumerant(value: boolean): void;
    getTargetsStruct(): boolean;
    setTargetsStruct(value: boolean): void;
    getTargetsField(): boolean;
    setTargetsField(value: boolean): void;
    getTargetsUnion(): boolean;
    setTargetsUnion(value: boolean): void;
    getTargetsGroup(): boolean;
    setTargetsGroup(value: boolean): void;
    getTargetsInterface(): boolean;
    setTargetsInterface(value: boolean): void;
    getTargetsMethod(): boolean;
    setTargetsMethod(value: boolean): void;
    getTargetsParam(): boolean;
    setTargetsParam(value: boolean): void;
    getTargetsAnnotation(): boolean;
    setTargetsAnnotation(value: boolean): void;
    toString(): string;
}
export declare enum Node_Which {
    FILE = 0,
    STRUCT = 1,
    ENUM = 2,
    INTERFACE = 3,
    CONST = 4,
    ANNOTATION = 5
}
export declare class Node extends __S {
    static readonly FILE = Node_Which.FILE;
    static readonly STRUCT = Node_Which.STRUCT;
    static readonly ENUM = Node_Which.ENUM;
    static readonly INTERFACE = Node_Which.INTERFACE;
    static readonly CONST = Node_Which.CONST;
    static readonly ANNOTATION = Node_Which.ANNOTATION;
    static readonly Parameter: typeof Node_Parameter;
    static readonly NestedNode: typeof Node_NestedNode;
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    static _Parameters: capnp.ListCtor<Node_Parameter>;
    static _NestedNodes: capnp.ListCtor<Node_NestedNode>;
    static _Annotations: capnp.ListCtor<Annotation>;
    getId(): capnp.Uint64;
    setId(value: capnp.Uint64): void;
    getDisplayName(): string;
    setDisplayName(value: string): void;
    getDisplayNamePrefixLength(): number;
    setDisplayNamePrefixLength(value: number): void;
    getScopeId(): capnp.Uint64;
    setScopeId(value: capnp.Uint64): void;
    adoptParameters(value: capnp.Orphan<capnp.List<Node_Parameter>>): void;
    disownParameters(): capnp.Orphan<capnp.List<Node_Parameter>>;
    getParameters(): capnp.List<Node_Parameter>;
    hasParameters(): boolean;
    initParameters(length: number): capnp.List<Node_Parameter>;
    setParameters(value: capnp.List<Node_Parameter>): void;
    getIsGeneric(): boolean;
    setIsGeneric(value: boolean): void;
    adoptNestedNodes(value: capnp.Orphan<capnp.List<Node_NestedNode>>): void;
    disownNestedNodes(): capnp.Orphan<capnp.List<Node_NestedNode>>;
    getNestedNodes(): capnp.List<Node_NestedNode>;
    hasNestedNodes(): boolean;
    initNestedNodes(length: number): capnp.List<Node_NestedNode>;
    setNestedNodes(value: capnp.List<Node_NestedNode>): void;
    adoptAnnotations(value: capnp.Orphan<capnp.List<Annotation>>): void;
    disownAnnotations(): capnp.Orphan<capnp.List<Annotation>>;
    getAnnotations(): capnp.List<Annotation>;
    hasAnnotations(): boolean;
    initAnnotations(length: number): capnp.List<Annotation>;
    setAnnotations(value: capnp.List<Annotation>): void;
    isFile(): boolean;
    setFile(): void;
    getStruct(): Node_Struct;
    initStruct(): Node_Struct;
    isStruct(): boolean;
    setStruct(): void;
    getEnum(): Node_Enum;
    initEnum(): Node_Enum;
    isEnum(): boolean;
    setEnum(): void;
    getInterface(): Node_Interface;
    initInterface(): Node_Interface;
    isInterface(): boolean;
    setInterface(): void;
    getConst(): Node_Const;
    initConst(): Node_Const;
    isConst(): boolean;
    setConst(): void;
    getAnnotation(): Node_Annotation;
    initAnnotation(): Node_Annotation;
    isAnnotation(): boolean;
    setAnnotation(): void;
    toString(): string;
    which(): Node_Which;
}
export declare class Field_Slot extends __S {
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    getOffset(): number;
    setOffset(value: number): void;
    adoptType(value: capnp.Orphan<Type>): void;
    disownType(): capnp.Orphan<Type>;
    getType(): Type;
    hasType(): boolean;
    initType(): Type;
    setType(value: Type): void;
    adoptDefaultValue(value: capnp.Orphan<Value>): void;
    disownDefaultValue(): capnp.Orphan<Value>;
    getDefaultValue(): Value;
    hasDefaultValue(): boolean;
    initDefaultValue(): Value;
    setDefaultValue(value: Value): void;
    getHadExplicitDefault(): boolean;
    setHadExplicitDefault(value: boolean): void;
    toString(): string;
}
export declare class Field_Group extends __S {
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    getTypeId(): capnp.Uint64;
    setTypeId(value: capnp.Uint64): void;
    toString(): string;
}
export declare enum Field_Ordinal_Which {
    IMPLICIT = 0,
    EXPLICIT = 1
}
export declare class Field_Ordinal extends __S {
    static readonly IMPLICIT = Field_Ordinal_Which.IMPLICIT;
    static readonly EXPLICIT = Field_Ordinal_Which.EXPLICIT;
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    isImplicit(): boolean;
    setImplicit(): void;
    getExplicit(): number;
    isExplicit(): boolean;
    setExplicit(value: number): void;
    toString(): string;
    which(): Field_Ordinal_Which;
}
export declare enum Field_Which {
    SLOT = 0,
    GROUP = 1
}
export declare class Field extends __S {
    static readonly NO_DISCRIMINANT = 65535;
    static readonly SLOT = Field_Which.SLOT;
    static readonly GROUP = Field_Which.GROUP;
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
        defaultDiscriminantValue: DataView;
    };
    static _Annotations: capnp.ListCtor<Annotation>;
    getName(): string;
    setName(value: string): void;
    getCodeOrder(): number;
    setCodeOrder(value: number): void;
    adoptAnnotations(value: capnp.Orphan<capnp.List<Annotation>>): void;
    disownAnnotations(): capnp.Orphan<capnp.List<Annotation>>;
    getAnnotations(): capnp.List<Annotation>;
    hasAnnotations(): boolean;
    initAnnotations(length: number): capnp.List<Annotation>;
    setAnnotations(value: capnp.List<Annotation>): void;
    getDiscriminantValue(): number;
    setDiscriminantValue(value: number): void;
    getSlot(): Field_Slot;
    initSlot(): Field_Slot;
    isSlot(): boolean;
    setSlot(): void;
    getGroup(): Field_Group;
    initGroup(): Field_Group;
    isGroup(): boolean;
    setGroup(): void;
    getOrdinal(): Field_Ordinal;
    initOrdinal(): Field_Ordinal;
    toString(): string;
    which(): Field_Which;
}
export declare class Enumerant extends __S {
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    static _Annotations: capnp.ListCtor<Annotation>;
    getName(): string;
    setName(value: string): void;
    getCodeOrder(): number;
    setCodeOrder(value: number): void;
    adoptAnnotations(value: capnp.Orphan<capnp.List<Annotation>>): void;
    disownAnnotations(): capnp.Orphan<capnp.List<Annotation>>;
    getAnnotations(): capnp.List<Annotation>;
    hasAnnotations(): boolean;
    initAnnotations(length: number): capnp.List<Annotation>;
    setAnnotations(value: capnp.List<Annotation>): void;
    toString(): string;
}
export declare class Superclass extends __S {
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    getId(): capnp.Uint64;
    setId(value: capnp.Uint64): void;
    adoptBrand(value: capnp.Orphan<Brand>): void;
    disownBrand(): capnp.Orphan<Brand>;
    getBrand(): Brand;
    hasBrand(): boolean;
    initBrand(): Brand;
    setBrand(value: Brand): void;
    toString(): string;
}
export declare class Method extends __S {
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    static _ImplicitParameters: capnp.ListCtor<Node_Parameter>;
    static _Annotations: capnp.ListCtor<Annotation>;
    getName(): string;
    setName(value: string): void;
    getCodeOrder(): number;
    setCodeOrder(value: number): void;
    adoptImplicitParameters(value: capnp.Orphan<capnp.List<Node_Parameter>>): void;
    disownImplicitParameters(): capnp.Orphan<capnp.List<Node_Parameter>>;
    getImplicitParameters(): capnp.List<Node_Parameter>;
    hasImplicitParameters(): boolean;
    initImplicitParameters(length: number): capnp.List<Node_Parameter>;
    setImplicitParameters(value: capnp.List<Node_Parameter>): void;
    getParamStructType(): capnp.Uint64;
    setParamStructType(value: capnp.Uint64): void;
    adoptParamBrand(value: capnp.Orphan<Brand>): void;
    disownParamBrand(): capnp.Orphan<Brand>;
    getParamBrand(): Brand;
    hasParamBrand(): boolean;
    initParamBrand(): Brand;
    setParamBrand(value: Brand): void;
    getResultStructType(): capnp.Uint64;
    setResultStructType(value: capnp.Uint64): void;
    adoptResultBrand(value: capnp.Orphan<Brand>): void;
    disownResultBrand(): capnp.Orphan<Brand>;
    getResultBrand(): Brand;
    hasResultBrand(): boolean;
    initResultBrand(): Brand;
    setResultBrand(value: Brand): void;
    adoptAnnotations(value: capnp.Orphan<capnp.List<Annotation>>): void;
    disownAnnotations(): capnp.Orphan<capnp.List<Annotation>>;
    getAnnotations(): capnp.List<Annotation>;
    hasAnnotations(): boolean;
    initAnnotations(length: number): capnp.List<Annotation>;
    setAnnotations(value: capnp.List<Annotation>): void;
    toString(): string;
}
export declare class Type_List extends __S {
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    adoptElementType(value: capnp.Orphan<Type>): void;
    disownElementType(): capnp.Orphan<Type>;
    getElementType(): Type;
    hasElementType(): boolean;
    initElementType(): Type;
    setElementType(value: Type): void;
    toString(): string;
}
export declare class Type_Enum extends __S {
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    getTypeId(): capnp.Uint64;
    setTypeId(value: capnp.Uint64): void;
    adoptBrand(value: capnp.Orphan<Brand>): void;
    disownBrand(): capnp.Orphan<Brand>;
    getBrand(): Brand;
    hasBrand(): boolean;
    initBrand(): Brand;
    setBrand(value: Brand): void;
    toString(): string;
}
export declare class Type_Struct extends __S {
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    getTypeId(): capnp.Uint64;
    setTypeId(value: capnp.Uint64): void;
    adoptBrand(value: capnp.Orphan<Brand>): void;
    disownBrand(): capnp.Orphan<Brand>;
    getBrand(): Brand;
    hasBrand(): boolean;
    initBrand(): Brand;
    setBrand(value: Brand): void;
    toString(): string;
}
export declare class Type_Interface extends __S {
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    getTypeId(): capnp.Uint64;
    setTypeId(value: capnp.Uint64): void;
    adoptBrand(value: capnp.Orphan<Brand>): void;
    disownBrand(): capnp.Orphan<Brand>;
    getBrand(): Brand;
    hasBrand(): boolean;
    initBrand(): Brand;
    setBrand(value: Brand): void;
    toString(): string;
}
export declare enum Type_AnyPointer_Unconstrained_Which {
    ANY_KIND = 0,
    STRUCT = 1,
    LIST = 2,
    CAPABILITY = 3
}
export declare class Type_AnyPointer_Unconstrained extends __S {
    static readonly ANY_KIND = Type_AnyPointer_Unconstrained_Which.ANY_KIND;
    static readonly STRUCT = Type_AnyPointer_Unconstrained_Which.STRUCT;
    static readonly LIST = Type_AnyPointer_Unconstrained_Which.LIST;
    static readonly CAPABILITY = Type_AnyPointer_Unconstrained_Which.CAPABILITY;
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    isAnyKind(): boolean;
    setAnyKind(): void;
    isStruct(): boolean;
    setStruct(): void;
    isList(): boolean;
    setList(): void;
    isCapability(): boolean;
    setCapability(): void;
    toString(): string;
    which(): Type_AnyPointer_Unconstrained_Which;
}
export declare class Type_AnyPointer_Parameter extends __S {
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    getScopeId(): capnp.Uint64;
    setScopeId(value: capnp.Uint64): void;
    getParameterIndex(): number;
    setParameterIndex(value: number): void;
    toString(): string;
}
export declare class Type_AnyPointer_ImplicitMethodParameter extends __S {
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    getParameterIndex(): number;
    setParameterIndex(value: number): void;
    toString(): string;
}
export declare enum Type_AnyPointer_Which {
    UNCONSTRAINED = 0,
    PARAMETER = 1,
    IMPLICIT_METHOD_PARAMETER = 2
}
export declare class Type_AnyPointer extends __S {
    static readonly UNCONSTRAINED = Type_AnyPointer_Which.UNCONSTRAINED;
    static readonly PARAMETER = Type_AnyPointer_Which.PARAMETER;
    static readonly IMPLICIT_METHOD_PARAMETER = Type_AnyPointer_Which.IMPLICIT_METHOD_PARAMETER;
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    getUnconstrained(): Type_AnyPointer_Unconstrained;
    initUnconstrained(): Type_AnyPointer_Unconstrained;
    isUnconstrained(): boolean;
    setUnconstrained(): void;
    getParameter(): Type_AnyPointer_Parameter;
    initParameter(): Type_AnyPointer_Parameter;
    isParameter(): boolean;
    setParameter(): void;
    getImplicitMethodParameter(): Type_AnyPointer_ImplicitMethodParameter;
    initImplicitMethodParameter(): Type_AnyPointer_ImplicitMethodParameter;
    isImplicitMethodParameter(): boolean;
    setImplicitMethodParameter(): void;
    toString(): string;
    which(): Type_AnyPointer_Which;
}
export declare enum Type_Which {
    VOID = 0,
    BOOL = 1,
    INT8 = 2,
    INT16 = 3,
    INT32 = 4,
    INT64 = 5,
    UINT8 = 6,
    UINT16 = 7,
    UINT32 = 8,
    UINT64 = 9,
    FLOAT32 = 10,
    FLOAT64 = 11,
    TEXT = 12,
    DATA = 13,
    LIST = 14,
    ENUM = 15,
    STRUCT = 16,
    INTERFACE = 17,
    ANY_POINTER = 18
}
export declare class Type extends __S {
    static readonly VOID = Type_Which.VOID;
    static readonly BOOL = Type_Which.BOOL;
    static readonly INT8 = Type_Which.INT8;
    static readonly INT16 = Type_Which.INT16;
    static readonly INT32 = Type_Which.INT32;
    static readonly INT64 = Type_Which.INT64;
    static readonly UINT8 = Type_Which.UINT8;
    static readonly UINT16 = Type_Which.UINT16;
    static readonly UINT32 = Type_Which.UINT32;
    static readonly UINT64 = Type_Which.UINT64;
    static readonly FLOAT32 = Type_Which.FLOAT32;
    static readonly FLOAT64 = Type_Which.FLOAT64;
    static readonly TEXT = Type_Which.TEXT;
    static readonly DATA = Type_Which.DATA;
    static readonly LIST = Type_Which.LIST;
    static readonly ENUM = Type_Which.ENUM;
    static readonly STRUCT = Type_Which.STRUCT;
    static readonly INTERFACE = Type_Which.INTERFACE;
    static readonly ANY_POINTER = Type_Which.ANY_POINTER;
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    isVoid(): boolean;
    setVoid(): void;
    isBool(): boolean;
    setBool(): void;
    isInt8(): boolean;
    setInt8(): void;
    isInt16(): boolean;
    setInt16(): void;
    isInt32(): boolean;
    setInt32(): void;
    isInt64(): boolean;
    setInt64(): void;
    isUint8(): boolean;
    setUint8(): void;
    isUint16(): boolean;
    setUint16(): void;
    isUint32(): boolean;
    setUint32(): void;
    isUint64(): boolean;
    setUint64(): void;
    isFloat32(): boolean;
    setFloat32(): void;
    isFloat64(): boolean;
    setFloat64(): void;
    isText(): boolean;
    setText(): void;
    isData(): boolean;
    setData(): void;
    getList(): Type_List;
    initList(): Type_List;
    isList(): boolean;
    setList(): void;
    getEnum(): Type_Enum;
    initEnum(): Type_Enum;
    isEnum(): boolean;
    setEnum(): void;
    getStruct(): Type_Struct;
    initStruct(): Type_Struct;
    isStruct(): boolean;
    setStruct(): void;
    getInterface(): Type_Interface;
    initInterface(): Type_Interface;
    isInterface(): boolean;
    setInterface(): void;
    getAnyPointer(): Type_AnyPointer;
    initAnyPointer(): Type_AnyPointer;
    isAnyPointer(): boolean;
    setAnyPointer(): void;
    toString(): string;
    which(): Type_Which;
}
export declare enum Brand_Scope_Which {
    BIND = 0,
    INHERIT = 1
}
export declare class Brand_Scope extends __S {
    static readonly BIND = Brand_Scope_Which.BIND;
    static readonly INHERIT = Brand_Scope_Which.INHERIT;
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    static _Bind: capnp.ListCtor<Brand_Binding>;
    getScopeId(): capnp.Uint64;
    setScopeId(value: capnp.Uint64): void;
    adoptBind(value: capnp.Orphan<capnp.List<Brand_Binding>>): void;
    disownBind(): capnp.Orphan<capnp.List<Brand_Binding>>;
    getBind(): capnp.List<Brand_Binding>;
    hasBind(): boolean;
    initBind(length: number): capnp.List<Brand_Binding>;
    isBind(): boolean;
    setBind(value: capnp.List<Brand_Binding>): void;
    isInherit(): boolean;
    setInherit(): void;
    toString(): string;
    which(): Brand_Scope_Which;
}
export declare enum Brand_Binding_Which {
    UNBOUND = 0,
    TYPE = 1
}
export declare class Brand_Binding extends __S {
    static readonly UNBOUND = Brand_Binding_Which.UNBOUND;
    static readonly TYPE = Brand_Binding_Which.TYPE;
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    isUnbound(): boolean;
    setUnbound(): void;
    adoptType(value: capnp.Orphan<Type>): void;
    disownType(): capnp.Orphan<Type>;
    getType(): Type;
    hasType(): boolean;
    initType(): Type;
    isType(): boolean;
    setType(value: Type): void;
    toString(): string;
    which(): Brand_Binding_Which;
}
export declare class Brand extends __S {
    static readonly Scope: typeof Brand_Scope;
    static readonly Binding: typeof Brand_Binding;
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    static _Scopes: capnp.ListCtor<Brand_Scope>;
    adoptScopes(value: capnp.Orphan<capnp.List<Brand_Scope>>): void;
    disownScopes(): capnp.Orphan<capnp.List<Brand_Scope>>;
    getScopes(): capnp.List<Brand_Scope>;
    hasScopes(): boolean;
    initScopes(length: number): capnp.List<Brand_Scope>;
    setScopes(value: capnp.List<Brand_Scope>): void;
    toString(): string;
}
export declare enum Value_Which {
    VOID = 0,
    BOOL = 1,
    INT8 = 2,
    INT16 = 3,
    INT32 = 4,
    INT64 = 5,
    UINT8 = 6,
    UINT16 = 7,
    UINT32 = 8,
    UINT64 = 9,
    FLOAT32 = 10,
    FLOAT64 = 11,
    TEXT = 12,
    DATA = 13,
    LIST = 14,
    ENUM = 15,
    STRUCT = 16,
    INTERFACE = 17,
    ANY_POINTER = 18
}
export declare class Value extends __S {
    static readonly VOID = Value_Which.VOID;
    static readonly BOOL = Value_Which.BOOL;
    static readonly INT8 = Value_Which.INT8;
    static readonly INT16 = Value_Which.INT16;
    static readonly INT32 = Value_Which.INT32;
    static readonly INT64 = Value_Which.INT64;
    static readonly UINT8 = Value_Which.UINT8;
    static readonly UINT16 = Value_Which.UINT16;
    static readonly UINT32 = Value_Which.UINT32;
    static readonly UINT64 = Value_Which.UINT64;
    static readonly FLOAT32 = Value_Which.FLOAT32;
    static readonly FLOAT64 = Value_Which.FLOAT64;
    static readonly TEXT = Value_Which.TEXT;
    static readonly DATA = Value_Which.DATA;
    static readonly LIST = Value_Which.LIST;
    static readonly ENUM = Value_Which.ENUM;
    static readonly STRUCT = Value_Which.STRUCT;
    static readonly INTERFACE = Value_Which.INTERFACE;
    static readonly ANY_POINTER = Value_Which.ANY_POINTER;
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    isVoid(): boolean;
    setVoid(): void;
    getBool(): boolean;
    isBool(): boolean;
    setBool(value: boolean): void;
    getInt8(): number;
    isInt8(): boolean;
    setInt8(value: number): void;
    getInt16(): number;
    isInt16(): boolean;
    setInt16(value: number): void;
    getInt32(): number;
    isInt32(): boolean;
    setInt32(value: number): void;
    getInt64(): capnp.Int64;
    isInt64(): boolean;
    setInt64(value: capnp.Int64): void;
    getUint8(): number;
    isUint8(): boolean;
    setUint8(value: number): void;
    getUint16(): number;
    isUint16(): boolean;
    setUint16(value: number): void;
    getUint32(): number;
    isUint32(): boolean;
    setUint32(value: number): void;
    getUint64(): capnp.Uint64;
    isUint64(): boolean;
    setUint64(value: capnp.Uint64): void;
    getFloat32(): number;
    isFloat32(): boolean;
    setFloat32(value: number): void;
    getFloat64(): number;
    isFloat64(): boolean;
    setFloat64(value: number): void;
    getText(): string;
    isText(): boolean;
    setText(value: string): void;
    adoptData(value: capnp.Orphan<capnp.Data>): void;
    disownData(): capnp.Orphan<capnp.Data>;
    getData(): capnp.Data;
    hasData(): boolean;
    initData(length: number): capnp.Data;
    isData(): boolean;
    setData(value: capnp.Data): void;
    adoptList(value: capnp.Orphan<capnp.Pointer>): void;
    disownList(): capnp.Orphan<capnp.Pointer>;
    getList(): capnp.Pointer;
    hasList(): boolean;
    isList(): boolean;
    setList(value: capnp.Pointer): void;
    getEnum(): number;
    isEnum(): boolean;
    setEnum(value: number): void;
    adoptStruct(value: capnp.Orphan<capnp.Pointer>): void;
    disownStruct(): capnp.Orphan<capnp.Pointer>;
    getStruct(): capnp.Pointer;
    hasStruct(): boolean;
    isStruct(): boolean;
    setStruct(value: capnp.Pointer): void;
    isInterface(): boolean;
    setInterface(): void;
    adoptAnyPointer(value: capnp.Orphan<capnp.Pointer>): void;
    disownAnyPointer(): capnp.Orphan<capnp.Pointer>;
    getAnyPointer(): capnp.Pointer;
    hasAnyPointer(): boolean;
    isAnyPointer(): boolean;
    setAnyPointer(value: capnp.Pointer): void;
    toString(): string;
    which(): Value_Which;
}
export declare class Annotation extends __S {
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    getId(): capnp.Uint64;
    setId(value: capnp.Uint64): void;
    adoptBrand(value: capnp.Orphan<Brand>): void;
    disownBrand(): capnp.Orphan<Brand>;
    getBrand(): Brand;
    hasBrand(): boolean;
    initBrand(): Brand;
    setBrand(value: Brand): void;
    adoptValue(value: capnp.Orphan<Value>): void;
    disownValue(): capnp.Orphan<Value>;
    getValue(): Value;
    hasValue(): boolean;
    initValue(): Value;
    setValue(value: Value): void;
    toString(): string;
}
export declare enum ElementSize {
    EMPTY = 0,
    BIT = 1,
    BYTE = 2,
    TWO_BYTES = 3,
    FOUR_BYTES = 4,
    EIGHT_BYTES = 5,
    POINTER = 6,
    INLINE_COMPOSITE = 7
}
export declare class CapnpVersion extends __S {
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    getMajor(): number;
    setMajor(value: number): void;
    getMinor(): number;
    setMinor(value: number): void;
    getMicro(): number;
    setMicro(value: number): void;
    toString(): string;
}
export declare class CodeGeneratorRequest_RequestedFile_Import extends __S {
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    getId(): capnp.Uint64;
    setId(value: capnp.Uint64): void;
    getName(): string;
    setName(value: string): void;
    toString(): string;
}
export declare class CodeGeneratorRequest_RequestedFile extends __S {
    static readonly Import: typeof CodeGeneratorRequest_RequestedFile_Import;
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    static _Imports: capnp.ListCtor<CodeGeneratorRequest_RequestedFile_Import>;
    getId(): capnp.Uint64;
    setId(value: capnp.Uint64): void;
    getFilename(): string;
    setFilename(value: string): void;
    adoptImports(value: capnp.Orphan<capnp.List<CodeGeneratorRequest_RequestedFile_Import>>): void;
    disownImports(): capnp.Orphan<capnp.List<CodeGeneratorRequest_RequestedFile_Import>>;
    getImports(): capnp.List<CodeGeneratorRequest_RequestedFile_Import>;
    hasImports(): boolean;
    initImports(length: number): capnp.List<CodeGeneratorRequest_RequestedFile_Import>;
    setImports(value: capnp.List<CodeGeneratorRequest_RequestedFile_Import>): void;
    toString(): string;
}
export declare class CodeGeneratorRequest extends __S {
    static readonly RequestedFile: typeof CodeGeneratorRequest_RequestedFile;
    static readonly _capnp: {
        displayName: string;
        id: string;
        size: capnp.ObjectSize;
    };
    static _Nodes: capnp.ListCtor<Node>;
    static _RequestedFiles: capnp.ListCtor<CodeGeneratorRequest_RequestedFile>;
    adoptCapnpVersion(value: capnp.Orphan<CapnpVersion>): void;
    disownCapnpVersion(): capnp.Orphan<CapnpVersion>;
    getCapnpVersion(): CapnpVersion;
    hasCapnpVersion(): boolean;
    initCapnpVersion(): CapnpVersion;
    setCapnpVersion(value: CapnpVersion): void;
    adoptNodes(value: capnp.Orphan<capnp.List<Node>>): void;
    disownNodes(): capnp.Orphan<capnp.List<Node>>;
    getNodes(): capnp.List<Node>;
    hasNodes(): boolean;
    initNodes(length: number): capnp.List<Node>;
    setNodes(value: capnp.List<Node>): void;
    adoptRequestedFiles(value: capnp.Orphan<capnp.List<CodeGeneratorRequest_RequestedFile>>): void;
    disownRequestedFiles(): capnp.Orphan<capnp.List<CodeGeneratorRequest_RequestedFile>>;
    getRequestedFiles(): capnp.List<CodeGeneratorRequest_RequestedFile>;
    hasRequestedFiles(): boolean;
    initRequestedFiles(length: number): capnp.List<CodeGeneratorRequest_RequestedFile>;
    setRequestedFiles(value: capnp.List<CodeGeneratorRequest_RequestedFile>): void;
    toString(): string;
}
