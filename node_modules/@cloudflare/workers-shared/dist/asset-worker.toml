##
# Configuration file for the Asset Worker
#
# Please note that wrangler has a dependency on this file, and will
# attempt to read it as part of setting up a new Miniflare instance
# in developemnt mode. We should ensure that any configuration changes
# to this file are persisted in wrangler as well, when necessary.
# (see packages/wrangler/src/dev/miniflare.ts -> buildMiniflareOptions())
##
name = "asset-worker"
account_id = "0f1b8aa119a907021f659042f95ea9ba"
workers_dev = false
main = "src/index.ts"
compatibility_date = "2024-07-31"
compatibility_flags = ["nodejs_compat"]

[[unsafe.bindings]]
name = "CONFIG"
type = "param"
param = "assetConfig"

[[unsafe.bindings]]
name = "ASSETS_MANIFEST"
type = "param"
param = "assetManifest"
data_ref = true

[[unsafe.bindings]]
name = "ASSETS_KV_NAMESPACE"
type = "internal_assets"

[unsafe.metadata.build_options]
stable_id = "cloudflare/cf_asset_worker"
networks = ["cf","jdc"]