var wn=Object.defineProperty;var An=(t,e,n)=>e in t?wn(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var fe=(t,e,n)=>(An(t,typeof e!="symbol"?e+"":e,n),n),yt=(t,e,n)=>{if(!e.has(t))throw TypeError("Cannot "+n)};var Fe=(t,e,n)=>(yt(t,e,"read from private field"),n?n.call(t):e.get(t)),Tt=(t,e,n)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,n)},bt=(t,e,n,r)=>(yt(t,e,"write to private field"),r?r.call(t,n):e.set(t,n),n);var Rt=Object.prototype.toString;function M(t){switch(Rt.call(t)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return O(t,Error)}}function xt(t,e){return Rt.call(t)===`[object ${e}]`}function j(t){return xt(t,"String")}function pe(t){return t===null||typeof t!="object"&&typeof t!="function"}function R(t){return xt(t,"Object")}function Le(t){return typeof Event<"u"&&O(t,Event)}function Ye(t){return typeof Element<"u"&&O(t,Element)}function G(t){return!!(t&&t.then&&typeof t.then=="function")}function $e(t){return R(t)&&"nativeEvent"in t&&"preventDefault"in t&&"stopPropagation"in t}function Z(t){return typeof t=="number"&&t!==t}function O(t,e){try{return t instanceof e}catch{return!1}}function je(t){return!!(typeof t=="object"&&t!==null&&(t.__isVue||t._isVue))}function A(t,e=0){return typeof t!="string"||e===0||t.length<=e?t:`${t.slice(0,e)}...`}function le(t){return t&&t.Math==Math?t:void 0}var _=typeof globalThis=="object"&&le(globalThis)||typeof window=="object"&&le(window)||typeof self=="object"&&le(self)||typeof global=="object"&&le(global)||function(){return this}()||{};function N(){return _}function Q(t,e,n){let r=n||_,s=r.__SENTRY__=r.__SENTRY__||{};return s[t]||(s[t]=e())}var os=N(),Pn=80;function Nt(t,e={}){if(!t)return"<unknown>";try{let n=t,r=5,s=[],i=0,o=0,a=" > ",u=a.length,c,f=Array.isArray(e)?e:e.keyAttrs,d=!Array.isArray(e)&&e.maxStringLength||Pn;for(;n&&i++<r&&(c=Cn(n,f),!(c==="html"||i>1&&o+s.length*u+c.length>=d));)s.push(c),o+=c.length,n=n.parentNode;return s.reverse().join(a)}catch{return"<unknown>"}}function Cn(t,e){let n=t,r=[],s,i,o,a,u;if(!n||!n.tagName)return"";r.push(n.tagName.toLowerCase());let c=e&&e.length?e.filter(d=>n.getAttribute(d)).map(d=>[d,n.getAttribute(d)]):null;if(c&&c.length)c.forEach(d=>{r.push(`[${d[0]}="${d[1]}"]`)});else if(n.id&&r.push(`#${n.id}`),s=n.className,s&&j(s))for(i=s.split(/\s+/),u=0;u<i.length;u++)r.push(`.${i[u]}`);let f=["aria-label","type","name","title","alt"];for(u=0;u<f.length;u++)o=f[u],a=n.getAttribute(o),a&&r.push(`[${o}="${a}"]`);return r.join("")}var Un="Sentry Logger ",ee=["debug","info","warn","error","log","assert","trace"],H={};function _e(t){if(!("console"in _))return t();let e=_.console,n={},r=Object.keys(H);r.forEach(s=>{let i=H[s];n[s]=e[s],e[s]=i});try{return t()}finally{r.forEach(s=>{e[s]=n[s]})}}function Mn(){let t=!1,e={enable:()=>{t=!0},disable:()=>{t=!1},isEnabled:()=>t};return typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__?ee.forEach(n=>{e[n]=(...r)=>{t&&_e(()=>{_.console[n](`${Un}[${n}]:`,...r)})}}):ee.forEach(n=>{e[n]=()=>{}}),e}var p=Mn();var Gn=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function Bn(t){return t==="http"||t==="https"}function P(t,e=!1){let{host:n,path:r,pass:s,port:i,projectId:o,protocol:a,publicKey:u}=t;return`${a}://${u}${e&&s?`:${s}`:""}@${n}${i?`:${i}`:""}/${r&&`${r}/`}${o}`}function It(t){let e=Gn.exec(t);if(!e){console.error(`Invalid Sentry Dsn: ${t}`);return}let[n,r,s="",i,o="",a]=e.slice(1),u="",c=a,f=c.split("/");if(f.length>1&&(u=f.slice(0,-1).join("/"),c=f.pop()),c){let d=c.match(/^\d+/);d&&(c=d[0])}return Dt({host:i,pass:s,path:u,projectId:c,port:o,protocol:n,publicKey:r})}function Dt(t){return{protocol:t.protocol,publicKey:t.publicKey||"",pass:t.pass||"",host:t.host,port:t.port||"",path:t.path||"",projectId:t.projectId}}function Fn(t){if(!(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__))return!0;let{port:e,projectId:n,protocol:r}=t;return["protocol","publicKey","host","projectId"].find(o=>t[o]?!1:(p.error(`Invalid Sentry Dsn: ${o} missing`),!0))?!1:n.match(/^\d+$/)?Bn(r)?e&&isNaN(parseInt(e,10))?(p.error(`Invalid Sentry Dsn: Invalid port ${e}`),!1):!0:(p.error(`Invalid Sentry Dsn: Invalid protocol ${r}`),!1):(p.error(`Invalid Sentry Dsn: Invalid projectId ${n}`),!1)}function me(t){let e=typeof t=="string"?It(t):Dt(t);if(!(!e||!Fn(e)))return e}var y=class extends Error{constructor(e,n="warn"){super(e),this.message=e,this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype),this.logLevel=n}};function x(t,e,n){if(!(e in t))return;let r=t[e],s=n(r);typeof s=="function"&&vt(s,r),t[e]=s}function q(t,e,n){try{Object.defineProperty(t,e,{value:n,writable:!0,configurable:!0})}catch{(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&p.log(`Failed to add non-enumerable property "${e}" to object`,t)}}function vt(t,e){try{let n=e.prototype||{};t.prototype=e.prototype=n,q(t,"__sentry_original__",e)}catch{}}function qe(t){return Object.keys(t).map(e=>`${encodeURIComponent(e)}=${encodeURIComponent(t[e])}`).join("&")}function he(t){if(M(t))return{message:t.message,name:t.name,stack:t.stack,...Ot(t)};if(Le(t)){let e={type:t.type,target:kt(t.target),currentTarget:kt(t.currentTarget),...Ot(t)};return typeof CustomEvent<"u"&&O(t,CustomEvent)&&(e.detail=t.detail),e}else return t}function kt(t){try{return Ye(t)?Nt(t):Object.prototype.toString.call(t)}catch{return"<unknown>"}}function Ot(t){if(typeof t=="object"&&t!==null){let e={};for(let n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}else return{}}function te(t,e=40){let n=Object.keys(he(t));if(n.sort(),!n.length)return"[object has no keys]";if(n[0].length>=e)return A(n[0],e);for(let r=n.length;r>0;r--){let s=n.slice(0,r).join(", ");if(!(s.length>e))return r===n.length?s:A(s,e)}return""}function g(t){return He(t,new Map)}function He(t,e){if(R(t)){let n=e.get(t);if(n!==void 0)return n;let r={};e.set(t,r);for(let s of Object.keys(t))typeof t[s]<"u"&&(r[s]=He(t[s],e));return r}if(Array.isArray(t)){let n=e.get(t);if(n!==void 0)return n;let r=[];return e.set(t,r),t.forEach(s=>{r.push(He(s,e))}),r}return t}function Ln(t,e=!1){return!(e||t&&!t.startsWith("/")&&!t.includes(":\\")&&!t.startsWith(".")&&!t.match(/^[a-zA-Z]([a-zA-Z0-9.\-+])*:\/\//))&&t!==void 0&&!t.includes("node_modules/")}function wt(t){let e=/^\s*[-]{4,}$/,n=/at (?:async )?(?:(.+?)\s+\()?(?:(.+):(\d+):(\d+)?|([^)]+))\)?/;return r=>{let s=r.match(n);if(s){let i,o,a,u,c;if(s[1]){a=s[1];let l=a.lastIndexOf(".");if(a[l-1]==="."&&l--,l>0){i=a.slice(0,l),o=a.slice(l+1);let m=i.indexOf(".Module");m>0&&(a=a.slice(m+1),i=i.slice(0,m))}u=void 0}o&&(u=i,c=o),o==="<anonymous>"&&(c=void 0,a=void 0),a===void 0&&(c=c||"<anonymous>",a=u?`${u}.${c}`:c);let f=s[2]&&s[2].startsWith("file://")?s[2].slice(7):s[2],d=s[5]==="native";return!f&&s[5]&&!d&&(f=s[5]),{filename:f,module:t?t(f):void 0,function:a,lineno:parseInt(s[3],10)||void 0,colno:parseInt(s[4],10)||void 0,in_app:Ln(f,d)}}if(r.match(e))return{filename:r}}}var Ct=50,At=/\(error: (.*)\)/,Pt=/captureMessage|captureException/;function ge(...t){let e=t.sort((n,r)=>n[0]-r[0]).map(n=>n[1]);return(n,r=0)=>{let s=[],i=n.split(`
`);for(let o=r;o<i.length;o++){let a=i[o];if(a.length>1024)continue;let u=At.test(a)?a.replace(At,"$1"):a;if(!u.match(/\S*Error: /)){for(let c of e){let f=c(u);if(f){s.push(f);break}}if(s.length>=Ct)break}}return Ut(s)}}function We(t){return Array.isArray(t)?ge(...t):t}function Ut(t){if(!t.length)return[];let e=Array.from(t);return/sentryWrapped/.test(e[e.length-1].function||"")&&e.pop(),e.reverse(),Pt.test(e[e.length-1].function||"")&&(e.pop(),Pt.test(e[e.length-1].function||"")&&e.pop()),e.slice(0,Ct).map(n=>({...n,filename:n.filename||e[e.length-1].filename,function:n.function||"?"}))}var ze="<anonymous>";function ne(t){try{return!t||typeof t!="function"?ze:t.name||ze}catch{return ze}}function Ke(t){return[90,wt(t)]}var Ve=N();function Yn(){if(!("fetch"in Ve))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch{return!1}}function Mt(t){return t&&/^function fetch\(\)\s+\{\s+\[native code\]\s+\}$/.test(t.toString())}function Gt(){if(!Yn())return!1;if(Mt(Ve.fetch))return!0;let t=!1,e=Ve.document;if(e&&typeof e.createElement=="function")try{let n=e.createElement("iframe");n.hidden=!0,e.head.appendChild(n),n.contentWindow&&n.contentWindow.fetch&&(t=Mt(n.contentWindow.fetch)),e.head.removeChild(n)}catch(n){(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&p.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",n)}return t}var Ee=N();function Bt(){let t=Ee.chrome,e=t&&t.app&&t.app.runtime,n="history"in Ee&&!!Ee.history.pushState&&!!Ee.history.replaceState;return!e&&n}var E=N(),re="__sentry_xhr_v2__",se={},Ft={};function $n(t){if(!Ft[t])switch(Ft[t]=!0,t){case"console":jn();break;case"dom":qt();break;case"xhr":Ht();break;case"fetch":Hn();break;case"history":qn();break;case"error":Vn();break;case"unhandledrejection":Jn();break;default:(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&p.warn("unknown instrumentation type:",t);return}}function Re(t,e){se[t]=se[t]||[],se[t].push(e),$n(t)}function I(t,e){if(!(!t||!se[t]))for(let n of se[t]||[])try{n(e)}catch(r){(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&p.error(`Error while triggering instrumentation handler.
Type: ${t}
Name: ${ne(n)}
Error:`,r)}}function jn(){"console"in _&&ee.forEach(function(t){t in _.console&&x(_.console,t,function(e){return H[t]=e,function(...n){I("console",{args:n,level:t});let r=H[t];r&&r.apply(_.console,n)}})})}function Hn(){Gt()&&x(_,"fetch",function(t){return function(...e){let{method:n,url:r}=jt(e),s={args:e,fetchData:{method:n,url:r},startTimestamp:Date.now()};return I("fetch",{...s}),t.apply(_,e).then(i=>(I("fetch",{...s,endTimestamp:Date.now(),response:i}),i),i=>{throw I("fetch",{...s,endTimestamp:Date.now(),error:i}),i})}})}function Je(t,e){return!!t&&typeof t=="object"&&!!t[e]}function Lt(t){return typeof t=="string"?t:t?Je(t,"url")?t.url:t.toString?t.toString():"":""}function jt(t){if(t.length===0)return{method:"GET",url:""};if(t.length===2){let[n,r]=t;return{url:Lt(n),method:Je(r,"method")?String(r.method).toUpperCase():"GET"}}let e=t[0];return{url:Lt(e),method:Je(e,"method")?String(e.method).toUpperCase():"GET"}}function Ht(){if(!E.XMLHttpRequest)return;let t=XMLHttpRequest.prototype;x(t,"open",function(e){return function(...n){let r=Date.now(),s=n[1],i=this[re]={method:j(n[0])?n[0].toUpperCase():n[0],url:n[1],request_headers:{}};j(s)&&i.method==="POST"&&s.match(/sentry_key/)&&(this.__sentry_own_request__=!0);let o=()=>{let a=this[re];if(a&&this.readyState===4){try{a.status_code=this.status}catch{}I("xhr",{args:n,endTimestamp:Date.now(),startTimestamp:r,xhr:this})}};return"onreadystatechange"in this&&typeof this.onreadystatechange=="function"?x(this,"onreadystatechange",function(a){return function(...u){return o(),a.apply(this,u)}}):this.addEventListener("readystatechange",o),x(this,"setRequestHeader",function(a){return function(...u){let[c,f]=u,d=this[re];return d&&(d.request_headers[c.toLowerCase()]=f),a.apply(this,u)}}),e.apply(this,n)}}),x(t,"send",function(e){return function(...n){let r=this[re];return r&&n[0]!==void 0&&(r.body=n[0]),I("xhr",{args:n,startTimestamp:Date.now(),xhr:this}),e.apply(this,n)}})}var Se;function qn(){if(!Bt())return;let t=E.onpopstate;E.onpopstate=function(...n){let r=E.location.href,s=Se;if(Se=r,I("history",{from:s,to:r}),t)try{return t.apply(this,n)}catch{}};function e(n){return function(...r){let s=r.length>2?r[2]:void 0;if(s){let i=Se,o=String(s);Se=o,I("history",{from:i,to:o})}return n.apply(this,r)}}x(E.history,"pushState",e),x(E.history,"replaceState",e)}var zn=1e3,Yt,ye;function Wn(t,e){if(t.type!==e.type)return!1;try{if(t.target!==e.target)return!1}catch{}return!0}function Kn(t){if(t.type!=="keypress")return!1;try{let e=t.target;if(!e||!e.tagName)return!0;if(e.tagName==="INPUT"||e.tagName==="TEXTAREA"||e.isContentEditable)return!1}catch{}return!0}function $t(t,e=!1){return n=>{if(!n||n._sentryCaptured||Kn(n))return;q(n,"_sentryCaptured",!0);let r=n.type==="keypress"?"input":n.type;(ye===void 0||!Wn(ye,n))&&(t({event:n,name:r,global:e}),ye=n),clearTimeout(Yt),Yt=E.setTimeout(()=>{ye=void 0},zn)}}function qt(){if(!E.document)return;let t=I.bind(null,"dom"),e=$t(t,!0);E.document.addEventListener("click",e,!1),E.document.addEventListener("keypress",e,!1),["EventTarget","Node"].forEach(n=>{let r=E[n]&&E[n].prototype;!r||!r.hasOwnProperty||!r.hasOwnProperty("addEventListener")||(x(r,"addEventListener",function(s){return function(i,o,a){if(i==="click"||i=="keypress")try{let u=this,c=u.__sentry_instrumentation_handlers__=u.__sentry_instrumentation_handlers__||{},f=c[i]=c[i]||{refCount:0};if(!f.handler){let d=$t(t);f.handler=d,s.call(this,i,d,a)}f.refCount++}catch{}return s.call(this,i,o,a)}}),x(r,"removeEventListener",function(s){return function(i,o,a){if(i==="click"||i=="keypress")try{let u=this,c=u.__sentry_instrumentation_handlers__||{},f=c[i];f&&(f.refCount--,f.refCount<=0&&(s.call(this,i,f.handler,a),f.handler=void 0,delete c[i]),Object.keys(c).length===0&&delete u.__sentry_instrumentation_handlers__)}catch{}return s.call(this,i,o,a)}}))})}var Te=null;function Vn(){Te=E.onerror,E.onerror=function(t,e,n,r,s){return I("error",{column:r,error:s,line:n,msg:t,url:e}),Te&&!Te.__SENTRY_LOADER__?Te.apply(this,arguments):!1},E.onerror.__SENTRY_INSTRUMENTED__=!0}var be=null;function Jn(){be=E.onunhandledrejection,E.onunhandledrejection=function(t){return I("unhandledrejection",t),be&&!be.__SENTRY_LOADER__?be.apply(this,arguments):!0},E.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}function zt(){return typeof __SENTRY_BROWSER_BUNDLE__<"u"&&!!__SENTRY_BROWSER_BUNDLE__}function Wt(){return!zt()&&Object.prototype.toString.call(typeof process<"u"?process:0)==="[object process]"}function Kt(t,e){return t.require(e)}function Vt(){let t=typeof WeakSet=="function",e=t?new WeakSet:[];function n(s){if(t)return e.has(s)?!0:(e.add(s),!1);for(let i=0;i<e.length;i++)if(e[i]===s)return!0;return e.push(s),!1}function r(s){if(t)e.delete(s);else for(let i=0;i<e.length;i++)if(e[i]===s){e.splice(i,1);break}}return[n,r]}function h(){let t=_,e=t.crypto||t.msCrypto,n=()=>Math.random()*16;try{if(e&&e.randomUUID)return e.randomUUID().replace(/-/g,"");e&&e.getRandomValues&&(n=()=>e.getRandomValues(new Uint8Array(1))[0])}catch{}return([1e7]+1e3+4e3+8e3+1e11).replace(/[018]/g,r=>(r^(n()&15)>>r/4).toString(16))}function Xn(t){return t.exception&&t.exception.values?t.exception.values[0]:void 0}function ie(t,e,n){let r=t.exception=t.exception||{},s=r.values=r.values||[],i=s[0]=s[0]||{};i.value||(i.value=e||""),i.type||(i.type=n||"Error")}function oe(t,e){let n=Xn(t);if(!n)return;let r={type:"generic",handled:!0},s=n.mechanism;if(n.mechanism={...r,...s,...e},e&&"data"in e){let i={...s&&s.data,...e.data};n.mechanism.data=i}}function xe(t){if(t&&t.__sentry_captured__)return!0;try{q(t,"__sentry_captured__",!0)}catch{}return!1}function ae(t){return Array.isArray(t)?t:[t]}function D(t,e=100,n=1/0){try{return Ne("",t,e,n)}catch(r){return{ERROR:`**non-serializable** (${r})`}}}function z(t,e=3,n=100*1024){let r=D(t,e);return tr(r)>n?z(t,e-1,n):r}function Ne(t,e,n=1/0,r=1/0,s=Vt()){let[i,o]=s;if(e==null||["number","boolean","string"].includes(typeof e)&&!Z(e))return e;let a=Zn(t,e);if(!a.startsWith("[object "))return a;if(e.__sentry_skip_normalization__)return e;let u=typeof e.__sentry_override_normalization_depth__=="number"?e.__sentry_override_normalization_depth__:n;if(u===0)return a.replace("object ","");if(i(e))return"[Circular ~]";let c=e;if(c&&typeof c.toJSON=="function")try{let m=c.toJSON();return Ne("",m,u-1,r,s)}catch{}let f=Array.isArray(e)?[]:{},d=0,l=he(e);for(let m in l){if(!Object.prototype.hasOwnProperty.call(l,m))continue;if(d>=r){f[m]="[MaxProperties ~]";break}let U=l[m];f[m]=Ne(m,U,u-1,r,s),d++}return o(e),f}function Zn(t,e){try{if(t==="domain"&&e&&typeof e=="object"&&e._events)return"[Domain]";if(t==="domainEmitter")return"[DomainEmitter]";if(typeof global<"u"&&e===global)return"[Global]";if(typeof window<"u"&&e===window)return"[Window]";if(typeof document<"u"&&e===document)return"[Document]";if(je(e))return"[VueViewModel]";if($e(e))return"[SyntheticEvent]";if(typeof e=="number"&&e!==e)return"[NaN]";if(typeof e=="function")return`[Function: ${ne(e)}]`;if(typeof e=="symbol")return`[${String(e)}]`;if(typeof e=="bigint")return`[BigInt: ${String(e)}]`;let n=Qn(e);return/^HTML(\w*)Element$/.test(n)?`[HTMLElement: ${n}]`:`[object ${n}]`}catch(n){return`**non-serializable** (${n})`}}function Qn(t){let e=Object.getPrototypeOf(t);return e?e.constructor.name:"null prototype"}function er(t){return~-encodeURI(t).split(/%..|./).length}function tr(t){return er(JSON.stringify(t))}var nr=/^(\S+:\\|\/?)([\s\S]*?)((?:\.{1,2}|[^/\\]+?|)(\.[^./\\]*|))(?:[/\\]*)$/;function rr(t){let e=t.length>1024?`<truncated>${t.slice(-1024)}`:t,n=nr.exec(e);return n?n.slice(1):[]}function Xe(t,e){let n=rr(t)[2];return e&&n.slice(e.length*-1)===e&&(n=n.slice(0,n.length-e.length)),n}var v;(function(t){t[t.PENDING=0]="PENDING";let n=1;t[t.RESOLVED=n]="RESOLVED";let r=2;t[t.REJECTED=r]="REJECTED"})(v||(v={}));function T(t){return new S(e=>{e(t)})}function B(t){return new S((e,n)=>{n(t)})}var S=class{constructor(e){S.prototype.__init.call(this),S.prototype.__init2.call(this),S.prototype.__init3.call(this),S.prototype.__init4.call(this),this._state=v.PENDING,this._handlers=[];try{e(this._resolve,this._reject)}catch(n){this._reject(n)}}then(e,n){return new S((r,s)=>{this._handlers.push([!1,i=>{if(!e)r(i);else try{r(e(i))}catch(o){s(o)}},i=>{if(!n)s(i);else try{r(n(i))}catch(o){s(o)}}]),this._executeHandlers()})}catch(e){return this.then(n=>n,e)}finally(e){return new S((n,r)=>{let s,i;return this.then(o=>{i=!1,s=o,e&&e()},o=>{i=!0,s=o,e&&e()}).then(()=>{if(i){r(s);return}n(s)})})}__init(){this._resolve=e=>{this._setResult(v.RESOLVED,e)}}__init2(){this._reject=e=>{this._setResult(v.REJECTED,e)}}__init3(){this._setResult=(e,n)=>{if(this._state===v.PENDING){if(G(n)){n.then(this._resolve,this._reject);return}this._state=e,this._value=n,this._executeHandlers()}}}__init4(){this._executeHandlers=()=>{if(this._state===v.PENDING)return;let e=this._handlers.slice();this._handlers=[],e.forEach(n=>{n[0]||(this._state===v.RESOLVED&&n[1](this._value),this._state===v.REJECTED&&n[2](this._value),n[0]=!0)})}}};function Ze(t){let e=[];function n(){return t===void 0||e.length<t}function r(o){return e.splice(e.indexOf(o),1)[0]}function s(o){if(!n())return B(new y("Not adding Promise because buffer limit was reached."));let a=o();return e.indexOf(a)===-1&&e.push(a),a.then(()=>r(a)).then(null,()=>r(a).then(null,()=>{})),a}function i(o){return new S((a,u)=>{let c=e.length;if(!c)return a(!0);let f=setTimeout(()=>{o&&o>0&&a(!1)},o);e.forEach(d=>{T(d).then(()=>{--c||(clearTimeout(f),a(!0))},u)})})}return{$:e,add:s,drain:i}}var Xt=N(),et={nowSeconds:()=>Date.now()/1e3};function sr(){let{performance:t}=Xt;if(!t||!t.now)return;let e=Date.now()-t.now();return{now:()=>t.now(),timeOrigin:e}}function ir(){try{return Kt(module,"perf_hooks").performance}catch{return}}var Qe=Wt()?ir():sr(),Jt=Qe===void 0?et:{nowSeconds:()=>(Qe.timeOrigin+Qe.now())/1e3},F=et.nowSeconds.bind(et),L=Jt.nowSeconds.bind(Jt);var ce,or=(()=>{let{performance:t}=Xt;if(!t||!t.now){ce="none";return}let e=3600*1e3,n=t.now(),r=Date.now(),s=t.timeOrigin?Math.abs(t.timeOrigin+n-r):e,i=s<e,o=t.timing&&t.timing.navigationStart,u=typeof o=="number"?Math.abs(o+n-r):e,c=u<e;return i||c?s<=u?(ce="timeOrigin",t.timeOrigin):(ce="navigationStart",o):(ce="dateNow",r)})();var ar=new RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");function tt(t=h(),e=h().substring(16),n){let r="";return n!==void 0&&(r=n?"-1":"-0"),`${t}-${e}${r}`}function C(t,e=[]){return[t,e]}function rt(t,e){let[n,r]=t;return[n,[...r,e]]}function Ie(t,e){let n=t[1];for(let r of n){let s=r[0].type;if(e(r,s))return!0}return!1}function nt(t,e){return(e||new TextEncoder).encode(t)}function st(t,e){let[n,r]=t,s=JSON.stringify(n);function i(o){typeof s=="string"?s=typeof o=="string"?s+o:[nt(s,e),o]:s.push(typeof o=="string"?nt(o,e):o)}for(let o of r){let[a,u]=o;if(i(`
${JSON.stringify(a)}
`),typeof u=="string"||u instanceof Uint8Array)i(u);else{let c;try{c=JSON.stringify(u)}catch{c=JSON.stringify(D(u))}i(c)}}return typeof s=="string"?s:cr(s)}function cr(t){let e=t.reduce((s,i)=>s+i.length,0),n=new Uint8Array(e),r=0;for(let s of t)n.set(s,r),r+=s.length;return n}function it(t,e){let n=typeof t.data=="string"?nt(t.data,e):t.data;return[g({type:"attachment",length:n.length,filename:t.filename,content_type:t.contentType,attachment_type:t.attachmentType}),n]}var ur={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",statsd:"unknown"};function De(t){return ur[t]}function ke(t){if(!t||!t.sdk)return;let{name:e,version:n}=t.sdk;return{name:e,version:n}}function ot(t,e,n,r){let s=t.sdkProcessingMetadata&&t.sdkProcessingMetadata.dynamicSamplingContext;return{event_id:t.event_id,sent_at:new Date().toISOString(),...e&&{sdk:e},...!!n&&r&&{dsn:P(r)},...s&&{trace:g({...s})}}}function Zt(t,e=Date.now()){let n=parseInt(`${t}`,10);if(!isNaN(n))return n*1e3;let r=Date.parse(`${t}`);return isNaN(r)?6e4:r-e}function Qt(t,e){return t[e]||t.all||0}function at(t,e,n=Date.now()){return Qt(t,e)>n}function ct(t,{statusCode:e,headers:n},r=Date.now()){let s={...t},i=n&&n["x-sentry-rate-limits"],o=n&&n["retry-after"];if(i)for(let a of i.trim().split(",")){let[u,c]=a.split(":",2),f=parseInt(u,10),d=(isNaN(f)?60:f)*1e3;if(!c)s.all=r+d;else for(let l of c.split(";"))s[l]=r+d}else o?s.all=r+Zt(o,r):e===429&&(s.all=r+60*1e3);return s}function ut(t,e){return t(e.stack||"",1)}function en(t,e){let n={type:e.name||e.constructor.name,value:e.message},r=ut(t,e);return r.length&&(n.stacktrace={frames:r}),n}function dr(t){if("name"in t&&typeof t.name=="string"){let e=`'${t.name}' captured as exception`;return"message"in t&&typeof t.message=="string"&&(e+=` with message '${t.message}'`),e}else return"message"in t&&typeof t.message=="string"?t.message:`Object captured as exception with keys: ${te(t)}`}function dt(t,e,n,r){let s=n,o=r&&r.data&&r.data.mechanism||{handled:!0,type:"generic"};if(!M(n)){if(R(n)){let u=t(),c=u.getClient(),f=c&&c.getOptions().normalizeDepth;u.configureScope(l=>{l.setExtra("__serialized__",z(n,f))});let d=dr(n);s=r&&r.syntheticException||new Error(d),s.message=d}else s=r&&r.syntheticException||new Error(n),s.message=n;o.synthetic=!0}let a={exception:{values:[en(e,s)]}};return ie(a,void 0,void 0),oe(a,o),{...a,event_id:r&&r.event_id}}function ft(t,e,n="info",r,s){let i={event_id:r&&r.event_id,level:n,message:e};if(s&&r&&r.syntheticException){let o=ut(t,r.syntheticException);o.length&&(i.exception={values:[{value:e,stacktrace:{frames:o}}]})}return i}var W="production";function ue(){return Q("globalEventProcessors",()=>[])}function tn(t){ue().push(t)}function K(t,e,n,r=0){return new S((s,i)=>{let o=t[r];if(e===null||typeof o!="function")s(e);else{let a=o({...e},n);(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&o.id&&a===null&&p.log(`Event processor "${o.id}" dropped event`),G(a)?a.then(u=>K(t,u,n,r+1).then(s)).then(null,i):K(t,a,n,r+1).then(s).then(null,i)}})}function nn(t){let e=L(),n={sid:h(),init:!0,timestamp:e,started:e,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>fr(n)};return t&&w(n,t),n}function w(t,e={}){if(e.user&&(!t.ipAddress&&e.user.ip_address&&(t.ipAddress=e.user.ip_address),!t.did&&!e.did&&(t.did=e.user.id||e.user.email||e.user.username)),t.timestamp=e.timestamp||L(),e.abnormal_mechanism&&(t.abnormal_mechanism=e.abnormal_mechanism),e.ignoreDuration&&(t.ignoreDuration=e.ignoreDuration),e.sid&&(t.sid=e.sid.length===32?e.sid:h()),e.init!==void 0&&(t.init=e.init),!t.did&&e.did&&(t.did=`${e.did}`),typeof e.started=="number"&&(t.started=e.started),t.ignoreDuration)t.duration=void 0;else if(typeof e.duration=="number")t.duration=e.duration;else{let n=t.timestamp-t.started;t.duration=n>=0?n:0}e.release&&(t.release=e.release),e.environment&&(t.environment=e.environment),!t.ipAddress&&e.ipAddress&&(t.ipAddress=e.ipAddress),!t.userAgent&&e.userAgent&&(t.userAgent=e.userAgent),typeof e.errors=="number"&&(t.errors=e.errors),e.status&&(t.status=e.status)}function rn(t,e){let n={};e?n={status:e}:t.status==="ok"&&(n={status:"exited"}),w(t,n)}function fr(t){return g({sid:`${t.sid}`,init:t.init,started:new Date(t.started*1e3).toISOString(),timestamp:new Date(t.timestamp*1e3).toISOString(),status:t.status,errors:t.errors,did:typeof t.did=="number"||typeof t.did=="string"?`${t.did}`:void 0,duration:t.duration,abnormal_mechanism:t.abnormal_mechanism,attrs:{release:t.release,environment:t.environment,ip_address:t.ipAddress,user_agent:t.userAgent}})}var pr=100,k=class{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext=sn()}static clone(e){let n=new k;return e&&(n._breadcrumbs=[...e._breadcrumbs],n._tags={...e._tags},n._extra={...e._extra},n._contexts={...e._contexts},n._user=e._user,n._level=e._level,n._span=e._span,n._session=e._session,n._transactionName=e._transactionName,n._fingerprint=e._fingerprint,n._eventProcessors=[...e._eventProcessors],n._requestSession=e._requestSession,n._attachments=[...e._attachments],n._sdkProcessingMetadata={...e._sdkProcessingMetadata},n._propagationContext={...e._propagationContext}),n}addScopeListener(e){this._scopeListeners.push(e)}addEventProcessor(e){return this._eventProcessors.push(e),this}setUser(e){return this._user=e||{},this._session&&w(this._session,{user:e}),this._notifyScopeListeners(),this}getUser(){return this._user}getRequestSession(){return this._requestSession}setRequestSession(e){return this._requestSession=e,this}setTags(e){return this._tags={...this._tags,...e},this._notifyScopeListeners(),this}setTag(e,n){return this._tags={...this._tags,[e]:n},this._notifyScopeListeners(),this}setExtras(e){return this._extra={...this._extra,...e},this._notifyScopeListeners(),this}setExtra(e,n){return this._extra={...this._extra,[e]:n},this._notifyScopeListeners(),this}setFingerprint(e){return this._fingerprint=e,this._notifyScopeListeners(),this}setLevel(e){return this._level=e,this._notifyScopeListeners(),this}setTransactionName(e){return this._transactionName=e,this._notifyScopeListeners(),this}setContext(e,n){return n===null?delete this._contexts[e]:this._contexts[e]=n,this._notifyScopeListeners(),this}setSpan(e){return this._span=e,this._notifyScopeListeners(),this}getSpan(){return this._span}getTransaction(){let e=this.getSpan();return e&&e.transaction}setSession(e){return e?this._session=e:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(e){if(!e)return this;if(typeof e=="function"){let n=e(this);return n instanceof k?n:this}return e instanceof k?(this._tags={...this._tags,...e._tags},this._extra={...this._extra,...e._extra},this._contexts={...this._contexts,...e._contexts},e._user&&Object.keys(e._user).length&&(this._user=e._user),e._level&&(this._level=e._level),e._fingerprint&&(this._fingerprint=e._fingerprint),e._requestSession&&(this._requestSession=e._requestSession),e._propagationContext&&(this._propagationContext=e._propagationContext)):R(e)&&(e=e,this._tags={...this._tags,...e.tags},this._extra={...this._extra,...e.extra},this._contexts={...this._contexts,...e.contexts},e.user&&(this._user=e.user),e.level&&(this._level=e.level),e.fingerprint&&(this._fingerprint=e.fingerprint),e.requestSession&&(this._requestSession=e.requestSession),e.propagationContext&&(this._propagationContext=e.propagationContext)),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._requestSession=void 0,this._span=void 0,this._session=void 0,this._notifyScopeListeners(),this._attachments=[],this._propagationContext=sn(),this}addBreadcrumb(e,n){let r=typeof n=="number"?n:pr;if(r<=0)return this;let s={timestamp:F(),...e},i=this._breadcrumbs;return i.push(s),this._breadcrumbs=i.length>r?i.slice(-r):i,this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(e){return this._attachments.push(e),this}getAttachments(){return this._attachments}clearAttachments(){return this._attachments=[],this}applyToEvent(e,n={},r){if(this._extra&&Object.keys(this._extra).length&&(e.extra={...this._extra,...e.extra}),this._tags&&Object.keys(this._tags).length&&(e.tags={...this._tags,...e.tags}),this._user&&Object.keys(this._user).length&&(e.user={...this._user,...e.user}),this._contexts&&Object.keys(this._contexts).length&&(e.contexts={...this._contexts,...e.contexts}),this._level&&(e.level=this._level),this._transactionName&&(e.transaction=this._transactionName),this._span){e.contexts={trace:this._span.getTraceContext(),...e.contexts};let o=this._span.transaction;if(o){e.sdkProcessingMetadata={dynamicSamplingContext:o.getDynamicSamplingContext(),...e.sdkProcessingMetadata};let a=o.name;a&&(e.tags={transaction:a,...e.tags})}}this._applyFingerprint(e);let s=this._getBreadcrumbs(),i=[...e.breadcrumbs||[],...s];return e.breadcrumbs=i.length>0?i:void 0,e.sdkProcessingMetadata={...e.sdkProcessingMetadata,...this._sdkProcessingMetadata,propagationContext:this._propagationContext},K([...r||[],...ue(),...this._eventProcessors],e,n)}setSDKProcessingMetadata(e){return this._sdkProcessingMetadata={...this._sdkProcessingMetadata,...e},this}setPropagationContext(e){return this._propagationContext=e,this}getPropagationContext(){return this._propagationContext}_getBreadcrumbs(){return this._breadcrumbs}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(e=>{e(this)}),this._notifyingListeners=!1)}_applyFingerprint(e){e.fingerprint=e.fingerprint?ae(e.fingerprint):[],this._fingerprint&&(e.fingerprint=e.fingerprint.concat(this._fingerprint)),e.fingerprint&&!e.fingerprint.length&&delete e.fingerprint}};function sn(){return{traceId:h(),spanId:h().substring(16)}}var on=4,lr=100,Y=class{constructor(e,n=new k,r=on){this._version=r,this._stack=[{scope:n}],e&&this.bindClient(e)}isOlderThan(e){return this._version<e}bindClient(e){let n=this.getStackTop();n.client=e,e&&e.setupIntegrations&&e.setupIntegrations()}pushScope(){let e=k.clone(this.getScope());return this.getStack().push({client:this.getClient(),scope:e}),e}popScope(){return this.getStack().length<=1?!1:!!this.getStack().pop()}withScope(e){let n=this.pushScope();try{e(n)}finally{this.popScope()}}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getStack(){return this._stack}getStackTop(){return this._stack[this._stack.length-1]}captureException(e,n){let r=this._lastEventId=n&&n.event_id?n.event_id:h(),s=new Error("Sentry syntheticException");return this._withClient((i,o)=>{i.captureException(e,{originalException:e,syntheticException:s,...n,event_id:r},o)}),r}captureMessage(e,n,r){let s=this._lastEventId=r&&r.event_id?r.event_id:h(),i=new Error(e);return this._withClient((o,a)=>{o.captureMessage(e,n,{originalException:e,syntheticException:i,...r,event_id:s},a)}),s}captureEvent(e,n){let r=n&&n.event_id?n.event_id:h();return e.type||(this._lastEventId=r),this._withClient((s,i)=>{s.captureEvent(e,{...n,event_id:r},i)}),r}lastEventId(){return this._lastEventId}addBreadcrumb(e,n){let{scope:r,client:s}=this.getStackTop();if(!s)return;let{beforeBreadcrumb:i=null,maxBreadcrumbs:o=lr}=s.getOptions&&s.getOptions()||{};if(o<=0)return;let u={timestamp:F(),...e},c=i?_e(()=>i(u,n)):u;c!==null&&(s.emit&&s.emit("beforeAddBreadcrumb",c,n),r.addBreadcrumb(c,o))}setUser(e){this.getScope().setUser(e)}setTags(e){this.getScope().setTags(e)}setExtras(e){this.getScope().setExtras(e)}setTag(e,n){this.getScope().setTag(e,n)}setExtra(e,n){this.getScope().setExtra(e,n)}setContext(e,n){this.getScope().setContext(e,n)}configureScope(e){let{scope:n,client:r}=this.getStackTop();r&&e(n)}run(e){let n=pt(this);try{e(this)}finally{pt(n)}}getIntegration(e){let n=this.getClient();if(!n)return null;try{return n.getIntegration(e)}catch{return(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&p.warn(`Cannot retrieve integration ${e.id} from the current Hub`),null}}startTransaction(e,n){let r=this._callExtensionMethod("startTransaction",e,n);if((typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&!r){let s=this.getClient();console.warn(s?`Tracing extension 'startTransaction' has not been added. Call 'addTracingExtensions' before calling 'init':
Sentry.addTracingExtensions();
Sentry.init({...});
`:"Tracing extension 'startTransaction' is missing. You should 'init' the SDK before calling 'startTransaction'")}return r}traceHeaders(){return this._callExtensionMethod("traceHeaders")}captureSession(e=!1){if(e)return this.endSession();this._sendSessionUpdate()}endSession(){let n=this.getStackTop().scope,r=n.getSession();r&&rn(r),this._sendSessionUpdate(),n.setSession()}startSession(e){let{scope:n,client:r}=this.getStackTop(),{release:s,environment:i=W}=r&&r.getOptions()||{},{userAgent:o}=_.navigator||{},a=nn({release:s,environment:i,user:n.getUser(),...o&&{userAgent:o},...e}),u=n.getSession&&n.getSession();return u&&u.status==="ok"&&w(u,{status:"exited"}),this.endSession(),n.setSession(a),a}shouldSendDefaultPii(){let e=this.getClient(),n=e&&e.getOptions();return!!(n&&n.sendDefaultPii)}_sendSessionUpdate(){let{scope:e,client:n}=this.getStackTop(),r=e.getSession();r&&n&&n.captureSession&&n.captureSession(r)}_withClient(e){let{scope:n,client:r}=this.getStackTop();r&&e(r,n)}_callExtensionMethod(e,...n){let s=$().__SENTRY__;if(s&&s.extensions&&typeof s.extensions[e]=="function")return s.extensions[e].apply(this,n);(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&p.warn(`Extension method ${e} couldn't be found, doing nothing.`)}};function $(){return _.__SENTRY__=_.__SENTRY__||{extensions:{},hub:void 0},_}function pt(t){let e=$(),n=Oe(e);return lt(e,t),n}function b(){let t=$();if(t.__SENTRY__&&t.__SENTRY__.acs){let e=t.__SENTRY__.acs.getCurrentHub();if(e)return e}return _r(t)}function _r(t=$()){return(!mr(t)||Oe(t).isOlderThan(on))&&lt(t,new Y),Oe(t)}function mr(t){return!!(t&&t.__SENTRY__&&t.__SENTRY__.hub)}function Oe(t){return Q("hub",()=>new Y,t)}function lt(t,e){if(!t)return!1;let n=t.__SENTRY__=t.__SENTRY__||{};return n.hub=e,!0}function an(t){return(t||b()).getScope().getTransaction()}var cn=!1;function un(){cn||(cn=!0,Re("error",_t),Re("unhandledrejection",_t))}function _t(){let t=an();if(t){let e="internal_error";(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&p.log(`[Tracing] Transaction: ${e} -> Global error occured`),t.setStatus(e)}}_t.tag="sentry_tracingErrorCallback";var ve=class{constructor(e=1e3){this._maxlen=e,this.spans=[]}add(e){this.spans.length>this._maxlen?e.spanRecorder=void 0:this.spans.push(e)}},V=class{constructor(e={}){this.traceId=e.traceId||h(),this.spanId=e.spanId||h().substring(16),this.startTimestamp=e.startTimestamp||L(),this.tags=e.tags||{},this.data=e.data||{},this.instrumenter=e.instrumenter||"sentry",this.origin=e.origin||"manual",e.parentSpanId&&(this.parentSpanId=e.parentSpanId),"sampled"in e&&(this.sampled=e.sampled),e.op&&(this.op=e.op),e.description&&(this.description=e.description),e.name&&(this.description=e.name),e.status&&(this.status=e.status),e.endTimestamp&&(this.endTimestamp=e.endTimestamp)}get name(){return this.description||""}set name(e){this.setName(e)}startChild(e){let n=new V({...e,parentSpanId:this.spanId,sampled:this.sampled,traceId:this.traceId});if(n.spanRecorder=this.spanRecorder,n.spanRecorder&&n.spanRecorder.add(n),n.transaction=this.transaction,(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&n.transaction){let r=e&&e.op||"< unknown op >",s=n.transaction.name||"< unknown name >",i=n.transaction.spanId,o=`[Tracing] Starting '${r}' span on transaction '${s}' (${i}).`;n.transaction.metadata.spanMetadata[n.spanId]={logMessage:o},p.log(o)}return n}setTag(e,n){return this.tags={...this.tags,[e]:n},this}setData(e,n){return this.data={...this.data,[e]:n},this}setStatus(e){return this.status=e,this}setHttpStatus(e){this.setTag("http.status_code",String(e)),this.setData("http.response.status_code",e);let n=hr(e);return n!=="unknown_error"&&this.setStatus(n),this}setName(e){this.description=e}isSuccess(){return this.status==="ok"}finish(e){if((typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&this.transaction&&this.transaction.spanId!==this.spanId){let{logMessage:n}=this.transaction.metadata.spanMetadata[this.spanId];n&&p.log(n.replace("Starting","Finishing"))}this.endTimestamp=typeof e=="number"?e:L()}toTraceparent(){return tt(this.traceId,this.spanId,this.sampled)}toContext(){return g({data:this.data,description:this.description,endTimestamp:this.endTimestamp,op:this.op,parentSpanId:this.parentSpanId,sampled:this.sampled,spanId:this.spanId,startTimestamp:this.startTimestamp,status:this.status,tags:this.tags,traceId:this.traceId})}updateWithContext(e){return this.data=e.data||{},this.description=e.description,this.endTimestamp=e.endTimestamp,this.op=e.op,this.parentSpanId=e.parentSpanId,this.sampled=e.sampled,this.spanId=e.spanId||this.spanId,this.startTimestamp=e.startTimestamp||this.startTimestamp,this.status=e.status,this.tags=e.tags||{},this.traceId=e.traceId||this.traceId,this}getTraceContext(){return g({data:Object.keys(this.data).length>0?this.data:void 0,description:this.description,op:this.op,parent_span_id:this.parentSpanId,span_id:this.spanId,status:this.status,tags:Object.keys(this.tags).length>0?this.tags:void 0,trace_id:this.traceId})}toJSON(){return g({data:Object.keys(this.data).length>0?this.data:void 0,description:this.description,op:this.op,parent_span_id:this.parentSpanId,span_id:this.spanId,start_timestamp:this.startTimestamp,status:this.status,tags:Object.keys(this.tags).length>0?this.tags:void 0,timestamp:this.endTimestamp,trace_id:this.traceId,origin:this.origin})}};function hr(t){if(t<400&&t>=100)return"ok";if(t>=400&&t<500)switch(t){case 401:return"unauthenticated";case 403:return"permission_denied";case 404:return"not_found";case 409:return"already_exists";case 413:return"failed_precondition";case 429:return"resource_exhausted";default:return"invalid_argument"}if(t>=500&&t<600)switch(t){case 501:return"unimplemented";case 503:return"unavailable";case 504:return"deadline_exceeded";default:return"internal_error"}return"unknown_error"}function J(t,e,n){let r=e.getOptions(),{publicKey:s}=e.getDsn()||{},{segment:i}=n&&n.getUser()||{},o=g({environment:r.environment||W,release:r.release,user_segment:i,public_key:s,trace_id:t});return e.emit&&e.emit("createDsc",o),o}var we=class extends V{constructor(e,n){super(e),delete this.description,this._measurements={},this._contexts={},this._hub=n||b(),this._name=e.name||"",this.metadata={source:"custom",...e.metadata,spanMetadata:{}},this._trimEnd=e.trimEnd,this.transaction=this;let r=this.metadata.dynamicSamplingContext;r&&(this._frozenDynamicSamplingContext={...r})}get name(){return this._name}set name(e){this.setName(e)}setName(e,n="custom"){this._name=e,this.metadata.source=n}initSpanRecorder(e=1e3){this.spanRecorder||(this.spanRecorder=new ve(e)),this.spanRecorder.add(this)}setContext(e,n){n===null?delete this._contexts[e]:this._contexts[e]=n}setMeasurement(e,n,r=""){this._measurements[e]={value:n,unit:r}}setMetadata(e){this.metadata={...this.metadata,...e}}finish(e){let n=this._finishTransaction(e);if(n)return this._hub.captureEvent(n)}toContext(){let e=super.toContext();return g({...e,name:this.name,trimEnd:this._trimEnd})}updateWithContext(e){return super.updateWithContext(e),this.name=e.name||"",this._trimEnd=e.trimEnd,this}getDynamicSamplingContext(){if(this._frozenDynamicSamplingContext)return this._frozenDynamicSamplingContext;let e=this._hub||b(),n=e.getClient();if(!n)return{};let r=e.getScope(),s=J(this.traceId,n,r),i=this.metadata.sampleRate;i!==void 0&&(s.sample_rate=`${i}`);let o=this.metadata.source;return o&&o!=="url"&&(s.transaction=this.name),this.sampled!==void 0&&(s.sampled=String(this.sampled)),s}setHub(e){this._hub=e}_finishTransaction(e){if(this.endTimestamp!==void 0)return;this.name||((typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&p.warn("Transaction has no name, falling back to `<unlabeled transaction>`."),this.name="<unlabeled transaction>"),super.finish(e);let n=this._hub.getClient();if(n&&n.emit&&n.emit("finishTransaction",this),this.sampled!==!0){(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&p.log("[Tracing] Discarding transaction because its trace was not chosen to be sampled."),n&&n.recordDroppedEvent("sample_rate","transaction");return}let r=this.spanRecorder?this.spanRecorder.spans.filter(a=>a!==this&&a.endTimestamp):[];this._trimEnd&&r.length>0&&(this.endTimestamp=r.reduce((a,u)=>a.endTimestamp&&u.endTimestamp?a.endTimestamp>u.endTimestamp?a:u:a).endTimestamp);let s=this.metadata,i={contexts:{...this._contexts,trace:this.getTraceContext()},spans:r,start_timestamp:this.startTimestamp,tags:this.tags,timestamp:this.endTimestamp,transaction:this.name,type:"transaction",sdkProcessingMetadata:{...s,dynamicSamplingContext:this.getDynamicSamplingContext()},...s.source&&{transaction_info:{source:s.source}}};return Object.keys(this._measurements).length>0&&((typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&p.log("[Measurements] Adding measurements to transaction",JSON.stringify(this._measurements,void 0,2)),i.measurements=this._measurements),(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&p.log(`[Tracing] Finishing ${this.op} transaction: ${this.name}.`),i}};function dn(t){if(typeof __SENTRY_TRACING__=="boolean"&&!__SENTRY_TRACING__)return!1;let e=b().getClient(),n=t||e&&e.getOptions();return!!n&&(n.enableTracing||"tracesSampleRate"in n||"tracesSampler"in n)}function fn(t,e,n){if(!dn(e))return t.sampled=!1,t;if(t.sampled!==void 0)return t.setMetadata({sampleRate:Number(t.sampled)}),t;let r;return typeof e.tracesSampler=="function"?(r=e.tracesSampler(n),t.setMetadata({sampleRate:Number(r)})):n.parentSampled!==void 0?r=n.parentSampled:typeof e.tracesSampleRate<"u"?(r=e.tracesSampleRate,t.setMetadata({sampleRate:Number(r)})):(r=1,t.setMetadata({sampleRate:r})),gr(r)?r?(t.sampled=Math.random()<r,t.sampled?((typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&p.log(`[Tracing] starting ${t.op} transaction - ${t.name}`),t):((typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&p.log(`[Tracing] Discarding transaction because it's not included in the random sample (sampling rate = ${Number(r)})`),t)):((typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&p.log(`[Tracing] Discarding transaction because ${typeof e.tracesSampler=="function"?"tracesSampler returned 0 or false":"a negative sampling decision was inherited or tracesSampleRate is set to 0"}`),t.sampled=!1,t):((typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&p.warn("[Tracing] Discarding transaction because of invalid sample rate."),t.sampled=!1,t)}function gr(t){return Z(t)||!(typeof t=="number"||typeof t=="boolean")?((typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&p.warn(`[Tracing] Given sample rate is invalid. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(t)} of type ${JSON.stringify(typeof t)}.`),!1):t<0||t>1?((typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&p.warn(`[Tracing] Given sample rate is invalid. Sample rate must be between 0 and 1. Got ${t}.`),!1):!0}function Er(){let e=this.getScope().getSpan();return e?{"sentry-trace":e.toTraceparent()}:{}}function Sr(t,e){let n=this.getClient(),r=n&&n.getOptions()||{},s=r.instrumenter||"sentry",i=t.instrumenter||"sentry";s!==i&&((typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&p.error(`A transaction was started with instrumenter=\`${i}\`, but the SDK is configured with the \`${s}\` instrumenter.
The transaction will not be sampled. Please use the ${s} instrumentation to start transactions.`),t.sampled=!1);let o=new we(t,this);return o=fn(o,r,{parentSampled:t.parentSampled,transactionContext:t,...e}),o.sampled&&o.initSpanRecorder(r._experiments&&r._experiments.maxSpans),n&&n.emit&&n.emit("startTransaction",o),o}function pn(){let t=$();t.__SENTRY__&&(t.__SENTRY__.extensions=t.__SENTRY__.extensions||{},t.__SENTRY__.extensions.startTransaction||(t.__SENTRY__.extensions.startTransaction=Sr),t.__SENTRY__.extensions.traceHeaders||(t.__SENTRY__.extensions.traceHeaders=Er),un())}var Ae=class{constructor(e,n){this._client=e,this.flushTimeout=60,this._pendingAggregates={},this._isEnabled=!0,this._intervalId=setInterval(()=>this.flush(),this.flushTimeout*1e3),this._sessionAttrs=n}flush(){let e=this.getSessionAggregates();e.aggregates.length!==0&&(this._pendingAggregates={},this._client.sendSession(e))}getSessionAggregates(){let e=Object.keys(this._pendingAggregates).map(r=>this._pendingAggregates[parseInt(r)]),n={attrs:this._sessionAttrs,aggregates:e};return g(n)}close(){clearInterval(this._intervalId),this._isEnabled=!1,this.flush()}incrementSessionStatusCount(){if(!this._isEnabled)return;let e=b().getScope(),n=e.getRequestSession();n&&n.status&&(this._incrementSessionStatusCount(n.status,new Date),e.setRequestSession(void 0))}_incrementSessionStatusCount(e,n){let r=new Date(n).setSeconds(0,0);this._pendingAggregates[r]=this._pendingAggregates[r]||{};let s=this._pendingAggregates[r];switch(s.started||(s.started=new Date(r).toISOString()),e){case"errored":return s.errored=(s.errored||0)+1,s.errored;case"ok":return s.exited=(s.exited||0)+1,s.exited;default:return s.crashed=(s.crashed||0)+1,s.crashed}}};var yr="7";function Tr(t){let e=t.protocol?`${t.protocol}:`:"",n=t.port?`:${t.port}`:"";return`${e}//${t.host}${n}${t.path?`/${t.path}`:""}/api/`}function br(t){return`${Tr(t)}${t.projectId}/envelope/`}function Rr(t,e){return qe({sentry_key:t.publicKey,sentry_version:yr,...e&&{sentry_client:`${e.name}/${e.version}`}})}function ln(t,e={}){let n=typeof e=="string"?e:e.tunnel,r=typeof e=="string"||!e._metadata?void 0:e._metadata.sdk;return n||`${br(t)}?${Rr(t,r)}`}function xr(t,e){return e&&(t.sdk=t.sdk||{},t.sdk.name=t.sdk.name||e.name,t.sdk.version=t.sdk.version||e.version,t.sdk.integrations=[...t.sdk.integrations||[],...e.integrations||[]],t.sdk.packages=[...t.sdk.packages||[],...e.packages||[]]),t}function _n(t,e,n,r){let s=ke(n),i={sent_at:new Date().toISOString(),...s&&{sdk:s},...!!r&&e&&{dsn:P(e)}},o="aggregates"in t?[{type:"sessions"},t]:[{type:"session"},t.toJSON()];return C(i,[o])}function mn(t,e,n,r){let s=ke(n),i=t.type&&t.type!=="replay_event"?t.type:"event";xr(t,n&&n.sdk);let o=ot(t,s,r,e);return delete t.sdkProcessingMetadata,C(o,[[{type:i},t]])}var hn=[];function Nr(t){let e={};return t.forEach(n=>{let{name:r}=n,s=e[r];s&&!s.isDefaultInstance&&n.isDefaultInstance||(e[r]=n)}),Object.keys(e).map(n=>e[n])}function mt(t){let e=t.defaultIntegrations||[],n=t.integrations;e.forEach(o=>{o.isDefaultInstance=!0});let r;Array.isArray(n)?r=[...e,...n]:typeof n=="function"?r=ae(n(e)):r=e;let s=Nr(r),i=Ir(s,o=>o.name==="Debug");if(i!==-1){let[o]=s.splice(i,1);s.push(o)}return s}function gn(t,e){let n={};return e.forEach(r=>{r&&ht(t,r,n)}),n}function ht(t,e,n){if(n[e.name]=e,hn.indexOf(e.name)===-1&&(e.setupOnce(tn,b),hn.push(e.name)),t.on&&typeof e.preprocessEvent=="function"){let r=e.preprocessEvent.bind(e);t.on("preprocessEvent",(s,i)=>r(s,i,t))}if(t.addEventProcessor&&typeof e.processEvent=="function"){let r=e.processEvent.bind(e),s=Object.assign((i,o)=>r(i,o,t),{id:e.name});t.addEventProcessor(s)}(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&p.log(`Integration installed: ${e.name}`)}function Ir(t,e){for(let n=0;n<t.length;n++)if(e(t[n])===!0)return n;return-1}function Sn(t,e,n,r,s){let{normalizeDepth:i=3,normalizeMaxBreadth:o=1e3}=t,a={...e,event_id:e.event_id||n.event_id||h(),timestamp:e.timestamp||F()},u=n.integrations||t.integrations.map(l=>l.name);Dr(a,t),vr(a,u),e.type===void 0&&kr(a,t.stackParser);let c=r;n.captureContext&&(c=k.clone(c).update(n.captureContext));let f=T(a),d=s&&s.getEventProcessors?s.getEventProcessors():[];if(c){if(c.getAttachments){let l=[...n.attachments||[],...c.getAttachments()];l.length&&(n.attachments=l)}f=c.applyToEvent(a,n,d)}else f=K([...d,...ue()],a,n);return f.then(l=>(l&&Or(l),typeof i=="number"&&i>0?wr(l,i,o):l))}function Dr(t,e){let{environment:n,release:r,dist:s,maxValueLength:i=250}=e;"environment"in t||(t.environment="environment"in e?n:W),t.release===void 0&&r!==void 0&&(t.release=r),t.dist===void 0&&s!==void 0&&(t.dist=s),t.message&&(t.message=A(t.message,i));let o=t.exception&&t.exception.values&&t.exception.values[0];o&&o.value&&(o.value=A(o.value,i));let a=t.request;a&&a.url&&(a.url=A(a.url,i))}var En=new WeakMap;function kr(t,e){let n=_._sentryDebugIds;if(!n)return;let r,s=En.get(e);s?r=s:(r=new Map,En.set(e,r));let i=Object.keys(n).reduce((o,a)=>{let u,c=r.get(a);c?u=c:(u=e(a),r.set(a,u));for(let f=u.length-1;f>=0;f--){let d=u[f];if(d.filename){o[d.filename]=n[a];break}}return o},{});try{t.exception.values.forEach(o=>{o.stacktrace.frames.forEach(a=>{a.filename&&(a.debug_id=i[a.filename])})})}catch{}}function Or(t){let e={};try{t.exception.values.forEach(r=>{r.stacktrace.frames.forEach(s=>{s.debug_id&&(s.abs_path?e[s.abs_path]=s.debug_id:s.filename&&(e[s.filename]=s.debug_id),delete s.debug_id)})})}catch{}if(Object.keys(e).length===0)return;t.debug_meta=t.debug_meta||{},t.debug_meta.images=t.debug_meta.images||[];let n=t.debug_meta.images;Object.keys(e).forEach(r=>{n.push({type:"sourcemap",code_file:r,debug_id:e[r]})})}function vr(t,e){e.length>0&&(t.sdk=t.sdk||{},t.sdk.integrations=[...t.sdk.integrations||[],...e])}function wr(t,e,n){if(!t)return null;let r={...t,...t.breadcrumbs&&{breadcrumbs:t.breadcrumbs.map(s=>({...s,...s.data&&{data:D(s.data,e,n)}}))},...t.user&&{user:D(t.user,e,n)},...t.contexts&&{contexts:D(t.contexts,e,n)},...t.extra&&{extra:D(t.extra,e,n)}};return t.contexts&&t.contexts.trace&&r.contexts&&(r.contexts.trace=t.contexts.trace,t.contexts.trace.data&&(r.contexts.trace.data=D(t.contexts.trace.data,e,n))),t.spans&&(r.spans=t.spans.map(s=>(s.data&&(s.data=D(s.data,e,n)),s))),r}var yn="Not capturing exception because it's already been captured.",Pe=class{constructor(e){if(this._options=e,this._integrations={},this._integrationsInitialized=!1,this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],e.dsn?this._dsn=me(e.dsn):(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&p.warn("No DSN provided, client will not send events."),this._dsn){let n=ln(this._dsn,e);this._transport=e.transport({recordDroppedEvent:this.recordDroppedEvent.bind(this),...e.transportOptions,url:n})}}captureException(e,n,r){if(xe(e)){(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&p.log(yn);return}let s=n&&n.event_id;return this._process(this.eventFromException(e,n).then(i=>this._captureEvent(i,n,r)).then(i=>{s=i})),s}captureMessage(e,n,r,s){let i=r&&r.event_id,o=pe(e)?this.eventFromMessage(String(e),n,r):this.eventFromException(e,r);return this._process(o.then(a=>this._captureEvent(a,r,s)).then(a=>{i=a})),i}captureEvent(e,n,r){if(n&&n.originalException&&xe(n.originalException)){(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&p.log(yn);return}let s=n&&n.event_id;return this._process(this._captureEvent(e,n,r).then(i=>{s=i})),s}captureSession(e){typeof e.release!="string"?(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&p.warn("Discarded session because of missing or non-string release"):(this.sendSession(e),w(e,{init:!1}))}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(e){let n=this._transport;return n?this._isClientDoneProcessing(e).then(r=>n.flush(e).then(s=>r&&s)):T(!0)}close(e){return this.flush(e).then(n=>(this.getOptions().enabled=!1,n))}getEventProcessors(){return this._eventProcessors}addEventProcessor(e){this._eventProcessors.push(e)}setupIntegrations(e){(e&&!this._integrationsInitialized||this._isEnabled()&&!this._integrationsInitialized)&&(this._integrations=gn(this,this._options.integrations),this._integrationsInitialized=!0)}getIntegrationById(e){return this._integrations[e]}getIntegration(e){try{return this._integrations[e.id]||null}catch{return(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&p.warn(`Cannot retrieve integration ${e.id} from the current Client`),null}}addIntegration(e){ht(this,e,this._integrations)}sendEvent(e,n={}){this.emit("beforeSendEvent",e,n);let r=mn(e,this._dsn,this._options._metadata,this._options.tunnel);for(let i of n.attachments||[])r=rt(r,it(i,this._options.transportOptions&&this._options.transportOptions.textEncoder));let s=this._sendEnvelope(r);s&&s.then(i=>this.emit("afterSendEvent",e,i),null)}sendSession(e){let n=_n(e,this._dsn,this._options._metadata,this._options.tunnel);this._sendEnvelope(n)}recordDroppedEvent(e,n,r){if(this._options.sendClientReports){let s=`${e}:${n}`;(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&p.log(`Adding outcome: "${s}"`),this._outcomes[s]=this._outcomes[s]+1||1}}on(e,n){this._hooks[e]||(this._hooks[e]=[]),this._hooks[e].push(n)}emit(e,...n){this._hooks[e]&&this._hooks[e].forEach(r=>r(...n))}_updateSessionFromEvent(e,n){let r=!1,s=!1,i=n.exception&&n.exception.values;if(i){s=!0;for(let u of i){let c=u.mechanism;if(c&&c.handled===!1){r=!0;break}}}let o=e.status==="ok";(o&&e.errors===0||o&&r)&&(w(e,{...r&&{status:"crashed"},errors:e.errors||Number(s||r)}),this.captureSession(e))}_isClientDoneProcessing(e){return new S(n=>{let r=0,s=1,i=setInterval(()=>{this._numProcessing==0?(clearInterval(i),n(!0)):(r+=s,e&&r>=e&&(clearInterval(i),n(!1)))},s)})}_isEnabled(){return this.getOptions().enabled!==!1&&this._transport!==void 0}_prepareEvent(e,n,r){let s=this.getOptions(),i=Object.keys(this._integrations);return!n.integrations&&i.length>0&&(n.integrations=i),this.emit("preprocessEvent",e,n),Sn(s,e,n,r,this).then(o=>{if(o===null)return o;let{propagationContext:a}=o.sdkProcessingMetadata||{};if(!(o.contexts&&o.contexts.trace)&&a){let{traceId:c,spanId:f,parentSpanId:d,dsc:l}=a;o.contexts={trace:{trace_id:c,span_id:f,parent_span_id:d},...o.contexts};let m=l||J(c,this,r);o.sdkProcessingMetadata={dynamicSamplingContext:m,...o.sdkProcessingMetadata}}return o})}_captureEvent(e,n={},r){return this._processEvent(e,n,r).then(s=>s.event_id,s=>{if(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__){let i=s;i.logLevel==="log"?p.log(i.message):p.warn(i)}})}_processEvent(e,n,r){let s=this.getOptions(),{sampleRate:i}=s,o=bn(e),a=Tn(e),u=e.type||"error",c=`before send for type \`${u}\``;if(a&&typeof i=="number"&&Math.random()>i)return this.recordDroppedEvent("sample_rate","error",e),B(new y(`Discarding event because it's not included in the random sample (sampling rate = ${i})`,"log"));let f=u==="replay_event"?"replay":u;return this._prepareEvent(e,n,r).then(d=>{if(d===null)throw this.recordDroppedEvent("event_processor",f,e),new y("An event processor returned `null`, will not send event.","log");if(n.data&&n.data.__sentry__===!0)return d;let m=Pr(s,d,n);return Ar(m,c)}).then(d=>{if(d===null)throw this.recordDroppedEvent("before_send",f,e),new y(`${c} returned \`null\`, will not send event.`,"log");let l=r&&r.getSession();!o&&l&&this._updateSessionFromEvent(l,d);let m=d.transaction_info;if(o&&m&&d.transaction!==e.transaction){let U="custom";d.transaction_info={...m,source:U}}return this.sendEvent(d,n),d}).then(null,d=>{throw d instanceof y?d:(this.captureException(d,{data:{__sentry__:!0},originalException:d}),new y(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${d}`))})}_process(e){this._numProcessing++,e.then(n=>(this._numProcessing--,n),n=>(this._numProcessing--,n))}_sendEnvelope(e){if(this.emit("beforeEnvelope",e),this._isEnabled()&&this._transport)return this._transport.send(e).then(null,n=>{(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&p.error("Error while sending event:",n)});(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&p.error("Transport disabled")}_clearOutcomes(){let e=this._outcomes;return this._outcomes={},Object.keys(e).map(n=>{let[r,s]=n.split(":");return{reason:r,category:s,quantity:e[n]}})}};function Ar(t,e){let n=`${e} must return \`null\` or a valid event.`;if(G(t))return t.then(r=>{if(!R(r)&&r!==null)throw new y(n);return r},r=>{throw new y(`${e} rejected with ${r}`)});if(!R(t)&&t!==null)throw new y(n);return t}function Pr(t,e,n){let{beforeSend:r,beforeSendTransaction:s}=t;return Tn(e)&&r?r(e,n):bn(e)&&s?s(e,n):e}function Tn(t){return t.type===void 0}function bn(t){return t.type==="transaction"}function Rn(t,e,n,r,s){let i={sent_at:new Date().toISOString()};n&&n.sdk&&(i.sdk={name:n.sdk.name,version:n.sdk.version}),r&&s&&(i.dsn=P(s)),e&&(i.trace=g(e));let o=Cr(t);return C(i,[o])}function Cr(t){return[{type:"check_in"},t]}var de=class extends Pe{constructor(e){pn(),super(e)}eventFromException(e,n){return T(dt(b,this._options.stackParser,e,n))}eventFromMessage(e,n="info",r){return T(ft(this._options.stackParser,e,n,r,this._options.attachStacktrace))}captureException(e,n,r){if(this._options.autoSessionTracking&&this._sessionFlusher&&r){let s=r.getRequestSession();s&&s.status==="ok"&&(s.status="errored")}return super.captureException(e,n,r)}captureEvent(e,n,r){if(this._options.autoSessionTracking&&this._sessionFlusher&&r&&(e.type||"exception")==="exception"&&e.exception&&e.exception.values&&e.exception.values.length>0){let o=r.getRequestSession();o&&o.status==="ok"&&(o.status="errored")}return super.captureEvent(e,n,r)}close(e){return this._sessionFlusher&&this._sessionFlusher.close(),super.close(e)}initSessionFlusher(){let{release:e,environment:n}=this._options;e?this._sessionFlusher=new Ae(this,{release:e,environment:n}):(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&p.warn("Cannot initialise an instance of SessionFlusher if no release is provided!")}captureCheckIn(e,n,r){let s=e.status!=="in_progress"&&e.checkInId?e.checkInId:h();if(!this._isEnabled())return(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&p.warn("SDK not enabled, will not capture checkin."),s;let i=this.getOptions(),{release:o,environment:a,tunnel:u}=i,c={check_in_id:s,monitor_slug:e.monitorSlug,status:e.status,release:o,environment:a};e.status!=="in_progress"&&(c.duration=e.duration),n&&(c.monitor_config={schedule:n.schedule,checkin_margin:n.checkinMargin,max_runtime:n.maxRuntime,timezone:n.timezone});let[f,d]=this._getTraceInfoFromScope(r);d&&(c.contexts={trace:d});let l=Rn(c,f,this.getSdkMetadata(),u,this.getDsn());return(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&p.info("Sending checkin:",e.monitorSlug,e.status),this._sendEnvelope(l),s}_captureRequestSession(){this._sessionFlusher?this._sessionFlusher.incrementSessionStatusCount():(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&p.warn("Discarded request mode session because autoSessionTracking option was disabled")}_prepareEvent(e,n,r){return this._options.platform&&(e.platform=e.platform||this._options.platform),this._options.runtime&&(e.contexts={...e.contexts,runtime:(e.contexts||{}).runtime||this._options.runtime}),this._options.serverName&&(e.server_name=e.server_name||this._options.serverName),super._prepareEvent(e,n,r)}_getTraceInfoFromScope(e){if(!e)return[void 0,void 0];let n=e.getSpan();if(n)return[n.transaction?n.transaction.getDynamicSamplingContext():void 0,n.getTraceContext()];let{traceId:r,spanId:s,parentSpanId:i,dsc:o}=e.getPropagationContext(),a={trace_id:r,span_id:s,parent_span_id:i};return o?[o,a]:[J(r,this,e),a]}};var Ur=30;function gt(t,e,n=Ze(t.bufferSize||Ur)){let r={},s=o=>n.drain(o);function i(o){let a=[];if(Ie(o,(d,l)=>{let m=De(l);if(at(r,m)){let U=xn(d,l);t.recordDroppedEvent("ratelimit_backoff",m,U)}else a.push(d)}),a.length===0)return T();let u=C(o[0],a),c=d=>{Ie(u,(l,m)=>{let U=xn(l,m);t.recordDroppedEvent(d,De(m),U)})},f=()=>e({body:st(u,t.textEncoder)}).then(d=>(d.statusCode!==void 0&&(d.statusCode<200||d.statusCode>=300)&&(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&p.warn(`Sentry responded with status code ${d.statusCode} to sent event.`),r=ct(r,d),d),d=>{throw c("network_error"),d});return n.add(f).then(d=>d,d=>{if(d instanceof y)return(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&p.error("Skipped sending event because buffer is full."),c("queue_overflow"),T();throw d})}return i.__sentry__baseTransport__=!0,{send:i,flush:s}}function xn(t,e){if(!(e!=="event"&&e!=="transaction"))return Array.isArray(t)?t[1]:void 0}function In(t){return typeof t=="object"&&t!==null}function Mr(t){return In(t)&&"handled"in t&&typeof t.handled=="boolean"&&"type"in t&&typeof t.type=="string"}function Gr(t){return In(t)&&"mechanism"in t&&Mr(t.mechanism)}function Br(){if(_.SENTRY_RELEASE&&_.SENTRY_RELEASE.id)return _.SENTRY_RELEASE.id}function Nn(t,e){return t!==void 0?(t[e[0]]=e[1],t):{[e[0]]:e[1]}}function Dn(t,e){return t(e.stack||"",1)}function Fr(t){let e=t&&t.message;return e?e.error&&typeof e.error.message=="string"?e.error.message:e:"No error message"}function kn(t,e){let n={type:e.name||e.constructor.name,value:Fr(e)},r=Dn(t,e);return r.length&&(n.stacktrace={frames:r}),n.type===void 0&&n.value===""&&(n.value="Unrecoverable error caught"),n}function Lr(t,e,n,r){let s,o=(r&&r.data&&Gr(r.data)?r.data.mechanism:void 0)??{handled:!0,type:"generic"};if(M(n))s=n;else{if(R(n)){let u=`Non-Error exception captured with keys: ${te(n)}`,c=t?.getClient(),f=c&&c.getOptions().normalizeDepth;t?.configureScope(d=>{d.setExtra("__serialized__",z(n,f))}),s=r&&r.syntheticException||new Error(u),s.message=u}else s=r&&r.syntheticException||new Error(n),s.message=n;o.synthetic=!0}let a={exception:{values:[kn(e,s)]}};return ie(a,void 0,void 0),oe(a,o),{...a,event_id:r&&r.event_id}}function Yr(t,e,n="info",r,s){let i={event_id:r&&r.event_id,level:n,message:e};if(s&&r&&r.syntheticException){let o=Dn(t,r.syntheticException);o.length&&(i.exception={values:[{value:e,stacktrace:{frames:o}}]})}return i}var $r=5,Ge=class{name=Ge.id;limit;constructor(e={}){this.limit=e.limit||$r}setupOnce(e,n){let r=n().getClient();r&&e((s,i)=>{let o=n().getIntegration(Ge);return o?jr(r.getOptions().stackParser,o.limit,s,i):s})}},Ce=Ge;fe(Ce,"id","LinkedErrors");function jr(t,e,n,r){if(!n.exception||!n.exception.values||!r||!O(r.originalException,Error))return n;let s=On(t,e,r.originalException);return n.exception.values=[...s,...n.exception.values],n}function On(t,e,n,r=[]){if(!O(n.cause,Error)||r.length+1>=e)return r;let s=kn(t,n.cause);return On(t,e,n.cause,[s,...r])}var Hr={allowedHeaders:["CF-RAY","CF-Worker"]},X,Be=class{constructor(e={}){fe(this,"name",Be.id);Tt(this,X,void 0);bt(this,X,{...Hr,...e})}setupOnce(e,n){n().getClient()&&e(s=>{let{sdkProcessingMetadata:i}=s;return!n().getIntegration(Be)||!i||("request"in i&&i.request instanceof Request&&(s.request=zr(i.request,Fe(this,X)),s.user=qr(s.user??{},i.request,Fe(this,X))),"requestData"in i&&(s.request?s.request.data=i.requestData:s.request={data:i.requestData})),s})}},Ue=Be;X=new WeakMap,fe(Ue,"id","RequestData");function qr(t,e,n){let r=e.headers.get("CF-Connecting-IP"),{allowedIps:s}=n,i={...t};return!("ip_address"in t)&&r&&s!==void 0&&Wr(r,s)&&(i.ip_address=r),Object.keys(i).length>0?i:void 0}function zr(t,e){let n=t.headers.get("cookie"),r;if(n)try{r=Kr(n)}catch{}let s={};for(let[c,f]of t.headers.entries())c!=="cookie"&&(s[c]=f);let i={method:t.method,cookies:r,headers:s};try{let c=new URL(t.url);i.url=`${c.protocol}//${c.hostname}${c.pathname}`,i.query_string=c.search}catch{let f=t.url.indexOf("?");f<0?i.url=t.url:(i.url=t.url.substr(0,f),i.query_string=t.url.substr(f+1))}let{allowedHeaders:o,allowedCookies:a,allowedSearchParams:u}=e;if(o!==void 0&&i.headers?(i.headers=Et(i.headers,o),Object.keys(i.headers).length===0&&delete i.headers):delete i.headers,a!==void 0&&i.cookies?(i.cookies=Et(i.cookies,a),Object.keys(i.cookies).length===0&&delete i.cookies):delete i.cookies,u!==void 0){let c=Object.fromEntries(new URLSearchParams(i.query_string)),f=new URLSearchParams;Object.keys(Et(c,u)).forEach(d=>{f.set(d,c[d])}),i.query_string=f.toString()}else delete i.query_string;return i}function Wr(t,e){return typeof e=="boolean"?e:e instanceof RegExp?e.test(t):Array.isArray(e)?e.map(r=>r.toLowerCase()).includes(t):!1}function Et(t,e){let n=()=>!1;if(typeof e=="boolean")return e?t:{};if(e instanceof RegExp)n=r=>e.test(r);else if(Array.isArray(e)){let r=e.map(s=>s.toLowerCase());n=s=>r.includes(s.toLowerCase())}else return{};return Object.keys(t).filter(n).reduce((r,s)=>(r[s]=t[s],r),{})}function Kr(t){if(typeof t!="string")return{};try{return t.split(";").map(e=>e.split("=")).reduce((e,[n,r])=>(e[decodeURIComponent(n.trim())]=decodeURIComponent(r.trim()),e),{})}catch{return{}}}function Vr(t,e){let n={};return t.forEach(r=>{n[r.name]=r,r.setupOnce(s=>{e.getScope()?.addEventProcessor(s)},()=>e)}),n}var St=class extends de{#e=null;constructor(e){e._metadata=e._metadata||{},e._metadata.sdk=e._metadata.sdk||{name:"toucan-js",packages:[{name:"npm:toucan-js",version:"3.3.1"}],version:"3.3.1"},super(e)}setupIntegrations(){this._isEnabled()&&!this._integrationsInitialized&&this.#e&&(this._integrations=Vr(this._options.integrations,this.#e),this._integrationsInitialized=!0)}eventFromException(e,n){return T(Lr(this.#e,this._options.stackParser,e,n))}eventFromMessage(e,n="info",r){return T(Yr(this._options.stackParser,e,n,r,this._options.attachStacktrace))}_prepareEvent(e,n,r){return e.platform=e.platform||"javascript",this.getOptions().request&&(e.sdkProcessingMetadata=Nn(e.sdkProcessingMetadata,["request",this.getOptions().request])),this.getOptions().requestData&&(e.sdkProcessingMetadata=Nn(e.sdkProcessingMetadata,["requestData",this.getOptions().requestData])),super._prepareEvent(e,n,r)}getSdk(){return this.#e}setSdk(e){this.#e=e}setRequestBody(e){this.getOptions().requestData=e}setEnabled(e){this.getOptions().enabled=e}};function Jr(t){let[e,n]=Ke(t);return[e,s=>{let i=n(s);if(i){let o=i.filename;i.abs_path=o!==void 0&&!o.startsWith("/")?`/${o}`:o,i.in_app=o!==void 0}return i}]}function Xr(t){if(t)return Xe(t,".js")}var Zr=ge(Jr(Xr));function Qr(t){function e({body:n}){try{let s=(t.fetcher??fetch)(t.url,{method:"POST",headers:t.headers,body:n}).then(i=>({statusCode:i.status,headers:{"retry-after":i.headers.get("Retry-After"),"x-sentry-rate-limits":i.headers.get("X-Sentry-Rate-Limits")}}));return t.context&&t.context.waitUntil(s),s}catch(r){return B(r)}}return gt(t,e)}var Me=class extends Y{constructor(e){if(e.defaultIntegrations=e.defaultIntegrations===!1?[]:[...Array.isArray(e.defaultIntegrations)?e.defaultIntegrations:[new Ue(e.requestDataOptions),new Ce]],e.release===void 0){let r=Br();r!==void 0&&(e.release=r)}let n=new St({...e,transport:Qr,integrations:mt(e),stackParser:We(e.stackParser||Zr),transportOptions:{...e.transportOptions,context:e.context}});super(n),n.setSdk(this),n.setupIntegrations()}setRequestBody(e){this.getClient()?.setRequestBody(e)}setEnabled(e){this.getClient()?.setEnabled(e)}captureCheckIn(e,n,r){return e.status==="in_progress"&&this.setContext("monitor",{slug:e.monitorSlug}),this.getClient().captureCheckIn(e,n,r)}};function vn(t,e,n,r,s){if(!(n&&r&&s))return;let i=new Me({dsn:n,request:t,context:e,sampleRate:1,requestDataOptions:{allowedHeaders:["user-agent","cf-challenge","accept-encoding","accept-language","cf-ray","content-length","content-type","host"],allowedSearchParams:/(.*)/},transportOptions:{headers:{"CF-Access-Client-ID":r,"CF-Access-Client-Secret":s}}}),o=t.cf?.colo??"UNKNOWN";i.setTag("colo",o);let a=t.headers.get("user-agent")??"UA UNKNOWN";return i.setUser({userAgent:a,colo:o}),i}var qa={async fetch(t,e,n){let r,s=t.clone();try{return r=vn(t,n,e.SENTRY_DSN,e.SENTRY_ACCESS_CLIENT_ID,e.SENTRY_ACCESS_CLIENT_SECRET),e.CONFIG.has_user_worker?await e.ASSET_WORKER.unstable_canFetch(t)?await e.ASSET_WORKER.fetch(s):e.USER_WORKER.fetch(s):await e.ASSET_WORKER.fetch(t)}catch(i){throw r&&r.captureException(i),i}}};export{qa as default};
