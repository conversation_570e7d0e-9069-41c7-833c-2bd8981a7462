var Jn=Object.defineProperty;var Xn=(t,e,n)=>e in t?Jn(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var he=(t,e,n)=>(Xn(t,typeof e!="symbol"?e+"":e,n),n),Ct=(t,e,n)=>{if(!e.has(t))throw TypeError("Cannot "+n)};var Xe=(t,e,n)=>(Ct(t,e,"read from private field"),n?n.call(t):e.get(t)),Mt=(t,e,n)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,n)},Ut=(t,e,n,r)=>(Ct(t,e,"write to private field"),r?r.call(t,n):e.set(t,n),n);import{WorkerEntrypoint as As}from"cloudflare:workers";var Bt=Object.prototype.toString;function B(t){switch(Bt.call(t)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return O(t,Error)}}function $t(t,e){return Bt.call(t)===`[object ${e}]`}function j(t){return $t(t,"String")}function ge(t){return t===null||typeof t!="object"&&typeof t!="function"}function R(t){return $t(t,"Object")}function Ze(t){return typeof Event<"u"&&O(t,Event)}function Qe(t){return typeof Element<"u"&&O(t,Element)}function $(t){return!!(t&&t.then&&typeof t.then=="function")}function et(t){return R(t)&&"nativeEvent"in t&&"preventDefault"in t&&"stopPropagation"in t}function te(t){return typeof t=="number"&&t!==t}function O(t,e){try{return t instanceof e}catch{return!1}}function tt(t){return!!(typeof t=="object"&&t!==null&&(t.__isVue||t._isVue))}function P(t,e=0){return typeof t!="string"||e===0||t.length<=e?t:`${t.slice(0,e)}...`}function Ee(t){return t&&t.Math==Math?t:void 0}var m=typeof globalThis=="object"&&Ee(globalThis)||typeof window=="object"&&Ee(window)||typeof self=="object"&&Ee(self)||typeof global=="object"&&Ee(global)||function(){return this}()||{};function N(){return m}function ne(t,e,n){let r=n||m,s=r.__SENTRY__=r.__SENTRY__||{};return s[t]||(s[t]=e())}var Us=N(),Zn=80;function Lt(t,e={}){if(!t)return"<unknown>";try{let n=t,r=5,s=[],i=0,o=0,a=" > ",u=a.length,c,d=Array.isArray(e)?e:e.keyAttrs,l=!Array.isArray(e)&&e.maxStringLength||Zn;for(;n&&i++<r&&(c=Qn(n,d),!(c==="html"||i>1&&o+s.length*u+c.length>=l));)s.push(c),o+=c.length,n=n.parentNode;return s.reverse().join(a)}catch{return"<unknown>"}}function Qn(t,e){let n=t,r=[],s,i,o,a,u;if(!n||!n.tagName)return"";r.push(n.tagName.toLowerCase());let c=e&&e.length?e.filter(l=>n.getAttribute(l)).map(l=>[l,n.getAttribute(l)]):null;if(c&&c.length)c.forEach(l=>{r.push(`[${l[0]}="${l[1]}"]`)});else if(n.id&&r.push(`#${n.id}`),s=n.className,s&&j(s))for(i=s.split(/\s+/),u=0;u<i.length;u++)r.push(`.${i[u]}`);let d=["aria-label","type","name","title","alt"];for(u=0;u<d.length;u++)o=d[u],a=n.getAttribute(o),a&&r.push(`[${o}="${a}"]`);return r.join("")}var er="Sentry Logger ",re=["debug","info","warn","error","log","assert","trace"],q={};function ye(t){if(!("console"in m))return t();let e=m.console,n={},r=Object.keys(q);r.forEach(s=>{let i=q[s];n[s]=e[s],e[s]=i});try{return t()}finally{r.forEach(s=>{e[s]=n[s]})}}function tr(){let t=!1,e={enable:()=>{t=!0},disable:()=>{t=!1},isEnabled:()=>t};return typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__?re.forEach(n=>{e[n]=(...r)=>{t&&ye(()=>{m.console[n](`${er}[${n}]:`,...r)})}}):re.forEach(n=>{e[n]=()=>{}}),e}var f=tr();var nr=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function rr(t){return t==="http"||t==="https"}function C(t,e=!1){let{host:n,path:r,pass:s,port:i,projectId:o,protocol:a,publicKey:u}=t;return`${a}://${u}${e&&s?`:${s}`:""}@${n}${i?`:${i}`:""}/${r&&`${r}/`}${o}`}function Gt(t){let e=nr.exec(t);if(!e){console.error(`Invalid Sentry Dsn: ${t}`);return}let[n,r,s="",i,o="",a]=e.slice(1),u="",c=a,d=c.split("/");if(d.length>1&&(u=d.slice(0,-1).join("/"),c=d.pop()),c){let l=c.match(/^\d+/);l&&(c=l[0])}return Ft({host:i,pass:s,path:u,projectId:c,port:o,protocol:n,publicKey:r})}function Ft(t){return{protocol:t.protocol,publicKey:t.publicKey||"",pass:t.pass||"",host:t.host,port:t.port||"",path:t.path||"",projectId:t.projectId}}function sr(t){if(!(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__))return!0;let{port:e,projectId:n,protocol:r}=t;return["protocol","publicKey","host","projectId"].find(o=>t[o]?!1:(f.error(`Invalid Sentry Dsn: ${o} missing`),!0))?!1:n.match(/^\d+$/)?rr(r)?e&&isNaN(parseInt(e,10))?(f.error(`Invalid Sentry Dsn: Invalid port ${e}`),!1):!0:(f.error(`Invalid Sentry Dsn: Invalid protocol ${r}`),!1):(f.error(`Invalid Sentry Dsn: Invalid projectId ${n}`),!1)}function Se(t){let e=typeof t=="string"?Gt(t):Ft(t);if(!(!e||!sr(e)))return e}var T=class extends Error{constructor(e,n="warn"){super(e),this.message=e,this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype),this.logLevel=n}};function I(t,e,n){if(!(e in t))return;let r=t[e],s=n(r);typeof s=="function"&&jt(s,r),t[e]=s}function W(t,e,n){try{Object.defineProperty(t,e,{value:n,writable:!0,configurable:!0})}catch{(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&f.log(`Failed to add non-enumerable property "${e}" to object`,t)}}function jt(t,e){try{let n=e.prototype||{};t.prototype=e.prototype=n,W(t,"__sentry_original__",e)}catch{}}function rt(t){return Object.keys(t).map(e=>`${encodeURIComponent(e)}=${encodeURIComponent(t[e])}`).join("&")}function Te(t){if(B(t))return{message:t.message,name:t.name,stack:t.stack,...Ht(t)};if(Ze(t)){let e={type:t.type,target:Yt(t.target),currentTarget:Yt(t.currentTarget),...Ht(t)};return typeof CustomEvent<"u"&&O(t,CustomEvent)&&(e.detail=t.detail),e}else return t}function Yt(t){try{return Qe(t)?Lt(t):Object.prototype.toString.call(t)}catch{return"<unknown>"}}function Ht(t){if(typeof t=="object"&&t!==null){let e={};for(let n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}else return{}}function se(t,e=40){let n=Object.keys(Te(t));if(n.sort(),!n.length)return"[object has no keys]";if(n[0].length>=e)return P(n[0],e);for(let r=n.length;r>0;r--){let s=n.slice(0,r).join(", ");if(!(s.length>e))return r===n.length?s:P(s,e)}return""}function E(t){return nt(t,new Map)}function nt(t,e){if(R(t)){let n=e.get(t);if(n!==void 0)return n;let r={};e.set(t,r);for(let s of Object.keys(t))typeof t[s]<"u"&&(r[s]=nt(t[s],e));return r}if(Array.isArray(t)){let n=e.get(t);if(n!==void 0)return n;let r=[];return e.set(t,r),t.forEach(s=>{r.push(nt(s,e))}),r}return t}function ir(t,e=!1){return!(e||t&&!t.startsWith("/")&&!t.includes(":\\")&&!t.startsWith(".")&&!t.match(/^[a-zA-Z]([a-zA-Z0-9.\-+])*:\/\//))&&t!==void 0&&!t.includes("node_modules/")}function qt(t){let e=/^\s*[-]{4,}$/,n=/at (?:async )?(?:(.+?)\s+\()?(?:(.+):(\d+):(\d+)?|([^)]+))\)?/;return r=>{let s=r.match(n);if(s){let i,o,a,u,c;if(s[1]){a=s[1];let p=a.lastIndexOf(".");if(a[p-1]==="."&&p--,p>0){i=a.slice(0,p),o=a.slice(p+1);let _=i.indexOf(".Module");_>0&&(a=a.slice(_+1),i=i.slice(0,_))}u=void 0}o&&(u=i,c=o),o==="<anonymous>"&&(c=void 0,a=void 0),a===void 0&&(c=c||"<anonymous>",a=u?`${u}.${c}`:c);let d=s[2]&&s[2].startsWith("file://")?s[2].slice(7):s[2],l=s[5]==="native";return!d&&s[5]&&!l&&(d=s[5]),{filename:d,module:t?t(d):void 0,function:a,lineno:parseInt(s[3],10)||void 0,colno:parseInt(s[4],10)||void 0,in_app:ir(d,l)}}if(r.match(e))return{filename:r}}}var Kt=50,Wt=/\(error: (.*)\)/,zt=/captureMessage|captureException/;function be(...t){let e=t.sort((n,r)=>n[0]-r[0]).map(n=>n[1]);return(n,r=0)=>{let s=[],i=n.split(`
`);for(let o=r;o<i.length;o++){let a=i[o];if(a.length>1024)continue;let u=Wt.test(a)?a.replace(Wt,"$1"):a;if(!u.match(/\S*Error: /)){for(let c of e){let d=c(u);if(d){s.push(d);break}}if(s.length>=Kt)break}}return Vt(s)}}function it(t){return Array.isArray(t)?be(...t):t}function Vt(t){if(!t.length)return[];let e=Array.from(t);return/sentryWrapped/.test(e[e.length-1].function||"")&&e.pop(),e.reverse(),zt.test(e[e.length-1].function||"")&&(e.pop(),zt.test(e[e.length-1].function||"")&&e.pop()),e.slice(0,Kt).map(n=>({...n,filename:n.filename||e[e.length-1].filename,function:n.function||"?"}))}var st="<anonymous>";function ie(t){try{return!t||typeof t!="function"?st:t.name||st}catch{return st}}function ot(t){return[90,qt(t)]}var at=N();function or(){if(!("fetch"in at))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch{return!1}}function Jt(t){return t&&/^function fetch\(\)\s+\{\s+\[native code\]\s+\}$/.test(t.toString())}function Xt(){if(!or())return!1;if(Jt(at.fetch))return!0;let t=!1,e=at.document;if(e&&typeof e.createElement=="function")try{let n=e.createElement("iframe");n.hidden=!0,e.head.appendChild(n),n.contentWindow&&n.contentWindow.fetch&&(t=Jt(n.contentWindow.fetch)),e.head.removeChild(n)}catch(n){(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&f.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",n)}return t}var xe=N();function Zt(){let t=xe.chrome,e=t&&t.app&&t.app.runtime,n="history"in xe&&!!xe.history.pushState&&!!xe.history.replaceState;return!e&&n}var y=N(),oe="__sentry_xhr_v2__",ae={},Qt={};function ar(t){if(!Qt[t])switch(Qt[t]=!0,t){case"console":cr();break;case"dom":on();break;case"xhr":sn();break;case"fetch":ur();break;case"history":lr();break;case"error":_r();break;case"unhandledrejection":mr();break;default:(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&f.warn("unknown instrumentation type:",t);return}}function we(t,e){ae[t]=ae[t]||[],ae[t].push(e),ar(t)}function D(t,e){if(!(!t||!ae[t]))for(let n of ae[t]||[])try{n(e)}catch(r){(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&f.error(`Error while triggering instrumentation handler.
Type: ${t}
Name: ${ie(n)}
Error:`,r)}}function cr(){"console"in m&&re.forEach(function(t){t in m.console&&I(m.console,t,function(e){return q[t]=e,function(...n){D("console",{args:n,level:t});let r=q[t];r&&r.apply(m.console,n)}})})}function ur(){Xt()&&I(m,"fetch",function(t){return function(...e){let{method:n,url:r}=rn(e),s={args:e,fetchData:{method:n,url:r},startTimestamp:Date.now()};return D("fetch",{...s}),t.apply(m,e).then(i=>(D("fetch",{...s,endTimestamp:Date.now(),response:i}),i),i=>{throw D("fetch",{...s,endTimestamp:Date.now(),error:i}),i})}})}function ct(t,e){return!!t&&typeof t=="object"&&!!t[e]}function en(t){return typeof t=="string"?t:t?ct(t,"url")?t.url:t.toString?t.toString():"":""}function rn(t){if(t.length===0)return{method:"GET",url:""};if(t.length===2){let[n,r]=t;return{url:en(n),method:ct(r,"method")?String(r.method).toUpperCase():"GET"}}let e=t[0];return{url:en(e),method:ct(e,"method")?String(e.method).toUpperCase():"GET"}}function sn(){if(!y.XMLHttpRequest)return;let t=XMLHttpRequest.prototype;I(t,"open",function(e){return function(...n){let r=Date.now(),s=n[1],i=this[oe]={method:j(n[0])?n[0].toUpperCase():n[0],url:n[1],request_headers:{}};j(s)&&i.method==="POST"&&s.match(/sentry_key/)&&(this.__sentry_own_request__=!0);let o=()=>{let a=this[oe];if(a&&this.readyState===4){try{a.status_code=this.status}catch{}D("xhr",{args:n,endTimestamp:Date.now(),startTimestamp:r,xhr:this})}};return"onreadystatechange"in this&&typeof this.onreadystatechange=="function"?I(this,"onreadystatechange",function(a){return function(...u){return o(),a.apply(this,u)}}):this.addEventListener("readystatechange",o),I(this,"setRequestHeader",function(a){return function(...u){let[c,d]=u,l=this[oe];return l&&(l.request_headers[c.toLowerCase()]=d),a.apply(this,u)}}),e.apply(this,n)}}),I(t,"send",function(e){return function(...n){let r=this[oe];return r&&n[0]!==void 0&&(r.body=n[0]),D("xhr",{args:n,startTimestamp:Date.now(),xhr:this}),e.apply(this,n)}})}var Re;function lr(){if(!Zt())return;let t=y.onpopstate;y.onpopstate=function(...n){let r=y.location.href,s=Re;if(Re=r,D("history",{from:s,to:r}),t)try{return t.apply(this,n)}catch{}};function e(n){return function(...r){let s=r.length>2?r[2]:void 0;if(s){let i=Re,o=String(s);Re=o,D("history",{from:i,to:o})}return n.apply(this,r)}}I(y.history,"pushState",e),I(y.history,"replaceState",e)}var dr=1e3,tn,Ie;function fr(t,e){if(t.type!==e.type)return!1;try{if(t.target!==e.target)return!1}catch{}return!0}function pr(t){if(t.type!=="keypress")return!1;try{let e=t.target;if(!e||!e.tagName)return!0;if(e.tagName==="INPUT"||e.tagName==="TEXTAREA"||e.isContentEditable)return!1}catch{}return!0}function nn(t,e=!1){return n=>{if(!n||n._sentryCaptured||pr(n))return;W(n,"_sentryCaptured",!0);let r=n.type==="keypress"?"input":n.type;(Ie===void 0||!fr(Ie,n))&&(t({event:n,name:r,global:e}),Ie=n),clearTimeout(tn),tn=y.setTimeout(()=>{Ie=void 0},dr)}}function on(){if(!y.document)return;let t=D.bind(null,"dom"),e=nn(t,!0);y.document.addEventListener("click",e,!1),y.document.addEventListener("keypress",e,!1),["EventTarget","Node"].forEach(n=>{let r=y[n]&&y[n].prototype;!r||!r.hasOwnProperty||!r.hasOwnProperty("addEventListener")||(I(r,"addEventListener",function(s){return function(i,o,a){if(i==="click"||i=="keypress")try{let u=this,c=u.__sentry_instrumentation_handlers__=u.__sentry_instrumentation_handlers__||{},d=c[i]=c[i]||{refCount:0};if(!d.handler){let l=nn(t);d.handler=l,s.call(this,i,l,a)}d.refCount++}catch{}return s.call(this,i,o,a)}}),I(r,"removeEventListener",function(s){return function(i,o,a){if(i==="click"||i=="keypress")try{let u=this,c=u.__sentry_instrumentation_handlers__||{},d=c[i];d&&(d.refCount--,d.refCount<=0&&(s.call(this,i,d.handler,a),d.handler=void 0,delete c[i]),Object.keys(c).length===0&&delete u.__sentry_instrumentation_handlers__)}catch{}return s.call(this,i,o,a)}}))})}var Ne=null;function _r(){Ne=y.onerror,y.onerror=function(t,e,n,r,s){return D("error",{column:r,error:s,line:n,msg:t,url:e}),Ne&&!Ne.__SENTRY_LOADER__?Ne.apply(this,arguments):!1},y.onerror.__SENTRY_INSTRUMENTED__=!0}var De=null;function mr(){De=y.onunhandledrejection,y.onunhandledrejection=function(t){return D("unhandledrejection",t),De&&!De.__SENTRY_LOADER__?De.apply(this,arguments):!0},y.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}function an(){return typeof __SENTRY_BROWSER_BUNDLE__<"u"&&!!__SENTRY_BROWSER_BUNDLE__}function cn(){return!an()&&Object.prototype.toString.call(typeof process<"u"?process:0)==="[object process]"}function un(t,e){return t.require(e)}function ln(){let t=typeof WeakSet=="function",e=t?new WeakSet:[];function n(s){if(t)return e.has(s)?!0:(e.add(s),!1);for(let i=0;i<e.length;i++)if(e[i]===s)return!0;return e.push(s),!1}function r(s){if(t)e.delete(s);else for(let i=0;i<e.length;i++)if(e[i]===s){e.splice(i,1);break}}return[n,r]}function g(){let t=m,e=t.crypto||t.msCrypto,n=()=>Math.random()*16;try{if(e&&e.randomUUID)return e.randomUUID().replace(/-/g,"");e&&e.getRandomValues&&(n=()=>e.getRandomValues(new Uint8Array(1))[0])}catch{}return([1e7]+1e3+4e3+8e3+1e11).replace(/[018]/g,r=>(r^(n()&15)>>r/4).toString(16))}function hr(t){return t.exception&&t.exception.values?t.exception.values[0]:void 0}function ce(t,e,n){let r=t.exception=t.exception||{},s=r.values=r.values||[],i=s[0]=s[0]||{};i.value||(i.value=e||""),i.type||(i.type=n||"Error")}function ue(t,e){let n=hr(t);if(!n)return;let r={type:"generic",handled:!0},s=n.mechanism;if(n.mechanism={...r,...s,...e},e&&"data"in e){let i={...s&&s.data,...e.data};n.mechanism.data=i}}function Ae(t){if(t&&t.__sentry_captured__)return!0;try{W(t,"__sentry_captured__",!0)}catch{}return!1}function le(t){return Array.isArray(t)?t:[t]}function w(t,e=100,n=1/0){try{return Oe("",t,e,n)}catch(r){return{ERROR:`**non-serializable** (${r})`}}}function z(t,e=3,n=100*1024){let r=w(t,e);return Sr(r)>n?z(t,e-1,n):r}function Oe(t,e,n=1/0,r=1/0,s=ln()){let[i,o]=s;if(e==null||["number","boolean","string"].includes(typeof e)&&!te(e))return e;let a=gr(t,e);if(!a.startsWith("[object "))return a;if(e.__sentry_skip_normalization__)return e;let u=typeof e.__sentry_override_normalization_depth__=="number"?e.__sentry_override_normalization_depth__:n;if(u===0)return a.replace("object ","");if(i(e))return"[Circular ~]";let c=e;if(c&&typeof c.toJSON=="function")try{let _=c.toJSON();return Oe("",_,u-1,r,s)}catch{}let d=Array.isArray(e)?[]:{},l=0,p=Te(e);for(let _ in p){if(!Object.prototype.hasOwnProperty.call(p,_))continue;if(l>=r){d[_]="[MaxProperties ~]";break}let U=p[_];d[_]=Oe(_,U,u-1,r,s),l++}return o(e),d}function gr(t,e){try{if(t==="domain"&&e&&typeof e=="object"&&e._events)return"[Domain]";if(t==="domainEmitter")return"[DomainEmitter]";if(typeof global<"u"&&e===global)return"[Global]";if(typeof window<"u"&&e===window)return"[Window]";if(typeof document<"u"&&e===document)return"[Document]";if(tt(e))return"[VueViewModel]";if(et(e))return"[SyntheticEvent]";if(typeof e=="number"&&e!==e)return"[NaN]";if(typeof e=="function")return`[Function: ${ie(e)}]`;if(typeof e=="symbol")return`[${String(e)}]`;if(typeof e=="bigint")return`[BigInt: ${String(e)}]`;let n=Er(e);return/^HTML(\w*)Element$/.test(n)?`[HTMLElement: ${n}]`:`[object ${n}]`}catch(n){return`**non-serializable** (${n})`}}function Er(t){let e=Object.getPrototypeOf(t);return e?e.constructor.name:"null prototype"}function yr(t){return~-encodeURI(t).split(/%..|./).length}function Sr(t){return yr(JSON.stringify(t))}var Tr=/^(\S+:\\|\/?)([\s\S]*?)((?:\.{1,2}|[^/\\]+?|)(\.[^./\\]*|))(?:[/\\]*)$/;function br(t){let e=t.length>1024?`<truncated>${t.slice(-1024)}`:t,n=Tr.exec(e);return n?n.slice(1):[]}function ut(t,e){let n=br(t)[2];return e&&n.slice(e.length*-1)===e&&(n=n.slice(0,n.length-e.length)),n}var v;(function(t){t[t.PENDING=0]="PENDING";let n=1;t[t.RESOLVED=n]="RESOLVED";let r=2;t[t.REJECTED=r]="REJECTED"})(v||(v={}));function b(t){return new S(e=>{e(t)})}function L(t){return new S((e,n)=>{n(t)})}var S=class{constructor(e){S.prototype.__init.call(this),S.prototype.__init2.call(this),S.prototype.__init3.call(this),S.prototype.__init4.call(this),this._state=v.PENDING,this._handlers=[];try{e(this._resolve,this._reject)}catch(n){this._reject(n)}}then(e,n){return new S((r,s)=>{this._handlers.push([!1,i=>{if(!e)r(i);else try{r(e(i))}catch(o){s(o)}},i=>{if(!n)s(i);else try{r(n(i))}catch(o){s(o)}}]),this._executeHandlers()})}catch(e){return this.then(n=>n,e)}finally(e){return new S((n,r)=>{let s,i;return this.then(o=>{i=!1,s=o,e&&e()},o=>{i=!0,s=o,e&&e()}).then(()=>{if(i){r(s);return}n(s)})})}__init(){this._resolve=e=>{this._setResult(v.RESOLVED,e)}}__init2(){this._reject=e=>{this._setResult(v.REJECTED,e)}}__init3(){this._setResult=(e,n)=>{if(this._state===v.PENDING){if($(n)){n.then(this._resolve,this._reject);return}this._state=e,this._value=n,this._executeHandlers()}}}__init4(){this._executeHandlers=()=>{if(this._state===v.PENDING)return;let e=this._handlers.slice();this._handlers=[],e.forEach(n=>{n[0]||(this._state===v.RESOLVED&&n[1](this._value),this._state===v.REJECTED&&n[2](this._value),n[0]=!0)})}}};function lt(t){let e=[];function n(){return t===void 0||e.length<t}function r(o){return e.splice(e.indexOf(o),1)[0]}function s(o){if(!n())return L(new T("Not adding Promise because buffer limit was reached."));let a=o();return e.indexOf(a)===-1&&e.push(a),a.then(()=>r(a)).then(null,()=>r(a).then(null,()=>{})),a}function i(o){return new S((a,u)=>{let c=e.length;if(!c)return a(!0);let d=setTimeout(()=>{o&&o>0&&a(!1)},o);e.forEach(l=>{b(l).then(()=>{--c||(clearTimeout(d),a(!0))},u)})})}return{$:e,add:s,drain:i}}var fn=N(),ft={nowSeconds:()=>Date.now()/1e3};function xr(){let{performance:t}=fn;if(!t||!t.now)return;let e=Date.now()-t.now();return{now:()=>t.now(),timeOrigin:e}}function Rr(){try{return un(module,"perf_hooks").performance}catch{return}}var dt=cn()?Rr():xr(),dn=dt===void 0?ft:{nowSeconds:()=>(dt.timeOrigin+dt.now())/1e3},G=ft.nowSeconds.bind(ft),F=dn.nowSeconds.bind(dn);var de,Ir=(()=>{let{performance:t}=fn;if(!t||!t.now){de="none";return}let e=3600*1e3,n=t.now(),r=Date.now(),s=t.timeOrigin?Math.abs(t.timeOrigin+n-r):e,i=s<e,o=t.timing&&t.timing.navigationStart,u=typeof o=="number"?Math.abs(o+n-r):e,c=u<e;return i||c?s<=u?(de="timeOrigin",t.timeOrigin):(de="navigationStart",o):(de="dateNow",r)})();var Nr=new RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");function pt(t=g(),e=g().substring(16),n){let r="";return n!==void 0&&(r=n?"-1":"-0"),`${t}-${e}${r}`}function M(t,e=[]){return[t,e]}function mt(t,e){let[n,r]=t;return[n,[...r,e]]}function ve(t,e){let n=t[1];for(let r of n){let s=r[0].type;if(e(r,s))return!0}return!1}function _t(t,e){return(e||new TextEncoder).encode(t)}function ht(t,e){let[n,r]=t,s=JSON.stringify(n);function i(o){typeof s=="string"?s=typeof o=="string"?s+o:[_t(s,e),o]:s.push(typeof o=="string"?_t(o,e):o)}for(let o of r){let[a,u]=o;if(i(`
${JSON.stringify(a)}
`),typeof u=="string"||u instanceof Uint8Array)i(u);else{let c;try{c=JSON.stringify(u)}catch{c=JSON.stringify(w(u))}i(c)}}return typeof s=="string"?s:Dr(s)}function Dr(t){let e=t.reduce((s,i)=>s+i.length,0),n=new Uint8Array(e),r=0;for(let s of t)n.set(s,r),r+=s.length;return n}function gt(t,e){let n=typeof t.data=="string"?_t(t.data,e):t.data;return[E({type:"attachment",length:n.length,filename:t.filename,content_type:t.contentType,attachment_type:t.attachmentType}),n]}var wr={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",statsd:"unknown"};function ke(t){return wr[t]}function Pe(t){if(!t||!t.sdk)return;let{name:e,version:n}=t.sdk;return{name:e,version:n}}function Et(t,e,n,r){let s=t.sdkProcessingMetadata&&t.sdkProcessingMetadata.dynamicSamplingContext;return{event_id:t.event_id,sent_at:new Date().toISOString(),...e&&{sdk:e},...!!n&&r&&{dsn:C(r)},...s&&{trace:E({...s})}}}function pn(t,e=Date.now()){let n=parseInt(`${t}`,10);if(!isNaN(n))return n*1e3;let r=Date.parse(`${t}`);return isNaN(r)?6e4:r-e}function _n(t,e){return t[e]||t.all||0}function yt(t,e,n=Date.now()){return _n(t,e)>n}function St(t,{statusCode:e,headers:n},r=Date.now()){let s={...t},i=n&&n["x-sentry-rate-limits"],o=n&&n["retry-after"];if(i)for(let a of i.trim().split(",")){let[u,c]=a.split(":",2),d=parseInt(u,10),l=(isNaN(d)?60:d)*1e3;if(!c)s.all=r+l;else for(let p of c.split(";"))s[p]=r+l}else o?s.all=r+pn(o,r):e===429&&(s.all=r+60*1e3);return s}function Tt(t,e){return t(e.stack||"",1)}function mn(t,e){let n={type:e.name||e.constructor.name,value:e.message},r=Tt(t,e);return r.length&&(n.stacktrace={frames:r}),n}function Ar(t){if("name"in t&&typeof t.name=="string"){let e=`'${t.name}' captured as exception`;return"message"in t&&typeof t.message=="string"&&(e+=` with message '${t.message}'`),e}else return"message"in t&&typeof t.message=="string"?t.message:`Object captured as exception with keys: ${se(t)}`}function bt(t,e,n,r){let s=n,o=r&&r.data&&r.data.mechanism||{handled:!0,type:"generic"};if(!B(n)){if(R(n)){let u=t(),c=u.getClient(),d=c&&c.getOptions().normalizeDepth;u.configureScope(p=>{p.setExtra("__serialized__",z(n,d))});let l=Ar(n);s=r&&r.syntheticException||new Error(l),s.message=l}else s=r&&r.syntheticException||new Error(n),s.message=n;o.synthetic=!0}let a={exception:{values:[mn(e,s)]}};return ce(a,void 0,void 0),ue(a,o),{...a,event_id:r&&r.event_id}}function xt(t,e,n="info",r,s){let i={event_id:r&&r.event_id,level:n,message:e};if(s&&r&&r.syntheticException){let o=Tt(t,r.syntheticException);o.length&&(i.exception={values:[{value:e,stacktrace:{frames:o}}]})}return i}var K="production";function fe(){return ne("globalEventProcessors",()=>[])}function hn(t){fe().push(t)}function V(t,e,n,r=0){return new S((s,i)=>{let o=t[r];if(e===null||typeof o!="function")s(e);else{let a=o({...e},n);(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&o.id&&a===null&&f.log(`Event processor "${o.id}" dropped event`),$(a)?a.then(u=>V(t,u,n,r+1).then(s)).then(null,i):V(t,a,n,r+1).then(s).then(null,i)}})}function gn(t){let e=F(),n={sid:g(),init:!0,timestamp:e,started:e,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>Or(n)};return t&&k(n,t),n}function k(t,e={}){if(e.user&&(!t.ipAddress&&e.user.ip_address&&(t.ipAddress=e.user.ip_address),!t.did&&!e.did&&(t.did=e.user.id||e.user.email||e.user.username)),t.timestamp=e.timestamp||F(),e.abnormal_mechanism&&(t.abnormal_mechanism=e.abnormal_mechanism),e.ignoreDuration&&(t.ignoreDuration=e.ignoreDuration),e.sid&&(t.sid=e.sid.length===32?e.sid:g()),e.init!==void 0&&(t.init=e.init),!t.did&&e.did&&(t.did=`${e.did}`),typeof e.started=="number"&&(t.started=e.started),t.ignoreDuration)t.duration=void 0;else if(typeof e.duration=="number")t.duration=e.duration;else{let n=t.timestamp-t.started;t.duration=n>=0?n:0}e.release&&(t.release=e.release),e.environment&&(t.environment=e.environment),!t.ipAddress&&e.ipAddress&&(t.ipAddress=e.ipAddress),!t.userAgent&&e.userAgent&&(t.userAgent=e.userAgent),typeof e.errors=="number"&&(t.errors=e.errors),e.status&&(t.status=e.status)}function En(t,e){let n={};e?n={status:e}:t.status==="ok"&&(n={status:"exited"}),k(t,n)}function Or(t){return E({sid:`${t.sid}`,init:t.init,started:new Date(t.started*1e3).toISOString(),timestamp:new Date(t.timestamp*1e3).toISOString(),status:t.status,errors:t.errors,did:typeof t.did=="number"||typeof t.did=="string"?`${t.did}`:void 0,duration:t.duration,abnormal_mechanism:t.abnormal_mechanism,attrs:{release:t.release,environment:t.environment,ip_address:t.ipAddress,user_agent:t.userAgent}})}var vr=100,A=class{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext=yn()}static clone(e){let n=new A;return e&&(n._breadcrumbs=[...e._breadcrumbs],n._tags={...e._tags},n._extra={...e._extra},n._contexts={...e._contexts},n._user=e._user,n._level=e._level,n._span=e._span,n._session=e._session,n._transactionName=e._transactionName,n._fingerprint=e._fingerprint,n._eventProcessors=[...e._eventProcessors],n._requestSession=e._requestSession,n._attachments=[...e._attachments],n._sdkProcessingMetadata={...e._sdkProcessingMetadata},n._propagationContext={...e._propagationContext}),n}addScopeListener(e){this._scopeListeners.push(e)}addEventProcessor(e){return this._eventProcessors.push(e),this}setUser(e){return this._user=e||{},this._session&&k(this._session,{user:e}),this._notifyScopeListeners(),this}getUser(){return this._user}getRequestSession(){return this._requestSession}setRequestSession(e){return this._requestSession=e,this}setTags(e){return this._tags={...this._tags,...e},this._notifyScopeListeners(),this}setTag(e,n){return this._tags={...this._tags,[e]:n},this._notifyScopeListeners(),this}setExtras(e){return this._extra={...this._extra,...e},this._notifyScopeListeners(),this}setExtra(e,n){return this._extra={...this._extra,[e]:n},this._notifyScopeListeners(),this}setFingerprint(e){return this._fingerprint=e,this._notifyScopeListeners(),this}setLevel(e){return this._level=e,this._notifyScopeListeners(),this}setTransactionName(e){return this._transactionName=e,this._notifyScopeListeners(),this}setContext(e,n){return n===null?delete this._contexts[e]:this._contexts[e]=n,this._notifyScopeListeners(),this}setSpan(e){return this._span=e,this._notifyScopeListeners(),this}getSpan(){return this._span}getTransaction(){let e=this.getSpan();return e&&e.transaction}setSession(e){return e?this._session=e:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(e){if(!e)return this;if(typeof e=="function"){let n=e(this);return n instanceof A?n:this}return e instanceof A?(this._tags={...this._tags,...e._tags},this._extra={...this._extra,...e._extra},this._contexts={...this._contexts,...e._contexts},e._user&&Object.keys(e._user).length&&(this._user=e._user),e._level&&(this._level=e._level),e._fingerprint&&(this._fingerprint=e._fingerprint),e._requestSession&&(this._requestSession=e._requestSession),e._propagationContext&&(this._propagationContext=e._propagationContext)):R(e)&&(e=e,this._tags={...this._tags,...e.tags},this._extra={...this._extra,...e.extra},this._contexts={...this._contexts,...e.contexts},e.user&&(this._user=e.user),e.level&&(this._level=e.level),e.fingerprint&&(this._fingerprint=e.fingerprint),e.requestSession&&(this._requestSession=e.requestSession),e.propagationContext&&(this._propagationContext=e.propagationContext)),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._requestSession=void 0,this._span=void 0,this._session=void 0,this._notifyScopeListeners(),this._attachments=[],this._propagationContext=yn(),this}addBreadcrumb(e,n){let r=typeof n=="number"?n:vr;if(r<=0)return this;let s={timestamp:G(),...e},i=this._breadcrumbs;return i.push(s),this._breadcrumbs=i.length>r?i.slice(-r):i,this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(e){return this._attachments.push(e),this}getAttachments(){return this._attachments}clearAttachments(){return this._attachments=[],this}applyToEvent(e,n={},r){if(this._extra&&Object.keys(this._extra).length&&(e.extra={...this._extra,...e.extra}),this._tags&&Object.keys(this._tags).length&&(e.tags={...this._tags,...e.tags}),this._user&&Object.keys(this._user).length&&(e.user={...this._user,...e.user}),this._contexts&&Object.keys(this._contexts).length&&(e.contexts={...this._contexts,...e.contexts}),this._level&&(e.level=this._level),this._transactionName&&(e.transaction=this._transactionName),this._span){e.contexts={trace:this._span.getTraceContext(),...e.contexts};let o=this._span.transaction;if(o){e.sdkProcessingMetadata={dynamicSamplingContext:o.getDynamicSamplingContext(),...e.sdkProcessingMetadata};let a=o.name;a&&(e.tags={transaction:a,...e.tags})}}this._applyFingerprint(e);let s=this._getBreadcrumbs(),i=[...e.breadcrumbs||[],...s];return e.breadcrumbs=i.length>0?i:void 0,e.sdkProcessingMetadata={...e.sdkProcessingMetadata,...this._sdkProcessingMetadata,propagationContext:this._propagationContext},V([...r||[],...fe(),...this._eventProcessors],e,n)}setSDKProcessingMetadata(e){return this._sdkProcessingMetadata={...this._sdkProcessingMetadata,...e},this}setPropagationContext(e){return this._propagationContext=e,this}getPropagationContext(){return this._propagationContext}_getBreadcrumbs(){return this._breadcrumbs}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(e=>{e(this)}),this._notifyingListeners=!1)}_applyFingerprint(e){e.fingerprint=e.fingerprint?le(e.fingerprint):[],this._fingerprint&&(e.fingerprint=e.fingerprint.concat(this._fingerprint)),e.fingerprint&&!e.fingerprint.length&&delete e.fingerprint}};function yn(){return{traceId:g(),spanId:g().substring(16)}}var Sn=4,kr=100,Y=class{constructor(e,n=new A,r=Sn){this._version=r,this._stack=[{scope:n}],e&&this.bindClient(e)}isOlderThan(e){return this._version<e}bindClient(e){let n=this.getStackTop();n.client=e,e&&e.setupIntegrations&&e.setupIntegrations()}pushScope(){let e=A.clone(this.getScope());return this.getStack().push({client:this.getClient(),scope:e}),e}popScope(){return this.getStack().length<=1?!1:!!this.getStack().pop()}withScope(e){let n=this.pushScope();try{e(n)}finally{this.popScope()}}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getStack(){return this._stack}getStackTop(){return this._stack[this._stack.length-1]}captureException(e,n){let r=this._lastEventId=n&&n.event_id?n.event_id:g(),s=new Error("Sentry syntheticException");return this._withClient((i,o)=>{i.captureException(e,{originalException:e,syntheticException:s,...n,event_id:r},o)}),r}captureMessage(e,n,r){let s=this._lastEventId=r&&r.event_id?r.event_id:g(),i=new Error(e);return this._withClient((o,a)=>{o.captureMessage(e,n,{originalException:e,syntheticException:i,...r,event_id:s},a)}),s}captureEvent(e,n){let r=n&&n.event_id?n.event_id:g();return e.type||(this._lastEventId=r),this._withClient((s,i)=>{s.captureEvent(e,{...n,event_id:r},i)}),r}lastEventId(){return this._lastEventId}addBreadcrumb(e,n){let{scope:r,client:s}=this.getStackTop();if(!s)return;let{beforeBreadcrumb:i=null,maxBreadcrumbs:o=kr}=s.getOptions&&s.getOptions()||{};if(o<=0)return;let u={timestamp:G(),...e},c=i?ye(()=>i(u,n)):u;c!==null&&(s.emit&&s.emit("beforeAddBreadcrumb",c,n),r.addBreadcrumb(c,o))}setUser(e){this.getScope().setUser(e)}setTags(e){this.getScope().setTags(e)}setExtras(e){this.getScope().setExtras(e)}setTag(e,n){this.getScope().setTag(e,n)}setExtra(e,n){this.getScope().setExtra(e,n)}setContext(e,n){this.getScope().setContext(e,n)}configureScope(e){let{scope:n,client:r}=this.getStackTop();r&&e(n)}run(e){let n=Rt(this);try{e(this)}finally{Rt(n)}}getIntegration(e){let n=this.getClient();if(!n)return null;try{return n.getIntegration(e)}catch{return(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&f.warn(`Cannot retrieve integration ${e.id} from the current Hub`),null}}startTransaction(e,n){let r=this._callExtensionMethod("startTransaction",e,n);if((typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&!r){let s=this.getClient();console.warn(s?`Tracing extension 'startTransaction' has not been added. Call 'addTracingExtensions' before calling 'init':
Sentry.addTracingExtensions();
Sentry.init({...});
`:"Tracing extension 'startTransaction' is missing. You should 'init' the SDK before calling 'startTransaction'")}return r}traceHeaders(){return this._callExtensionMethod("traceHeaders")}captureSession(e=!1){if(e)return this.endSession();this._sendSessionUpdate()}endSession(){let n=this.getStackTop().scope,r=n.getSession();r&&En(r),this._sendSessionUpdate(),n.setSession()}startSession(e){let{scope:n,client:r}=this.getStackTop(),{release:s,environment:i=K}=r&&r.getOptions()||{},{userAgent:o}=m.navigator||{},a=gn({release:s,environment:i,user:n.getUser(),...o&&{userAgent:o},...e}),u=n.getSession&&n.getSession();return u&&u.status==="ok"&&k(u,{status:"exited"}),this.endSession(),n.setSession(a),a}shouldSendDefaultPii(){let e=this.getClient(),n=e&&e.getOptions();return!!(n&&n.sendDefaultPii)}_sendSessionUpdate(){let{scope:e,client:n}=this.getStackTop(),r=e.getSession();r&&n&&n.captureSession&&n.captureSession(r)}_withClient(e){let{scope:n,client:r}=this.getStackTop();r&&e(r,n)}_callExtensionMethod(e,...n){let s=H().__SENTRY__;if(s&&s.extensions&&typeof s.extensions[e]=="function")return s.extensions[e].apply(this,n);(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&f.warn(`Extension method ${e} couldn't be found, doing nothing.`)}};function H(){return m.__SENTRY__=m.__SENTRY__||{extensions:{},hub:void 0},m}function Rt(t){let e=H(),n=Ce(e);return It(e,t),n}function x(){let t=H();if(t.__SENTRY__&&t.__SENTRY__.acs){let e=t.__SENTRY__.acs.getCurrentHub();if(e)return e}return Pr(t)}function Pr(t=H()){return(!Cr(t)||Ce(t).isOlderThan(Sn))&&It(t,new Y),Ce(t)}function Cr(t){return!!(t&&t.__SENTRY__&&t.__SENTRY__.hub)}function Ce(t){return ne("hub",()=>new Y,t)}function It(t,e){if(!t)return!1;let n=t.__SENTRY__=t.__SENTRY__||{};return n.hub=e,!0}function Tn(t){return(t||x()).getScope().getTransaction()}var bn=!1;function xn(){bn||(bn=!0,we("error",Nt),we("unhandledrejection",Nt))}function Nt(){let t=Tn();if(t){let e="internal_error";(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&f.log(`[Tracing] Transaction: ${e} -> Global error occured`),t.setStatus(e)}}Nt.tag="sentry_tracingErrorCallback";var Me=class{constructor(e=1e3){this._maxlen=e,this.spans=[]}add(e){this.spans.length>this._maxlen?e.spanRecorder=void 0:this.spans.push(e)}},J=class{constructor(e={}){this.traceId=e.traceId||g(),this.spanId=e.spanId||g().substring(16),this.startTimestamp=e.startTimestamp||F(),this.tags=e.tags||{},this.data=e.data||{},this.instrumenter=e.instrumenter||"sentry",this.origin=e.origin||"manual",e.parentSpanId&&(this.parentSpanId=e.parentSpanId),"sampled"in e&&(this.sampled=e.sampled),e.op&&(this.op=e.op),e.description&&(this.description=e.description),e.name&&(this.description=e.name),e.status&&(this.status=e.status),e.endTimestamp&&(this.endTimestamp=e.endTimestamp)}get name(){return this.description||""}set name(e){this.setName(e)}startChild(e){let n=new J({...e,parentSpanId:this.spanId,sampled:this.sampled,traceId:this.traceId});if(n.spanRecorder=this.spanRecorder,n.spanRecorder&&n.spanRecorder.add(n),n.transaction=this.transaction,(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&n.transaction){let r=e&&e.op||"< unknown op >",s=n.transaction.name||"< unknown name >",i=n.transaction.spanId,o=`[Tracing] Starting '${r}' span on transaction '${s}' (${i}).`;n.transaction.metadata.spanMetadata[n.spanId]={logMessage:o},f.log(o)}return n}setTag(e,n){return this.tags={...this.tags,[e]:n},this}setData(e,n){return this.data={...this.data,[e]:n},this}setStatus(e){return this.status=e,this}setHttpStatus(e){this.setTag("http.status_code",String(e)),this.setData("http.response.status_code",e);let n=Mr(e);return n!=="unknown_error"&&this.setStatus(n),this}setName(e){this.description=e}isSuccess(){return this.status==="ok"}finish(e){if((typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&this.transaction&&this.transaction.spanId!==this.spanId){let{logMessage:n}=this.transaction.metadata.spanMetadata[this.spanId];n&&f.log(n.replace("Starting","Finishing"))}this.endTimestamp=typeof e=="number"?e:F()}toTraceparent(){return pt(this.traceId,this.spanId,this.sampled)}toContext(){return E({data:this.data,description:this.description,endTimestamp:this.endTimestamp,op:this.op,parentSpanId:this.parentSpanId,sampled:this.sampled,spanId:this.spanId,startTimestamp:this.startTimestamp,status:this.status,tags:this.tags,traceId:this.traceId})}updateWithContext(e){return this.data=e.data||{},this.description=e.description,this.endTimestamp=e.endTimestamp,this.op=e.op,this.parentSpanId=e.parentSpanId,this.sampled=e.sampled,this.spanId=e.spanId||this.spanId,this.startTimestamp=e.startTimestamp||this.startTimestamp,this.status=e.status,this.tags=e.tags||{},this.traceId=e.traceId||this.traceId,this}getTraceContext(){return E({data:Object.keys(this.data).length>0?this.data:void 0,description:this.description,op:this.op,parent_span_id:this.parentSpanId,span_id:this.spanId,status:this.status,tags:Object.keys(this.tags).length>0?this.tags:void 0,trace_id:this.traceId})}toJSON(){return E({data:Object.keys(this.data).length>0?this.data:void 0,description:this.description,op:this.op,parent_span_id:this.parentSpanId,span_id:this.spanId,start_timestamp:this.startTimestamp,status:this.status,tags:Object.keys(this.tags).length>0?this.tags:void 0,timestamp:this.endTimestamp,trace_id:this.traceId,origin:this.origin})}};function Mr(t){if(t<400&&t>=100)return"ok";if(t>=400&&t<500)switch(t){case 401:return"unauthenticated";case 403:return"permission_denied";case 404:return"not_found";case 409:return"already_exists";case 413:return"failed_precondition";case 429:return"resource_exhausted";default:return"invalid_argument"}if(t>=500&&t<600)switch(t){case 501:return"unimplemented";case 503:return"unavailable";case 504:return"deadline_exceeded";default:return"internal_error"}return"unknown_error"}function X(t,e,n){let r=e.getOptions(),{publicKey:s}=e.getDsn()||{},{segment:i}=n&&n.getUser()||{},o=E({environment:r.environment||K,release:r.release,user_segment:i,public_key:s,trace_id:t});return e.emit&&e.emit("createDsc",o),o}var Ue=class extends J{constructor(e,n){super(e),delete this.description,this._measurements={},this._contexts={},this._hub=n||x(),this._name=e.name||"",this.metadata={source:"custom",...e.metadata,spanMetadata:{}},this._trimEnd=e.trimEnd,this.transaction=this;let r=this.metadata.dynamicSamplingContext;r&&(this._frozenDynamicSamplingContext={...r})}get name(){return this._name}set name(e){this.setName(e)}setName(e,n="custom"){this._name=e,this.metadata.source=n}initSpanRecorder(e=1e3){this.spanRecorder||(this.spanRecorder=new Me(e)),this.spanRecorder.add(this)}setContext(e,n){n===null?delete this._contexts[e]:this._contexts[e]=n}setMeasurement(e,n,r=""){this._measurements[e]={value:n,unit:r}}setMetadata(e){this.metadata={...this.metadata,...e}}finish(e){let n=this._finishTransaction(e);if(n)return this._hub.captureEvent(n)}toContext(){let e=super.toContext();return E({...e,name:this.name,trimEnd:this._trimEnd})}updateWithContext(e){return super.updateWithContext(e),this.name=e.name||"",this._trimEnd=e.trimEnd,this}getDynamicSamplingContext(){if(this._frozenDynamicSamplingContext)return this._frozenDynamicSamplingContext;let e=this._hub||x(),n=e.getClient();if(!n)return{};let r=e.getScope(),s=X(this.traceId,n,r),i=this.metadata.sampleRate;i!==void 0&&(s.sample_rate=`${i}`);let o=this.metadata.source;return o&&o!=="url"&&(s.transaction=this.name),this.sampled!==void 0&&(s.sampled=String(this.sampled)),s}setHub(e){this._hub=e}_finishTransaction(e){if(this.endTimestamp!==void 0)return;this.name||((typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&f.warn("Transaction has no name, falling back to `<unlabeled transaction>`."),this.name="<unlabeled transaction>"),super.finish(e);let n=this._hub.getClient();if(n&&n.emit&&n.emit("finishTransaction",this),this.sampled!==!0){(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&f.log("[Tracing] Discarding transaction because its trace was not chosen to be sampled."),n&&n.recordDroppedEvent("sample_rate","transaction");return}let r=this.spanRecorder?this.spanRecorder.spans.filter(a=>a!==this&&a.endTimestamp):[];this._trimEnd&&r.length>0&&(this.endTimestamp=r.reduce((a,u)=>a.endTimestamp&&u.endTimestamp?a.endTimestamp>u.endTimestamp?a:u:a).endTimestamp);let s=this.metadata,i={contexts:{...this._contexts,trace:this.getTraceContext()},spans:r,start_timestamp:this.startTimestamp,tags:this.tags,timestamp:this.endTimestamp,transaction:this.name,type:"transaction",sdkProcessingMetadata:{...s,dynamicSamplingContext:this.getDynamicSamplingContext()},...s.source&&{transaction_info:{source:s.source}}};return Object.keys(this._measurements).length>0&&((typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&f.log("[Measurements] Adding measurements to transaction",JSON.stringify(this._measurements,void 0,2)),i.measurements=this._measurements),(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&f.log(`[Tracing] Finishing ${this.op} transaction: ${this.name}.`),i}};function Rn(t){if(typeof __SENTRY_TRACING__=="boolean"&&!__SENTRY_TRACING__)return!1;let e=x().getClient(),n=t||e&&e.getOptions();return!!n&&(n.enableTracing||"tracesSampleRate"in n||"tracesSampler"in n)}function In(t,e,n){if(!Rn(e))return t.sampled=!1,t;if(t.sampled!==void 0)return t.setMetadata({sampleRate:Number(t.sampled)}),t;let r;return typeof e.tracesSampler=="function"?(r=e.tracesSampler(n),t.setMetadata({sampleRate:Number(r)})):n.parentSampled!==void 0?r=n.parentSampled:typeof e.tracesSampleRate<"u"?(r=e.tracesSampleRate,t.setMetadata({sampleRate:Number(r)})):(r=1,t.setMetadata({sampleRate:r})),Ur(r)?r?(t.sampled=Math.random()<r,t.sampled?((typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&f.log(`[Tracing] starting ${t.op} transaction - ${t.name}`),t):((typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&f.log(`[Tracing] Discarding transaction because it's not included in the random sample (sampling rate = ${Number(r)})`),t)):((typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&f.log(`[Tracing] Discarding transaction because ${typeof e.tracesSampler=="function"?"tracesSampler returned 0 or false":"a negative sampling decision was inherited or tracesSampleRate is set to 0"}`),t.sampled=!1,t):((typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&f.warn("[Tracing] Discarding transaction because of invalid sample rate."),t.sampled=!1,t)}function Ur(t){return te(t)||!(typeof t=="number"||typeof t=="boolean")?((typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&f.warn(`[Tracing] Given sample rate is invalid. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(t)} of type ${JSON.stringify(typeof t)}.`),!1):t<0||t>1?((typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&f.warn(`[Tracing] Given sample rate is invalid. Sample rate must be between 0 and 1. Got ${t}.`),!1):!0}function Br(){let e=this.getScope().getSpan();return e?{"sentry-trace":e.toTraceparent()}:{}}function $r(t,e){let n=this.getClient(),r=n&&n.getOptions()||{},s=r.instrumenter||"sentry",i=t.instrumenter||"sentry";s!==i&&((typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&f.error(`A transaction was started with instrumenter=\`${i}\`, but the SDK is configured with the \`${s}\` instrumenter.
The transaction will not be sampled. Please use the ${s} instrumentation to start transactions.`),t.sampled=!1);let o=new Ue(t,this);return o=In(o,r,{parentSampled:t.parentSampled,transactionContext:t,...e}),o.sampled&&o.initSpanRecorder(r._experiments&&r._experiments.maxSpans),n&&n.emit&&n.emit("startTransaction",o),o}function Nn(){let t=H();t.__SENTRY__&&(t.__SENTRY__.extensions=t.__SENTRY__.extensions||{},t.__SENTRY__.extensions.startTransaction||(t.__SENTRY__.extensions.startTransaction=$r),t.__SENTRY__.extensions.traceHeaders||(t.__SENTRY__.extensions.traceHeaders=Br),xn())}var Be=class{constructor(e,n){this._client=e,this.flushTimeout=60,this._pendingAggregates={},this._isEnabled=!0,this._intervalId=setInterval(()=>this.flush(),this.flushTimeout*1e3),this._sessionAttrs=n}flush(){let e=this.getSessionAggregates();e.aggregates.length!==0&&(this._pendingAggregates={},this._client.sendSession(e))}getSessionAggregates(){let e=Object.keys(this._pendingAggregates).map(r=>this._pendingAggregates[parseInt(r)]),n={attrs:this._sessionAttrs,aggregates:e};return E(n)}close(){clearInterval(this._intervalId),this._isEnabled=!1,this.flush()}incrementSessionStatusCount(){if(!this._isEnabled)return;let e=x().getScope(),n=e.getRequestSession();n&&n.status&&(this._incrementSessionStatusCount(n.status,new Date),e.setRequestSession(void 0))}_incrementSessionStatusCount(e,n){let r=new Date(n).setSeconds(0,0);this._pendingAggregates[r]=this._pendingAggregates[r]||{};let s=this._pendingAggregates[r];switch(s.started||(s.started=new Date(r).toISOString()),e){case"errored":return s.errored=(s.errored||0)+1,s.errored;case"ok":return s.exited=(s.exited||0)+1,s.exited;default:return s.crashed=(s.crashed||0)+1,s.crashed}}};var Lr="7";function Gr(t){let e=t.protocol?`${t.protocol}:`:"",n=t.port?`:${t.port}`:"";return`${e}//${t.host}${n}${t.path?`/${t.path}`:""}/api/`}function Fr(t){return`${Gr(t)}${t.projectId}/envelope/`}function Yr(t,e){return rt({sentry_key:t.publicKey,sentry_version:Lr,...e&&{sentry_client:`${e.name}/${e.version}`}})}function Dn(t,e={}){let n=typeof e=="string"?e:e.tunnel,r=typeof e=="string"||!e._metadata?void 0:e._metadata.sdk;return n||`${Fr(t)}?${Yr(t,r)}`}function Hr(t,e){return e&&(t.sdk=t.sdk||{},t.sdk.name=t.sdk.name||e.name,t.sdk.version=t.sdk.version||e.version,t.sdk.integrations=[...t.sdk.integrations||[],...e.integrations||[]],t.sdk.packages=[...t.sdk.packages||[],...e.packages||[]]),t}function wn(t,e,n,r){let s=Pe(n),i={sent_at:new Date().toISOString(),...s&&{sdk:s},...!!r&&e&&{dsn:C(e)}},o="aggregates"in t?[{type:"sessions"},t]:[{type:"session"},t.toJSON()];return M(i,[o])}function An(t,e,n,r){let s=Pe(n),i=t.type&&t.type!=="replay_event"?t.type:"event";Hr(t,n&&n.sdk);let o=Et(t,s,r,e);return delete t.sdkProcessingMetadata,M(o,[[{type:i},t]])}var On=[];function jr(t){let e={};return t.forEach(n=>{let{name:r}=n,s=e[r];s&&!s.isDefaultInstance&&n.isDefaultInstance||(e[r]=n)}),Object.keys(e).map(n=>e[n])}function Dt(t){let e=t.defaultIntegrations||[],n=t.integrations;e.forEach(o=>{o.isDefaultInstance=!0});let r;Array.isArray(n)?r=[...e,...n]:typeof n=="function"?r=le(n(e)):r=e;let s=jr(r),i=qr(s,o=>o.name==="Debug");if(i!==-1){let[o]=s.splice(i,1);s.push(o)}return s}function vn(t,e){let n={};return e.forEach(r=>{r&&wt(t,r,n)}),n}function wt(t,e,n){if(n[e.name]=e,On.indexOf(e.name)===-1&&(e.setupOnce(hn,x),On.push(e.name)),t.on&&typeof e.preprocessEvent=="function"){let r=e.preprocessEvent.bind(e);t.on("preprocessEvent",(s,i)=>r(s,i,t))}if(t.addEventProcessor&&typeof e.processEvent=="function"){let r=e.processEvent.bind(e),s=Object.assign((i,o)=>r(i,o,t),{id:e.name});t.addEventProcessor(s)}(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&f.log(`Integration installed: ${e.name}`)}function qr(t,e){for(let n=0;n<t.length;n++)if(e(t[n])===!0)return n;return-1}function Pn(t,e,n,r,s){let{normalizeDepth:i=3,normalizeMaxBreadth:o=1e3}=t,a={...e,event_id:e.event_id||n.event_id||g(),timestamp:e.timestamp||G()},u=n.integrations||t.integrations.map(p=>p.name);Wr(a,t),Vr(a,u),e.type===void 0&&zr(a,t.stackParser);let c=r;n.captureContext&&(c=A.clone(c).update(n.captureContext));let d=b(a),l=s&&s.getEventProcessors?s.getEventProcessors():[];if(c){if(c.getAttachments){let p=[...n.attachments||[],...c.getAttachments()];p.length&&(n.attachments=p)}d=c.applyToEvent(a,n,l)}else d=V([...l,...fe()],a,n);return d.then(p=>(p&&Kr(p),typeof i=="number"&&i>0?Jr(p,i,o):p))}function Wr(t,e){let{environment:n,release:r,dist:s,maxValueLength:i=250}=e;"environment"in t||(t.environment="environment"in e?n:K),t.release===void 0&&r!==void 0&&(t.release=r),t.dist===void 0&&s!==void 0&&(t.dist=s),t.message&&(t.message=P(t.message,i));let o=t.exception&&t.exception.values&&t.exception.values[0];o&&o.value&&(o.value=P(o.value,i));let a=t.request;a&&a.url&&(a.url=P(a.url,i))}var kn=new WeakMap;function zr(t,e){let n=m._sentryDebugIds;if(!n)return;let r,s=kn.get(e);s?r=s:(r=new Map,kn.set(e,r));let i=Object.keys(n).reduce((o,a)=>{let u,c=r.get(a);c?u=c:(u=e(a),r.set(a,u));for(let d=u.length-1;d>=0;d--){let l=u[d];if(l.filename){o[l.filename]=n[a];break}}return o},{});try{t.exception.values.forEach(o=>{o.stacktrace.frames.forEach(a=>{a.filename&&(a.debug_id=i[a.filename])})})}catch{}}function Kr(t){let e={};try{t.exception.values.forEach(r=>{r.stacktrace.frames.forEach(s=>{s.debug_id&&(s.abs_path?e[s.abs_path]=s.debug_id:s.filename&&(e[s.filename]=s.debug_id),delete s.debug_id)})})}catch{}if(Object.keys(e).length===0)return;t.debug_meta=t.debug_meta||{},t.debug_meta.images=t.debug_meta.images||[];let n=t.debug_meta.images;Object.keys(e).forEach(r=>{n.push({type:"sourcemap",code_file:r,debug_id:e[r]})})}function Vr(t,e){e.length>0&&(t.sdk=t.sdk||{},t.sdk.integrations=[...t.sdk.integrations||[],...e])}function Jr(t,e,n){if(!t)return null;let r={...t,...t.breadcrumbs&&{breadcrumbs:t.breadcrumbs.map(s=>({...s,...s.data&&{data:w(s.data,e,n)}}))},...t.user&&{user:w(t.user,e,n)},...t.contexts&&{contexts:w(t.contexts,e,n)},...t.extra&&{extra:w(t.extra,e,n)}};return t.contexts&&t.contexts.trace&&r.contexts&&(r.contexts.trace=t.contexts.trace,t.contexts.trace.data&&(r.contexts.trace.data=w(t.contexts.trace.data,e,n))),t.spans&&(r.spans=t.spans.map(s=>(s.data&&(s.data=w(s.data,e,n)),s))),r}var Cn="Not capturing exception because it's already been captured.",$e=class{constructor(e){if(this._options=e,this._integrations={},this._integrationsInitialized=!1,this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],e.dsn?this._dsn=Se(e.dsn):(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&f.warn("No DSN provided, client will not send events."),this._dsn){let n=Dn(this._dsn,e);this._transport=e.transport({recordDroppedEvent:this.recordDroppedEvent.bind(this),...e.transportOptions,url:n})}}captureException(e,n,r){if(Ae(e)){(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&f.log(Cn);return}let s=n&&n.event_id;return this._process(this.eventFromException(e,n).then(i=>this._captureEvent(i,n,r)).then(i=>{s=i})),s}captureMessage(e,n,r,s){let i=r&&r.event_id,o=ge(e)?this.eventFromMessage(String(e),n,r):this.eventFromException(e,r);return this._process(o.then(a=>this._captureEvent(a,r,s)).then(a=>{i=a})),i}captureEvent(e,n,r){if(n&&n.originalException&&Ae(n.originalException)){(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&f.log(Cn);return}let s=n&&n.event_id;return this._process(this._captureEvent(e,n,r).then(i=>{s=i})),s}captureSession(e){typeof e.release!="string"?(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&f.warn("Discarded session because of missing or non-string release"):(this.sendSession(e),k(e,{init:!1}))}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(e){let n=this._transport;return n?this._isClientDoneProcessing(e).then(r=>n.flush(e).then(s=>r&&s)):b(!0)}close(e){return this.flush(e).then(n=>(this.getOptions().enabled=!1,n))}getEventProcessors(){return this._eventProcessors}addEventProcessor(e){this._eventProcessors.push(e)}setupIntegrations(e){(e&&!this._integrationsInitialized||this._isEnabled()&&!this._integrationsInitialized)&&(this._integrations=vn(this,this._options.integrations),this._integrationsInitialized=!0)}getIntegrationById(e){return this._integrations[e]}getIntegration(e){try{return this._integrations[e.id]||null}catch{return(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&f.warn(`Cannot retrieve integration ${e.id} from the current Client`),null}}addIntegration(e){wt(this,e,this._integrations)}sendEvent(e,n={}){this.emit("beforeSendEvent",e,n);let r=An(e,this._dsn,this._options._metadata,this._options.tunnel);for(let i of n.attachments||[])r=mt(r,gt(i,this._options.transportOptions&&this._options.transportOptions.textEncoder));let s=this._sendEnvelope(r);s&&s.then(i=>this.emit("afterSendEvent",e,i),null)}sendSession(e){let n=wn(e,this._dsn,this._options._metadata,this._options.tunnel);this._sendEnvelope(n)}recordDroppedEvent(e,n,r){if(this._options.sendClientReports){let s=`${e}:${n}`;(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&f.log(`Adding outcome: "${s}"`),this._outcomes[s]=this._outcomes[s]+1||1}}on(e,n){this._hooks[e]||(this._hooks[e]=[]),this._hooks[e].push(n)}emit(e,...n){this._hooks[e]&&this._hooks[e].forEach(r=>r(...n))}_updateSessionFromEvent(e,n){let r=!1,s=!1,i=n.exception&&n.exception.values;if(i){s=!0;for(let u of i){let c=u.mechanism;if(c&&c.handled===!1){r=!0;break}}}let o=e.status==="ok";(o&&e.errors===0||o&&r)&&(k(e,{...r&&{status:"crashed"},errors:e.errors||Number(s||r)}),this.captureSession(e))}_isClientDoneProcessing(e){return new S(n=>{let r=0,s=1,i=setInterval(()=>{this._numProcessing==0?(clearInterval(i),n(!0)):(r+=s,e&&r>=e&&(clearInterval(i),n(!1)))},s)})}_isEnabled(){return this.getOptions().enabled!==!1&&this._transport!==void 0}_prepareEvent(e,n,r){let s=this.getOptions(),i=Object.keys(this._integrations);return!n.integrations&&i.length>0&&(n.integrations=i),this.emit("preprocessEvent",e,n),Pn(s,e,n,r,this).then(o=>{if(o===null)return o;let{propagationContext:a}=o.sdkProcessingMetadata||{};if(!(o.contexts&&o.contexts.trace)&&a){let{traceId:c,spanId:d,parentSpanId:l,dsc:p}=a;o.contexts={trace:{trace_id:c,span_id:d,parent_span_id:l},...o.contexts};let _=p||X(c,this,r);o.sdkProcessingMetadata={dynamicSamplingContext:_,...o.sdkProcessingMetadata}}return o})}_captureEvent(e,n={},r){return this._processEvent(e,n,r).then(s=>s.event_id,s=>{if(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__){let i=s;i.logLevel==="log"?f.log(i.message):f.warn(i)}})}_processEvent(e,n,r){let s=this.getOptions(),{sampleRate:i}=s,o=Un(e),a=Mn(e),u=e.type||"error",c=`before send for type \`${u}\``;if(a&&typeof i=="number"&&Math.random()>i)return this.recordDroppedEvent("sample_rate","error",e),L(new T(`Discarding event because it's not included in the random sample (sampling rate = ${i})`,"log"));let d=u==="replay_event"?"replay":u;return this._prepareEvent(e,n,r).then(l=>{if(l===null)throw this.recordDroppedEvent("event_processor",d,e),new T("An event processor returned `null`, will not send event.","log");if(n.data&&n.data.__sentry__===!0)return l;let _=Zr(s,l,n);return Xr(_,c)}).then(l=>{if(l===null)throw this.recordDroppedEvent("before_send",d,e),new T(`${c} returned \`null\`, will not send event.`,"log");let p=r&&r.getSession();!o&&p&&this._updateSessionFromEvent(p,l);let _=l.transaction_info;if(o&&_&&l.transaction!==e.transaction){let U="custom";l.transaction_info={..._,source:U}}return this.sendEvent(l,n),l}).then(null,l=>{throw l instanceof T?l:(this.captureException(l,{data:{__sentry__:!0},originalException:l}),new T(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${l}`))})}_process(e){this._numProcessing++,e.then(n=>(this._numProcessing--,n),n=>(this._numProcessing--,n))}_sendEnvelope(e){if(this.emit("beforeEnvelope",e),this._isEnabled()&&this._transport)return this._transport.send(e).then(null,n=>{(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&f.error("Error while sending event:",n)});(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&f.error("Transport disabled")}_clearOutcomes(){let e=this._outcomes;return this._outcomes={},Object.keys(e).map(n=>{let[r,s]=n.split(":");return{reason:r,category:s,quantity:e[n]}})}};function Xr(t,e){let n=`${e} must return \`null\` or a valid event.`;if($(t))return t.then(r=>{if(!R(r)&&r!==null)throw new T(n);return r},r=>{throw new T(`${e} rejected with ${r}`)});if(!R(t)&&t!==null)throw new T(n);return t}function Zr(t,e,n){let{beforeSend:r,beforeSendTransaction:s}=t;return Mn(e)&&r?r(e,n):Un(e)&&s?s(e,n):e}function Mn(t){return t.type===void 0}function Un(t){return t.type==="transaction"}function Bn(t,e,n,r,s){let i={sent_at:new Date().toISOString()};n&&n.sdk&&(i.sdk={name:n.sdk.name,version:n.sdk.version}),r&&s&&(i.dsn=C(s)),e&&(i.trace=E(e));let o=Qr(t);return M(i,[o])}function Qr(t){return[{type:"check_in"},t]}var pe=class extends $e{constructor(e){Nn(),super(e)}eventFromException(e,n){return b(bt(x,this._options.stackParser,e,n))}eventFromMessage(e,n="info",r){return b(xt(this._options.stackParser,e,n,r,this._options.attachStacktrace))}captureException(e,n,r){if(this._options.autoSessionTracking&&this._sessionFlusher&&r){let s=r.getRequestSession();s&&s.status==="ok"&&(s.status="errored")}return super.captureException(e,n,r)}captureEvent(e,n,r){if(this._options.autoSessionTracking&&this._sessionFlusher&&r&&(e.type||"exception")==="exception"&&e.exception&&e.exception.values&&e.exception.values.length>0){let o=r.getRequestSession();o&&o.status==="ok"&&(o.status="errored")}return super.captureEvent(e,n,r)}close(e){return this._sessionFlusher&&this._sessionFlusher.close(),super.close(e)}initSessionFlusher(){let{release:e,environment:n}=this._options;e?this._sessionFlusher=new Be(this,{release:e,environment:n}):(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&f.warn("Cannot initialise an instance of SessionFlusher if no release is provided!")}captureCheckIn(e,n,r){let s=e.status!=="in_progress"&&e.checkInId?e.checkInId:g();if(!this._isEnabled())return(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&f.warn("SDK not enabled, will not capture checkin."),s;let i=this.getOptions(),{release:o,environment:a,tunnel:u}=i,c={check_in_id:s,monitor_slug:e.monitorSlug,status:e.status,release:o,environment:a};e.status!=="in_progress"&&(c.duration=e.duration),n&&(c.monitor_config={schedule:n.schedule,checkin_margin:n.checkinMargin,max_runtime:n.maxRuntime,timezone:n.timezone});let[d,l]=this._getTraceInfoFromScope(r);l&&(c.contexts={trace:l});let p=Bn(c,d,this.getSdkMetadata(),u,this.getDsn());return(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&f.info("Sending checkin:",e.monitorSlug,e.status),this._sendEnvelope(p),s}_captureRequestSession(){this._sessionFlusher?this._sessionFlusher.incrementSessionStatusCount():(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&f.warn("Discarded request mode session because autoSessionTracking option was disabled")}_prepareEvent(e,n,r){return this._options.platform&&(e.platform=e.platform||this._options.platform),this._options.runtime&&(e.contexts={...e.contexts,runtime:(e.contexts||{}).runtime||this._options.runtime}),this._options.serverName&&(e.server_name=e.server_name||this._options.serverName),super._prepareEvent(e,n,r)}_getTraceInfoFromScope(e){if(!e)return[void 0,void 0];let n=e.getSpan();if(n)return[n.transaction?n.transaction.getDynamicSamplingContext():void 0,n.getTraceContext()];let{traceId:r,spanId:s,parentSpanId:i,dsc:o}=e.getPropagationContext(),a={trace_id:r,span_id:s,parent_span_id:i};return o?[o,a]:[X(r,this,e),a]}};var es=30;function At(t,e,n=lt(t.bufferSize||es)){let r={},s=o=>n.drain(o);function i(o){let a=[];if(ve(o,(l,p)=>{let _=ke(p);if(yt(r,_)){let U=$n(l,p);t.recordDroppedEvent("ratelimit_backoff",_,U)}else a.push(l)}),a.length===0)return b();let u=M(o[0],a),c=l=>{ve(u,(p,_)=>{let U=$n(p,_);t.recordDroppedEvent(l,ke(_),U)})},d=()=>e({body:ht(u,t.textEncoder)}).then(l=>(l.statusCode!==void 0&&(l.statusCode<200||l.statusCode>=300)&&(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&f.warn(`Sentry responded with status code ${l.statusCode} to sent event.`),r=St(r,l),l),l=>{throw c("network_error"),l});return n.add(d).then(l=>l,l=>{if(l instanceof T)return(typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__)&&f.error("Skipped sending event because buffer is full."),c("queue_overflow"),b();throw l})}return i.__sentry__baseTransport__=!0,{send:i,flush:s}}function $n(t,e){if(!(e!=="event"&&e!=="transaction"))return Array.isArray(t)?t[1]:void 0}function Gn(t){return typeof t=="object"&&t!==null}function ts(t){return Gn(t)&&"handled"in t&&typeof t.handled=="boolean"&&"type"in t&&typeof t.type=="string"}function ns(t){return Gn(t)&&"mechanism"in t&&ts(t.mechanism)}function rs(){if(m.SENTRY_RELEASE&&m.SENTRY_RELEASE.id)return m.SENTRY_RELEASE.id}function Ln(t,e){return t!==void 0?(t[e[0]]=e[1],t):{[e[0]]:e[1]}}function Fn(t,e){return t(e.stack||"",1)}function ss(t){let e=t&&t.message;return e?e.error&&typeof e.error.message=="string"?e.error.message:e:"No error message"}function Yn(t,e){let n={type:e.name||e.constructor.name,value:ss(e)},r=Fn(t,e);return r.length&&(n.stacktrace={frames:r}),n.type===void 0&&n.value===""&&(n.value="Unrecoverable error caught"),n}function is(t,e,n,r){let s,o=(r&&r.data&&ns(r.data)?r.data.mechanism:void 0)??{handled:!0,type:"generic"};if(B(n))s=n;else{if(R(n)){let u=`Non-Error exception captured with keys: ${se(n)}`,c=t?.getClient(),d=c&&c.getOptions().normalizeDepth;t?.configureScope(l=>{l.setExtra("__serialized__",z(n,d))}),s=r&&r.syntheticException||new Error(u),s.message=u}else s=r&&r.syntheticException||new Error(n),s.message=n;o.synthetic=!0}let a={exception:{values:[Yn(e,s)]}};return ce(a,void 0,void 0),ue(a,o),{...a,event_id:r&&r.event_id}}function os(t,e,n="info",r,s){let i={event_id:r&&r.event_id,level:n,message:e};if(s&&r&&r.syntheticException){let o=Fn(t,r.syntheticException);o.length&&(i.exception={values:[{value:e,stacktrace:{frames:o}}]})}return i}var as=5,Ye=class{name=Ye.id;limit;constructor(e={}){this.limit=e.limit||as}setupOnce(e,n){let r=n().getClient();r&&e((s,i)=>{let o=n().getIntegration(Ye);return o?cs(r.getOptions().stackParser,o.limit,s,i):s})}},Le=Ye;he(Le,"id","LinkedErrors");function cs(t,e,n,r){if(!n.exception||!n.exception.values||!r||!O(r.originalException,Error))return n;let s=Hn(t,e,r.originalException);return n.exception.values=[...s,...n.exception.values],n}function Hn(t,e,n,r=[]){if(!O(n.cause,Error)||r.length+1>=e)return r;let s=Yn(t,n.cause);return Hn(t,e,n.cause,[s,...r])}var us={allowedHeaders:["CF-RAY","CF-Worker"]},Z,He=class{constructor(e={}){he(this,"name",He.id);Mt(this,Z,void 0);Ut(this,Z,{...us,...e})}setupOnce(e,n){n().getClient()&&e(s=>{let{sdkProcessingMetadata:i}=s;return!n().getIntegration(He)||!i||("request"in i&&i.request instanceof Request&&(s.request=ds(i.request,Xe(this,Z)),s.user=ls(s.user??{},i.request,Xe(this,Z))),"requestData"in i&&(s.request?s.request.data=i.requestData:s.request={data:i.requestData})),s})}},Ge=He;Z=new WeakMap,he(Ge,"id","RequestData");function ls(t,e,n){let r=e.headers.get("CF-Connecting-IP"),{allowedIps:s}=n,i={...t};return!("ip_address"in t)&&r&&s!==void 0&&fs(r,s)&&(i.ip_address=r),Object.keys(i).length>0?i:void 0}function ds(t,e){let n=t.headers.get("cookie"),r;if(n)try{r=ps(n)}catch{}let s={};for(let[c,d]of t.headers.entries())c!=="cookie"&&(s[c]=d);let i={method:t.method,cookies:r,headers:s};try{let c=new URL(t.url);i.url=`${c.protocol}//${c.hostname}${c.pathname}`,i.query_string=c.search}catch{let d=t.url.indexOf("?");d<0?i.url=t.url:(i.url=t.url.substr(0,d),i.query_string=t.url.substr(d+1))}let{allowedHeaders:o,allowedCookies:a,allowedSearchParams:u}=e;if(o!==void 0&&i.headers?(i.headers=Ot(i.headers,o),Object.keys(i.headers).length===0&&delete i.headers):delete i.headers,a!==void 0&&i.cookies?(i.cookies=Ot(i.cookies,a),Object.keys(i.cookies).length===0&&delete i.cookies):delete i.cookies,u!==void 0){let c=Object.fromEntries(new URLSearchParams(i.query_string)),d=new URLSearchParams;Object.keys(Ot(c,u)).forEach(l=>{d.set(l,c[l])}),i.query_string=d.toString()}else delete i.query_string;return i}function fs(t,e){return typeof e=="boolean"?e:e instanceof RegExp?e.test(t):Array.isArray(e)?e.map(r=>r.toLowerCase()).includes(t):!1}function Ot(t,e){let n=()=>!1;if(typeof e=="boolean")return e?t:{};if(e instanceof RegExp)n=r=>e.test(r);else if(Array.isArray(e)){let r=e.map(s=>s.toLowerCase());n=s=>r.includes(s.toLowerCase())}else return{};return Object.keys(t).filter(n).reduce((r,s)=>(r[s]=t[s],r),{})}function ps(t){if(typeof t!="string")return{};try{return t.split(";").map(e=>e.split("=")).reduce((e,[n,r])=>(e[decodeURIComponent(n.trim())]=decodeURIComponent(r.trim()),e),{})}catch{return{}}}function _s(t,e){let n={};return t.forEach(r=>{n[r.name]=r,r.setupOnce(s=>{e.getScope()?.addEventProcessor(s)},()=>e)}),n}var vt=class extends pe{#e=null;constructor(e){e._metadata=e._metadata||{},e._metadata.sdk=e._metadata.sdk||{name:"toucan-js",packages:[{name:"npm:toucan-js",version:"3.3.1"}],version:"3.3.1"},super(e)}setupIntegrations(){this._isEnabled()&&!this._integrationsInitialized&&this.#e&&(this._integrations=_s(this._options.integrations,this.#e),this._integrationsInitialized=!0)}eventFromException(e,n){return b(is(this.#e,this._options.stackParser,e,n))}eventFromMessage(e,n="info",r){return b(os(this._options.stackParser,e,n,r,this._options.attachStacktrace))}_prepareEvent(e,n,r){return e.platform=e.platform||"javascript",this.getOptions().request&&(e.sdkProcessingMetadata=Ln(e.sdkProcessingMetadata,["request",this.getOptions().request])),this.getOptions().requestData&&(e.sdkProcessingMetadata=Ln(e.sdkProcessingMetadata,["requestData",this.getOptions().requestData])),super._prepareEvent(e,n,r)}getSdk(){return this.#e}setSdk(e){this.#e=e}setRequestBody(e){this.getOptions().requestData=e}setEnabled(e){this.getOptions().enabled=e}};function ms(t){let[e,n]=ot(t);return[e,s=>{let i=n(s);if(i){let o=i.filename;i.abs_path=o!==void 0&&!o.startsWith("/")?`/${o}`:o,i.in_app=o!==void 0}return i}]}function hs(t){if(t)return ut(t,".js")}var gs=be(ms(hs));function Es(t){function e({body:n}){try{let s=(t.fetcher??fetch)(t.url,{method:"POST",headers:t.headers,body:n}).then(i=>({statusCode:i.status,headers:{"retry-after":i.headers.get("Retry-After"),"x-sentry-rate-limits":i.headers.get("X-Sentry-Rate-Limits")}}));return t.context&&t.context.waitUntil(s),s}catch(r){return L(r)}}return At(t,e)}var Fe=class extends Y{constructor(e){if(e.defaultIntegrations=e.defaultIntegrations===!1?[]:[...Array.isArray(e.defaultIntegrations)?e.defaultIntegrations:[new Ge(e.requestDataOptions),new Le]],e.release===void 0){let r=rs();r!==void 0&&(e.release=r)}let n=new vt({...e,transport:Es,integrations:Dt(e),stackParser:it(e.stackParser||gs),transportOptions:{...e.transportOptions,context:e.context}});super(n),n.setSdk(this),n.setupIntegrations()}setRequestBody(e){this.getClient()?.setRequestBody(e)}setEnabled(e){this.getClient()?.setEnabled(e)}captureCheckIn(e,n,r){return e.status==="in_progress"&&this.setContext("monitor",{slug:e.monitorSlug}),this.getClient().captureCheckIn(e,n,r)}};function jn(t,e,n,r,s){if(!(n&&r&&s))return;let i=new Fe({dsn:n,request:t,context:e,sampleRate:1,requestDataOptions:{allowedHeaders:["user-agent","cf-challenge","accept-encoding","accept-language","cf-ray","content-length","content-type","host"],allowedSearchParams:/(.*)/},transportOptions:{headers:{"CF-Access-Client-ID":r,"CF-Access-Client-Secret":s}}}),o=t.cf?.colo??"UNKNOWN";i.setTag("colo",o);let a=t.headers.get("user-agent")??"UA UNKNOWN";return i.setUser({userAgent:a,colo:o}),i}var qe=class{constructor(e){this.data=e}async get(e){let n=await Ts(e),r=kt(new Uint8Array(this.data,20),n);return r?xs(r):null}},Ts=async t=>{let n=new TextEncoder().encode(t),r=await crypto.subtle.digest("SHA-256",n.buffer);return new Uint8Array(r,0,16)},kt=(t,e)=>{if(t.byteLength===0)return!1;let n=t.byteOffset+(t.byteLength/40>>1)*40,r=new Uint8Array(t.buffer,n,16);if(r.byteLength!==e.byteLength)throw new TypeError("Search value and current value are of different lengths");let s=bs(e,r);if(s<0){let i=t.byteOffset,o=n-t.byteOffset;return kt(new Uint8Array(t.buffer,i,o),e)}else if(s>0){let i=n+40,o=t.buffer.byteLength-n-40;return kt(new Uint8Array(t.buffer,i,o),e)}else return new Uint8Array(t.buffer,n,40)},bs=(t,e)=>{if(t.byteLength<e.byteLength)return-1;if(t.byteLength>e.byteLength)return 1;for(let[n,r]of t.entries()){if(r<e[n])return-1;if(r>e[n])return 1}return 0},xs=t=>[...t.slice(16,16+16)].map(n=>n.toString(16).padStart(2,"0")).join("");var Pt=t=>({html_handling:t?.html_handling??"auto-trailing-slash",not_found_handling:t?.not_found_handling??"none"});var We=class extends Response{constructor(e,n){super(e,{...n,status:200})}},me=class extends Response{constructor(...[e,n]){super(e,{...n,status:404,statusText:"Not Found"})}},Q=class extends Response{constructor(...[e,n]){super(e,{...n,status:405,statusText:"Method Not Allowed"})}},ee=class extends Response{constructor(e,n){super(null,{...n,status:500})}},ze=class extends Response{constructor(...[e,n]){super(null,{...n,status:304,statusText:"Not Modified"})}},Ke=class extends Response{constructor(e,n){super(null,{...n,status:307,statusText:"Temporary Redirect",headers:{...n?.headers,Location:e}})}};var qn="public, max-age=0, must-revalidate";function Wn(t,e,n){let r=new Headers({"Content-Type":e,ETag:`"${t}"`});return Rs(n)&&r.append("Cache-Control",qn),r}function Rs(t){return!t.headers.has("Authorization")&&!t.headers.has("Range")}var zn=async(t,e,n,r)=>{let{pathname:s,search:i}=new URL(t.url),o=await Ve(s,e,n);if(!o)return new me;let a=t.method.toUpperCase();if(!["GET","HEAD"].includes(a))return new Q;if(o.redirect)return new Ke(o.redirect+i);if(!o.asset)return new ee(new Error("Unknown action"));let u=await r(o.asset.eTag),c=Wn(o.asset.eTag,u.contentType,t),d=`"${o.asset.eTag}"`,l=`W/${d}`,p=t.headers.get("If-None-Match")||"";if([l,d].includes(p))return new ze(null,{headers:c});let _=a==="HEAD"?null:u.readableStream;switch(o.asset.status){case 404:return new me(_,{headers:c});case 200:return new We(_,{headers:c})}},Ve=async(t,e,n,r=!1)=>{switch(e.html_handling){case"auto-trailing-slash":return Is(t,e,n,r);case"force-trailing-slash":return Ns(t,e,n,r);case"drop-trailing-slash":return Ds(t,e,n,r);case"none":return ws(t,e,n)}},Is=async(t,e,n,r)=>{let s=null,i=null,o=await n(t);if(t.endsWith("/index")){if(o)return{asset:{eTag:o,status:200},redirect:null};if(s=await h(`${t}.html`,t.slice(0,-5),e,n,r))return s;if(s=await h(`${t.slice(0,-6)}.html`,t.slice(0,-6),e,n,r))return s}else if(t.endsWith("/index.html")){if(s=await h(t,t.slice(0,-10),e,n,r))return s;if(s=await h(`${t.slice(0,-11)}.html`,t.slice(0,-11),e,n,r))return s}else if(t.endsWith("/")){if(i=await n(`${t}index.html`))return{asset:{eTag:i,status:200},redirect:null};if(s=await h(`${t.slice(0,-1)}.html`,t.slice(0,-1),e,n,r))return s}else if(t.endsWith(".html")){if(s=await h(t,t.slice(0,-5),e,n,r))return s;if(s=await h(`${t.slice(0,-5)}/index.html`,`${t.slice(0,-5)}/`,e,n,r))return s}return o?{asset:{eTag:o,status:200},redirect:null}:(i=await n(`${t}.html`))?{asset:{eTag:i,status:200},redirect:null}:(s=await h(`${t}/index.html`,`${t}/`,e,n,r))?s:Je(t,e,n)},Ns=async(t,e,n,r)=>{let s=null,i=null,o=await n(t);if(t.endsWith("/index")){if(o)return{asset:{eTag:o,status:200},redirect:null};if(s=await h(`${t}.html`,t.slice(0,-5),e,n,r))return s;if(s=await h(`${t.slice(0,-6)}.html`,t.slice(0,-5),e,n,r))return s}else if(t.endsWith("/index.html")){if(s=await h(t,t.slice(0,-10),e,n,r))return s;if(s=await h(`${t.slice(0,-11)}.html`,t.slice(0,-10),e,n,r))return s}else if(t.endsWith("/")){if(i=await n(`${t}index.html`))return{asset:{eTag:i,status:200},redirect:null};if(i=await n(`${t.slice(0,-1)}.html`))return{asset:{eTag:i,status:200},redirect:null}}else if(t.endsWith(".html")){if(s=await h(t,`${t.slice(0,-5)}/`,e,n,r))return s;if(o)return{asset:{eTag:o,status:200},redirect:null};if(s=await h(`${t.slice(0,-5)}/index.html`,`${t.slice(0,-5)}/`,e,n,r))return s}return o?{asset:{eTag:o,status:200},redirect:null}:(s=await h(`${t}.html`,`${t}/`,e,n,r))||(s=await h(`${t}/index.html`,`${t}/`,e,n,r))?s:Je(t,e,n)},Ds=async(t,e,n,r)=>{let s=null,i=null,o=await n(t);if(t.endsWith("/index")){if(o)return{asset:{eTag:o,status:200},redirect:null};if(t==="/index"){if(s=await h("/index.html","/",e,n,r))return s}else{if(s=await h(`${t.slice(0,-6)}.html`,t.slice(0,-6),e,n,r))return s;if(s=await h(`${t}.html`,t.slice(0,-6),e,n,r))return s}}else if(t.endsWith("/index.html"))if(t==="/index.html"){if(s=await h("/index.html","/",e,n,r))return s}else{if(s=await h(t,t.slice(0,-11),e,n,r))return s;if(o)return{asset:{eTag:o,status:200},redirect:null};if(s=await h(`${t.slice(0,-11)}.html`,t.slice(0,-11),e,n,r))return s}else if(t.endsWith("/"))if(t==="/"){if(i=await n("/index.html"))return{asset:{eTag:i,status:200},redirect:null}}else{if(s=await h(`${t.slice(0,-1)}.html`,t.slice(0,-1),e,n,r))return s;if(s=await h(`${t.slice(0,-1)}/index.html`,t.slice(0,-1),e,n,r))return s}else if(t.endsWith(".html")){if(s=await h(t,t.slice(0,-5),e,n,r))return s;if(s=await h(`${t.slice(0,-5)}/index.html`,t.slice(0,-5),e,n,r))return s}return o?{asset:{eTag:o,status:200},redirect:null}:(i=await n(`${t}.html`))?{asset:{eTag:i,status:200},redirect:null}:(i=await n(`${t}/index.html`))?{asset:{eTag:i,status:200},redirect:null}:Je(t,e,n)},ws=async(t,e,n)=>{let r=await n(t);return r?{asset:{eTag:r,status:200},redirect:null}:Je(t,e,n)},Je=async(t,e,n)=>{switch(e.not_found_handling){case"single-page-application":{let r=await n("/index.html");return r?{asset:{eTag:r,status:200},redirect:null}:null}case"404-page":{let r=t;for(;r;){r=r.slice(0,r.lastIndexOf("/"));let s=await n(`${r}/404.html`);if(s)return{asset:{eTag:s,status:404},redirect:null}}return null}case"none":default:return null}},h=async(t,e,n,r,s)=>{if(s)return null;if(!await r(e)){let i=await Ve(e,n,r,!0);if(i?.asset&&i.asset.eTag===await r(t))return{asset:null,redirect:e}}return null};async function Kn(t,e,n=1){let r=0;for(;r<=n;)try{return await t.getWithMetadata(e,{type:"stream",cacheTtl:31536e3})}catch{if(r>=n)throw new Error(`Requested asset ${e} could not be fetched from KV namespace.`);await new Promise(i=>setTimeout(i,Math.pow(2,r++)*1e3))}}var Vn=class extends As{async fetch(t){let e;try{return e=jn(t,this.ctx,this.env.SENTRY_DSN,this.env.SENTRY_ACCESS_CLIENT_ID,this.env.SENTRY_ACCESS_CLIENT_SECRET),zn(t,Pt(this.env.CONFIG),this.unstable_exists.bind(this),this.unstable_getByETag.bind(this))}catch(n){let r=new ee(n);return e&&e.captureException(n),r}}async unstable_canFetch(t){let e=new URL(t.url),n=t.method.toUpperCase(),r=await Ve(e.pathname,{...Pt(this.env.CONFIG),not_found_handling:"none"},this.unstable_exists.bind(this));return r&&["GET","HEAD"].includes(n)?new Q:r!==null}async unstable_getByETag(t){let e=await Kn(this.env.ASSETS_KV_NAMESPACE,t);if(!e||!e.value)throw new Error(`Requested asset ${t} exists in the asset manifest but not in the KV namespace.`);return{readableStream:e.value,contentType:e.metadata?.contentType??"application/octet-stream"}}async unstable_getByPathname(t){let e=await this.unstable_exists(t);return e?this.unstable_getByETag(e):null}async unstable_exists(t){return await new qe(this.env.ASSETS_MANIFEST).get(t)}};export{Vn as default};
