import { z } from "zod";
export declare const RoutingConfigSchema: z.ZodObject<{
    has_user_worker: z.<PERSON>al<z.ZodBoolean>;
}, "strip", z.<PERSON><PERSON><PERSON>ype<PERSON>ny, {
    has_user_worker?: boolean;
}, {
    has_user_worker?: boolean;
}>;
export declare const AssetConfigSchema: z.ZodObject<{
    html_handling: z.ZodOptional<z.ZodEnum<["auto-trailing-slash", "force-trailing-slash", "drop-trailing-slash", "none"]>>;
    not_found_handling: z.<PERSON>od<PERSON>ptional<z.Zod<PERSON>num<["single-page-application", "404-page", "none"]>>;
}, "strip", z.<PERSON>odType<PERSON>ny, {
    html_handling?: "none" | "auto-trailing-slash" | "force-trailing-slash" | "drop-trailing-slash";
    not_found_handling?: "none" | "single-page-application" | "404-page";
}, {
    html_handling?: "none" | "auto-trailing-slash" | "force-trailing-slash" | "drop-trailing-slash";
    not_found_handling?: "none" | "single-page-application" | "404-page";
}>;
export type RoutingConfig = z.infer<typeof RoutingConfigSchema>;
export type AssetConfig = z.infer<typeof AssetConfigSchema>;
