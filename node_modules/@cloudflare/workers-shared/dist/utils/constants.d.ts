/** Reserved header at the start of the whole manifest, NOT in each entry (currently unused)
 * manifest = [HEADER, [ entry = PATH_HASH, CONTENT_HASH, TAIL], [entry], ... , [entry] ]
 */
export declare const HEADER_SIZE = 20;
/** manifest = [HEADER, [ entry = PATH_HASH, CONTENT_HASH, TAIL], [entry], ... , [entry] ] */
export declare const PATH_HASH_SIZE = 16;
/** manifest = [HEADER, [ entry = PATH_HASH, CONTENT_HASH, TAIL], [entry], ... , [entry] ] */
export declare const CONTENT_HASH_SIZE = 16;
/** manifest = [HEADER, [ entry = PATH_HASH, CONTENT_HASH, TAIL], [entry], ... , [entry] ] */
export declare const TAIL_SIZE = 8;
/** offset of PATH_HASH from start of each entry
 *  manifest = [HEADER, [ entry = PATH_HASH, CONTENT_HASH, TAIL], [entry], ... , [entry] ] */
export declare const PATH_HASH_OFFSET = 0;
/** offset of CONTENT_HASH from start of each entry
 *  manifest = [HEADER, [ entry = PATH_HASH, CONTENT_HASH, TAIL], [entry], ... , [entry] ] */
export declare const CONTENT_HASH_OFFSET = 16;
/** manifest = [HEADER, [ entry = PATH_HASH, CONTENT_HASH, TAIL], [entry], ... , [entry] ] */
export declare const ENTRY_SIZE: number;
/** Maximum number of assets that can be deployed with a worker */
export declare const MAX_ASSET_COUNT = 20000;
/** Maximum size per asset that can be deployed with a worker */
export declare const MAX_ASSET_SIZE: number;
