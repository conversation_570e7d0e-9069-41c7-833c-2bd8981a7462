{"version": 3, "sources": ["../asset-worker/src/index.ts", "../../../node_modules/.pnpm/@sentry+utils@7.76.0/node_modules/@sentry/src/is.ts", "../../../node_modules/.pnpm/@sentry+utils@7.76.0/node_modules/@sentry/src/string.ts", "../../../node_modules/.pnpm/@sentry+utils@7.76.0/node_modules/@sentry/src/worldwide.ts", "../../../node_modules/.pnpm/@sentry+utils@7.76.0/node_modules/@sentry/src/browser.ts", "../../../node_modules/.pnpm/@sentry+utils@7.76.0/node_modules/@sentry/src/logger.ts", "../../../node_modules/.pnpm/@sentry+utils@7.76.0/node_modules/@sentry/src/dsn.ts", "../../../node_modules/.pnpm/@sentry+utils@7.76.0/node_modules/@sentry/src/error.ts", "../../../node_modules/.pnpm/@sentry+utils@7.76.0/node_modules/@sentry/src/object.ts", "../../../node_modules/.pnpm/@sentry+utils@7.76.0/node_modules/@sentry/src/node-stack-trace.ts", "../../../node_modules/.pnpm/@sentry+utils@7.76.0/node_modules/@sentry/src/stacktrace.ts", "../../../node_modules/.pnpm/@sentry+utils@7.76.0/node_modules/@sentry/src/supports.ts", "../../../node_modules/.pnpm/@sentry+utils@7.76.0/node_modules/@sentry/src/vendor/supportsHistory.ts", "../../../node_modules/.pnpm/@sentry+utils@7.76.0/node_modules/@sentry/src/instrument.ts", "../../../node_modules/.pnpm/@sentry+utils@7.76.0/node_modules/@sentry/src/env.ts", "../../../node_modules/.pnpm/@sentry+utils@7.76.0/node_modules/@sentry/src/node.ts", "../../../node_modules/.pnpm/@sentry+utils@7.76.0/node_modules/@sentry/src/memo.ts", "../../../node_modules/.pnpm/@sentry+utils@7.76.0/node_modules/@sentry/src/misc.ts", "../../../node_modules/.pnpm/@sentry+utils@7.76.0/node_modules/@sentry/src/normalize.ts", "../../../node_modules/.pnpm/@sentry+utils@7.76.0/node_modules/@sentry/src/path.ts", "../../../node_modules/.pnpm/@sentry+utils@7.76.0/node_modules/@sentry/src/syncpromise.ts", "../../../node_modules/.pnpm/@sentry+utils@7.76.0/node_modules/@sentry/src/promisebuffer.ts", "../../../node_modules/.pnpm/@sentry+utils@7.76.0/node_modules/@sentry/src/time.ts", "../../../node_modules/.pnpm/@sentry+utils@7.76.0/node_modules/@sentry/src/tracing.ts", "../../../node_modules/.pnpm/@sentry+utils@7.76.0/node_modules/@sentry/src/envelope.ts", "../../../node_modules/.pnpm/@sentry+utils@7.76.0/node_modules/@sentry/src/ratelimit.ts", "../../../node_modules/.pnpm/@sentry+utils@7.76.0/node_modules/@sentry/src/eventbuilder.ts", "../../../node_modules/.pnpm/@sentry+core@7.76.0/node_modules/@sentry/src/constants.ts", "../../../node_modules/.pnpm/@sentry+core@7.76.0/node_modules/@sentry/src/eventProcessors.ts", "../../../node_modules/.pnpm/@sentry+core@7.76.0/node_modules/@sentry/src/session.ts", "../../../node_modules/.pnpm/@sentry+core@7.76.0/node_modules/@sentry/src/scope.ts", "../../../node_modules/.pnpm/@sentry+core@7.76.0/node_modules/@sentry/src/hub.ts", "../../../node_modules/.pnpm/@sentry+core@7.76.0/node_modules/@sentry/src/tracing/utils.ts", "../../../node_modules/.pnpm/@sentry+core@7.76.0/node_modules/@sentry/src/tracing/errors.ts", "../../../node_modules/.pnpm/@sentry+core@7.76.0/node_modules/@sentry/src/tracing/span.ts", "../../../node_modules/.pnpm/@sentry+core@7.76.0/node_modules/@sentry/src/tracing/dynamicSamplingContext.ts", "../../../node_modules/.pnpm/@sentry+core@7.76.0/node_modules/@sentry/src/tracing/transaction.ts", "../../../node_modules/.pnpm/@sentry+core@7.76.0/node_modules/@sentry/src/utils/hasTracingEnabled.ts", "../../../node_modules/.pnpm/@sentry+core@7.76.0/node_modules/@sentry/src/tracing/sampling.ts", "../../../node_modules/.pnpm/@sentry+core@7.76.0/node_modules/@sentry/src/tracing/hubextensions.ts", "../../../node_modules/.pnpm/@sentry+core@7.76.0/node_modules/@sentry/src/sessionflusher.ts", "../../../node_modules/.pnpm/@sentry+core@7.76.0/node_modules/@sentry/src/api.ts", "../../../node_modules/.pnpm/@sentry+core@7.76.0/node_modules/@sentry/src/envelope.ts", "../../../node_modules/.pnpm/@sentry+core@7.76.0/node_modules/@sentry/src/integration.ts", "../../../node_modules/.pnpm/@sentry+core@7.76.0/node_modules/@sentry/src/utils/prepareEvent.ts", "../../../node_modules/.pnpm/@sentry+core@7.76.0/node_modules/@sentry/src/baseclient.ts", "../../../node_modules/.pnpm/@sentry+core@7.76.0/node_modules/@sentry/src/checkin.ts", "../../../node_modules/.pnpm/@sentry+core@7.76.0/node_modules/@sentry/src/server-runtime-client.ts", "../../../node_modules/.pnpm/@sentry+core@7.76.0/node_modules/@sentry/src/transports/base.ts", "../../../node_modules/.pnpm/toucan-js@3.3.1_patch_hash=b5gffumfuckaq3c77sda2gdfuq/node_modules/toucan-js/dist/index.esm.js", "../utils/sentry.ts", "../asset-worker/src/assets-manifest.ts", "../asset-worker/src/configuration.ts", "../asset-worker/src/responses.ts", "../asset-worker/src/constants.ts", "../asset-worker/src/utils/headers.ts", "../asset-worker/src/handler.ts", "../asset-worker/src/utils/kv.ts"], "sourcesContent": ["import { WorkerEntrypoint } from \"cloudflare:workers\";\nimport { setupSentry } from \"../../utils/sentry\";\nimport { AssetsManifest } from \"./assets-manifest\";\nimport { applyConfigurationDefaults } from \"./configuration\";\nimport { getIntent, handleRequest } from \"./handler\";\nimport {\n\tInternalServerErrorResponse,\n\tMethodNotAllowedResponse,\n} from \"./responses\";\nimport { getAssetWithMetadataFromKV } from \"./utils/kv\";\nimport type { AssetConfig } from \"../../utils/types\";\n\ntype Env = {\n\t/*\n\t * ASSETS_MANIFEST is a pipeline binding to an ArrayBuffer containing the\n\t * binary-encoded site manifest\n\t */\n\tASSETS_MANIFEST: ArrayBuffer;\n\n\t/*\n\t * ASSETS_KV_NAMESPACE is a pipeline binding to the KV namespace that the\n\t * assets are in.\n\t */\n\tASSETS_KV_NAMESPACE: KVNamespace;\n\n\tCONFIG: AssetConfig;\n\n\tSENTRY_DSN: string;\n\n\tSENTRY_ACCESS_CLIENT_ID: string;\n\tSENTRY_ACCESS_CLIENT_SECRET: string;\n};\n\n/*\n * The Asset Worker is currently set up as a `WorkerEntrypoint` class so\n * that it is able to accept RPC calls to any of its public methods. There\n * are currently four such public methods defined on this Worker:\n * `canFetch`, `getByETag`, `getByPathname` and `exists`. While we are\n * stabilising the implementation details of these methods, we would like\n * to prevent developers from having their Workers call these methods\n * directly. To that end, we are adopting the `unstable_<method_name>`\n * naming convention for all of the aforementioned methods, to indicate that\n * they are still in flux and that they are not an established API contract.\n */\nexport default class extends WorkerEntrypoint<Env> {\n\tasync fetch(request: Request): Promise<Response> {\n\t\tlet sentry: ReturnType<typeof setupSentry> | undefined;\n\t\ttry {\n\t\t\tsentry = setupSentry(\n\t\t\t\trequest,\n\t\t\t\tthis.ctx,\n\t\t\t\tthis.env.SENTRY_DSN,\n\t\t\t\tthis.env.SENTRY_ACCESS_CLIENT_ID,\n\t\t\t\tthis.env.SENTRY_ACCESS_CLIENT_SECRET\n\t\t\t);\n\n\t\t\treturn handleRequest(\n\t\t\t\trequest,\n\t\t\t\tapplyConfigurationDefaults(this.env.CONFIG),\n\t\t\t\tthis.unstable_exists.bind(this),\n\t\t\t\tthis.unstable_getByETag.bind(this)\n\t\t\t);\n\t\t} catch (err) {\n\t\t\tconst response = new InternalServerErrorResponse(err as Error);\n\n\t\t\t// Log to Sentry if we can\n\t\t\tif (sentry) {\n\t\t\t\tsentry.captureException(err);\n\t\t\t}\n\n\t\t\treturn response;\n\t\t}\n\t}\n\n\tasync unstable_canFetch(request: Request): Promise<boolean | Response> {\n\t\tconst url = new URL(request.url);\n\t\tconst method = request.method.toUpperCase();\n\t\tconst intent = await getIntent(\n\t\t\turl.pathname,\n\t\t\t{\n\t\t\t\t...applyConfigurationDefaults(this.env.CONFIG),\n\t\t\t\tnot_found_handling: \"none\",\n\t\t\t},\n\t\t\tthis.unstable_exists.bind(this)\n\t\t);\n\t\t// if asset exists but non GET/HEAD method, 405\n\t\tif (intent && [\"GET\", \"HEAD\"].includes(method)) {\n\t\t\treturn new MethodNotAllowedResponse();\n\t\t}\n\t\tif (intent === null) {\n\t\t\treturn false;\n\t\t}\n\t\treturn true;\n\t}\n\n\tasync unstable_getByETag(\n\t\teTag: string\n\t): Promise<{ readableStream: ReadableStream; contentType: string }> {\n\t\tconst asset = await getAssetWithMetadataFromKV(\n\t\t\tthis.env.ASSETS_KV_NAMESPACE,\n\t\t\teTag\n\t\t);\n\n\t\tif (!asset || !asset.value) {\n\t\t\tthrow new Error(\n\t\t\t\t`Requested asset ${eTag} exists in the asset manifest but not in the KV namespace.`\n\t\t\t);\n\t\t}\n\n\t\treturn {\n\t\t\treadableStream: asset.value,\n\t\t\tcontentType: asset.metadata?.contentType ?? \"application/octet-stream\",\n\t\t};\n\t}\n\n\tasync unstable_getByPathname(\n\t\tpathname: string\n\t): Promise<{ readableStream: ReadableStream; contentType: string } | null> {\n\t\tconst eTag = await this.unstable_exists(pathname);\n\t\tif (!eTag) {\n\t\t\treturn null;\n\t\t}\n\n\t\treturn this.unstable_getByETag(eTag);\n\t}\n\n\tasync unstable_exists(pathname: string): Promise<string | null> {\n\t\tconst assetsManifest = new AssetsManifest(this.env.ASSETS_MANIFEST);\n\t\treturn await assetsManifest.get(pathname);\n\t}\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable @typescript-eslint/explicit-module-boundary-types */\n\nimport type { PolymorphicEvent, Primitive } from '@sentry/types';\n\n// eslint-disable-next-line @typescript-eslint/unbound-method\nconst objectToString = Object.prototype.toString;\n\n/**\n * Checks whether given value's type is one of a few Error or Error-like\n * {@link isError}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isError(wat: unknown): wat is Error {\n  switch (objectToString.call(wat)) {\n    case '[object Error]':\n    case '[object Exception]':\n    case '[object DOMException]':\n      return true;\n    default:\n      return isInstanceOf(wat, Error);\n  }\n}\n/**\n * Checks whether given value is an instance of the given built-in class.\n *\n * @param wat The value to be checked\n * @param className\n * @returns A boolean representing the result.\n */\nfunction isBuiltin(wat: unknown, className: string): boolean {\n  return objectToString.call(wat) === `[object ${className}]`;\n}\n\n/**\n * Checks whether given value's type is ErrorEvent\n * {@link isErrorEvent}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isErrorEvent(wat: unknown): boolean {\n  return isBuiltin(wat, 'ErrorEvent');\n}\n\n/**\n * Checks whether given value's type is DOMError\n * {@link isDOMError}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isDOMError(wat: unknown): boolean {\n  return isBuiltin(wat, 'DOMError');\n}\n\n/**\n * Checks whether given value's type is DOMException\n * {@link isDOMException}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isDOMException(wat: unknown): boolean {\n  return isBuiltin(wat, 'DOMException');\n}\n\n/**\n * Checks whether given value's type is a string\n * {@link isString}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isString(wat: unknown): wat is string {\n  return isBuiltin(wat, 'String');\n}\n\n/**\n * Checks whether given value is a primitive (undefined, null, number, boolean, string, bigint, symbol)\n * {@link isPrimitive}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isPrimitive(wat: unknown): wat is Primitive {\n  return wat === null || (typeof wat !== 'object' && typeof wat !== 'function');\n}\n\n/**\n * Checks whether given value's type is an object literal\n * {@link isPlainObject}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isPlainObject(wat: unknown): wat is Record<string, unknown> {\n  return isBuiltin(wat, 'Object');\n}\n\n/**\n * Checks whether given value's type is an Event instance\n * {@link isEvent}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isEvent(wat: unknown): wat is PolymorphicEvent {\n  return typeof Event !== 'undefined' && isInstanceOf(wat, Event);\n}\n\n/**\n * Checks whether given value's type is an Element instance\n * {@link isElement}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isElement(wat: unknown): boolean {\n  return typeof Element !== 'undefined' && isInstanceOf(wat, Element);\n}\n\n/**\n * Checks whether given value's type is an regexp\n * {@link isRegExp}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isRegExp(wat: unknown): wat is RegExp {\n  return isBuiltin(wat, 'RegExp');\n}\n\n/**\n * Checks whether given value has a then function.\n * @param wat A value to be checked.\n */\nexport function isThenable(wat: any): wat is PromiseLike<any> {\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n  return Boolean(wat && wat.then && typeof wat.then === 'function');\n}\n\n/**\n * Checks whether given value's type is a SyntheticEvent\n * {@link isSyntheticEvent}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isSyntheticEvent(wat: unknown): boolean {\n  return isPlainObject(wat) && 'nativeEvent' in wat && 'preventDefault' in wat && 'stopPropagation' in wat;\n}\n\n/**\n * Checks whether given value is NaN\n * {@link isNaN}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isNaN(wat: unknown): boolean {\n  return typeof wat === 'number' && wat !== wat;\n}\n\n/**\n * Checks whether given value's type is an instance of provided constructor.\n * {@link isInstanceOf}.\n *\n * @param wat A value to be checked.\n * @param base A constructor to be used in a check.\n * @returns A boolean representing the result.\n */\nexport function isInstanceOf(wat: any, base: any): boolean {\n  try {\n    return wat instanceof base;\n  } catch (_e) {\n    return false;\n  }\n}\n\ninterface VueViewModel {\n  // Vue3\n  __isVue?: boolean;\n  // Vue2\n  _isVue?: boolean;\n}\n/**\n * Checks whether given value's type is a Vue ViewModel.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isVueViewModel(wat: unknown): boolean {\n  // Not using Object.prototype.toString because in Vue 3 it would read the instance's Symbol(Symbol.toStringTag) property.\n  return !!(typeof wat === 'object' && wat !== null && ((wat as VueViewModel).__isVue || (wat as VueViewModel)._isVue));\n}\n", "import { isRegExp, isString, isVueViewModel } from './is';\n\nexport { escapeStringForRegex } from './vendor/escapeStringForRegex';\n\n/**\n * Truncates given string to the maximum characters count\n *\n * @param str An object that contains serializable values\n * @param max Maximum number of characters in truncated string (0 = unlimited)\n * @returns string Encoded\n */\nexport function truncate(str: string, max: number = 0): string {\n  if (typeof str !== 'string' || max === 0) {\n    return str;\n  }\n  return str.length <= max ? str : `${str.slice(0, max)}...`;\n}\n\n/**\n * This is basically just `trim_line` from\n * https://github.com/getsentry/sentry/blob/master/src/sentry/lang/javascript/processor.py#L67\n *\n * @param str An object that contains serializable values\n * @param max Maximum number of characters in truncated string\n * @returns string Encoded\n */\nexport function snipLine(line: string, colno: number): string {\n  let newLine = line;\n  const lineLength = newLine.length;\n  if (lineLength <= 150) {\n    return newLine;\n  }\n  if (colno > lineLength) {\n    // eslint-disable-next-line no-param-reassign\n    colno = lineLength;\n  }\n\n  let start = Math.max(colno - 60, 0);\n  if (start < 5) {\n    start = 0;\n  }\n\n  let end = Math.min(start + 140, lineLength);\n  if (end > lineLength - 5) {\n    end = lineLength;\n  }\n  if (end === lineLength) {\n    start = Math.max(end - 140, 0);\n  }\n\n  newLine = newLine.slice(start, end);\n  if (start > 0) {\n    newLine = `'{snip} ${newLine}`;\n  }\n  if (end < lineLength) {\n    newLine += ' {snip}';\n  }\n\n  return newLine;\n}\n\n/**\n * Join values in array\n * @param input array of values to be joined together\n * @param delimiter string to be placed in-between values\n * @returns Joined values\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function safeJoin(input: any[], delimiter?: string): string {\n  if (!Array.isArray(input)) {\n    return '';\n  }\n\n  const output = [];\n  // eslint-disable-next-line @typescript-eslint/prefer-for-of\n  for (let i = 0; i < input.length; i++) {\n    const value = input[i];\n    try {\n      // This is a hack to fix a Vue3-specific bug that causes an infinite loop of\n      // console warnings. This happens when a Vue template is rendered with\n      // an undeclared variable, which we try to stringify, ultimately causing\n      // Vue to issue another warning which repeats indefinitely.\n      // see: https://github.com/getsentry/sentry-javascript/pull/8981\n      if (isVueViewModel(value)) {\n        output.push('[VueViewModel]');\n      } else {\n        output.push(String(value));\n      }\n    } catch (e) {\n      output.push('[value cannot be serialized]');\n    }\n  }\n\n  return output.join(delimiter);\n}\n\n/**\n * Checks if the given value matches a regex or string\n *\n * @param value The string to test\n * @param pattern Either a regex or a string against which `value` will be matched\n * @param requireExactStringMatch If true, `value` must match `pattern` exactly. If false, `value` will match\n * `pattern` if it contains `pattern`. Only applies to string-type patterns.\n */\nexport function isMatchingPattern(\n  value: string,\n  pattern: RegExp | string,\n  requireExactStringMatch: boolean = false,\n): boolean {\n  if (!isString(value)) {\n    return false;\n  }\n\n  if (isRegExp(pattern)) {\n    return pattern.test(value);\n  }\n  if (isString(pattern)) {\n    return requireExactStringMatch ? value === pattern : value.includes(pattern);\n  }\n\n  return false;\n}\n\n/**\n * Test the given string against an array of strings and regexes. By default, string matching is done on a\n * substring-inclusion basis rather than a strict equality basis\n *\n * @param testString The string to test\n * @param patterns The patterns against which to test the string\n * @param requireExactStringMatch If true, `testString` must match one of the given string patterns exactly in order to\n * count. If false, `testString` will match a string pattern if it contains that pattern.\n * @returns\n */\nexport function stringMatchesSomePattern(\n  testString: string,\n  patterns: Array<string | RegExp> = [],\n  requireExactStringMatch: boolean = false,\n): boolean {\n  return patterns.some(pattern => isMatchingPattern(testString, pattern, requireExactStringMatch));\n}\n", "/**\n * NOTE: In order to avoid circular dependencies, if you add a function to this module and it needs to print something,\n * you must either a) use `console.log` rather than the logger, or b) put your function elsewhere.\n *\n * Note: This file was originally called `global.ts`, but was changed to unblock users which might be doing\n * string replaces with bundlers like Vite for `global` (would break imports that rely on importing from utils/src/global).\n *\n * Why worldwide?\n *\n * Why not?\n */\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n\nimport type { Integration } from '@sentry/types';\n\nimport type { SdkSource } from './env';\n\n/** Internal global with common properties and Sentry extensions  */\nexport interface InternalGlobal {\n  navigator?: { userAgent?: string };\n  console: Console;\n  Sentry?: {\n    Integrations?: Integration[];\n  };\n  onerror?: {\n    (msg: unknown, url: unknown, line: unknown, column: unknown, error: unknown): boolean;\n    __SENTRY_INSTRUMENTED__?: true;\n    __SENTRY_LOADER__?: true;\n  };\n  onunhandledrejection?: {\n    (event: unknown): boolean;\n    __SENTRY_INSTRUMENTED__?: true;\n    __SENTRY_LOADER__?: true;\n  };\n  SENTRY_ENVIRONMENT?: string;\n  SENTRY_DSN?: string;\n  SENTRY_RELEASE?: {\n    id?: string;\n  };\n  SENTRY_SDK_SOURCE?: SdkSource;\n  /**\n   * Debug IDs are indirectly injected by Sentry CLI or bundler plugins to directly reference a particular source map\n   * for resolving of a source file. The injected code will place an entry into the record for each loaded bundle/JS\n   * file.\n   */\n  _sentryDebugIds?: Record<string, string>;\n  __SENTRY__: {\n    globalEventProcessors: any;\n    hub: any;\n    logger: any;\n    extensions?: {\n      /** Extension methods for the hub, which are bound to the current Hub instance */\n      // eslint-disable-next-line @typescript-eslint/ban-types\n      [key: string]: Function;\n    };\n  };\n  /**\n   * Raw module metadata that is injected by bundler plugins.\n   *\n   * Keys are `error.stack` strings, values are the metadata.\n   */\n  _sentryModuleMetadata?: Record<string, any>;\n}\n\n// The code below for 'isGlobalObj' and 'GLOBAL_OBJ' was copied from core-js before modification\n// https://github.com/zloirock/core-js/blob/1b944df55282cdc99c90db5f49eb0b6eda2cc0a3/packages/core-js/internals/global.js\n// core-js has the following licence:\n//\n// Copyright (c) 2014-2022 Denis Pushkarev\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\n/** Returns 'obj' if it's the global object, otherwise returns undefined */\nfunction isGlobalObj(obj: { Math?: Math }): any | undefined {\n  return obj && obj.Math == Math ? obj : undefined;\n}\n\n/** Get's the global object for the current JavaScript runtime */\nexport const GLOBAL_OBJ: InternalGlobal =\n  (typeof globalThis == 'object' && isGlobalObj(globalThis)) ||\n  // eslint-disable-next-line no-restricted-globals\n  (typeof window == 'object' && isGlobalObj(window)) ||\n  (typeof self == 'object' && isGlobalObj(self)) ||\n  (typeof global == 'object' && isGlobalObj(global)) ||\n  (function (this: any) {\n    return this;\n  })() ||\n  {};\n\n/**\n * @deprecated Use GLOBAL_OBJ instead or WINDOW from @sentry/browser. This will be removed in v8\n */\nexport function getGlobalObject<T>(): T & InternalGlobal {\n  return GLOBAL_OBJ as T & InternalGlobal;\n}\n\n/**\n * Returns a global singleton contained in the global `__SENTRY__` object.\n *\n * If the singleton doesn't already exist in `__SENTRY__`, it will be created using the given factory\n * function and added to the `__SENTRY__` object.\n *\n * @param name name of the global singleton on __SENTRY__\n * @param creator creator Factory function to create the singleton if it doesn't already exist on `__SENTRY__`\n * @param obj (Optional) The global object on which to look for `__SENTRY__`, if not `GLOBAL_OBJ`'s return value\n * @returns the singleton\n */\nexport function getGlobalSingleton<T>(name: keyof InternalGlobal['__SENTRY__'], creator: () => T, obj?: unknown): T {\n  const gbl = (obj || GLOBAL_OBJ) as InternalGlobal;\n  const __SENTRY__ = (gbl.__SENTRY__ = gbl.__SENTRY__ || {});\n  const singleton = __SENTRY__[name] || (__SENTRY__[name] = creator());\n  return singleton;\n}\n", "import { isString } from './is';\nimport { getGlobalObject } from './worldwide';\n\n// eslint-disable-next-line deprecation/deprecation\nconst WINDOW = getGlobalObject<Window>();\n\nconst DEFAULT_MAX_STRING_LENGTH = 80;\n\n/**\n * Given a child DOM element, returns a query-selector statement describing that\n * and its ancestors\n * e.g. [HTMLElement] => body > div > input#foo.btn[name=baz]\n * @returns generated DOM path\n */\nexport function htmlTreeAsString(\n  elem: unknown,\n  options: string[] | { keyAttrs?: string[]; maxStringLength?: number } = {},\n): string {\n  type SimpleNode = {\n    parentNode: SimpleNode;\n  } | null;\n\n  if (!elem) {\n    return '<unknown>';\n  }\n\n  // try/catch both:\n  // - accessing event.target (see getsentry/raven-js#838, #768)\n  // - `htmlTreeAsString` because it's complex, and just accessing the DOM incorrectly\n  // - can throw an exception in some circumstances.\n  try {\n    let currentElem = elem as SimpleNode;\n    const MAX_TRAVERSE_HEIGHT = 5;\n    const out = [];\n    let height = 0;\n    let len = 0;\n    const separator = ' > ';\n    const sepLength = separator.length;\n    let nextStr;\n    const keyAttrs = Array.isArray(options) ? options : options.keyAttrs;\n    const maxStringLength = (!Array.isArray(options) && options.maxStringLength) || DEFAULT_MAX_STRING_LENGTH;\n\n    while (currentElem && height++ < MAX_TRAVERSE_HEIGHT) {\n      nextStr = _htmlElementAsString(currentElem, keyAttrs);\n      // bail out if\n      // - nextStr is the 'html' element\n      // - the length of the string that would be created exceeds maxStringLength\n      //   (ignore this limit if we are on the first iteration)\n      if (nextStr === 'html' || (height > 1 && len + out.length * sepLength + nextStr.length >= maxStringLength)) {\n        break;\n      }\n\n      out.push(nextStr);\n\n      len += nextStr.length;\n      currentElem = currentElem.parentNode;\n    }\n\n    return out.reverse().join(separator);\n  } catch (_oO) {\n    return '<unknown>';\n  }\n}\n\n/**\n * Returns a simple, query-selector representation of a DOM element\n * e.g. [HTMLElement] => input#foo.btn[name=baz]\n * @returns generated DOM path\n */\nfunction _htmlElementAsString(el: unknown, keyAttrs?: string[]): string {\n  const elem = el as {\n    tagName?: string;\n    id?: string;\n    className?: string;\n    getAttribute(key: string): string;\n  };\n\n  const out = [];\n  let className;\n  let classes;\n  let key;\n  let attr;\n  let i;\n\n  if (!elem || !elem.tagName) {\n    return '';\n  }\n\n  out.push(elem.tagName.toLowerCase());\n\n  // Pairs of attribute keys defined in `serializeAttribute` and their values on element.\n  const keyAttrPairs =\n    keyAttrs && keyAttrs.length\n      ? keyAttrs.filter(keyAttr => elem.getAttribute(keyAttr)).map(keyAttr => [keyAttr, elem.getAttribute(keyAttr)])\n      : null;\n\n  if (keyAttrPairs && keyAttrPairs.length) {\n    keyAttrPairs.forEach(keyAttrPair => {\n      out.push(`[${keyAttrPair[0]}=\"${keyAttrPair[1]}\"]`);\n    });\n  } else {\n    if (elem.id) {\n      out.push(`#${elem.id}`);\n    }\n\n    // eslint-disable-next-line prefer-const\n    className = elem.className;\n    if (className && isString(className)) {\n      classes = className.split(/\\s+/);\n      for (i = 0; i < classes.length; i++) {\n        out.push(`.${classes[i]}`);\n      }\n    }\n  }\n  const allowedAttrs = ['aria-label', 'type', 'name', 'title', 'alt'];\n  for (i = 0; i < allowedAttrs.length; i++) {\n    key = allowedAttrs[i];\n    attr = elem.getAttribute(key);\n    if (attr) {\n      out.push(`[${key}=\"${attr}\"]`);\n    }\n  }\n  return out.join('');\n}\n\n/**\n * A safe form of location.href\n */\nexport function getLocationHref(): string {\n  try {\n    return WINDOW.document.location.href;\n  } catch (oO) {\n    return '';\n  }\n}\n\n/**\n * Gets a DOM element by using document.querySelector.\n *\n * This wrapper will first check for the existance of the function before\n * actually calling it so that we don't have to take care of this check,\n * every time we want to access the DOM.\n *\n * Reason: DOM/querySelector is not available in all environments.\n *\n * We have to cast to any because utils can be consumed by a variety of environments,\n * and we don't want to break TS users. If you know what element will be selected by\n * `document.querySelector`, specify it as part of the generic call. For example,\n * `const element = getDomElement<Element>('selector');`\n *\n * @param selector the selector string passed on to document.querySelector\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function getDomElement<E = any>(selector: string): E | null {\n  if (WINDOW.document && WINDOW.document.querySelector) {\n    return WINDOW.document.querySelector(selector) as unknown as E;\n  }\n  return null;\n}\n", "import { GLOBAL_OBJ } from './worldwide';\n\n/** Prefix for logging strings */\nconst PREFIX = 'Sentry Logger ';\n\nexport const CONSOLE_LEVELS = ['debug', 'info', 'warn', 'error', 'log', 'assert', 'trace'] as const;\nexport type ConsoleLevel = (typeof CONSOLE_LEVELS)[number];\n\ntype LoggerMethod = (...args: unknown[]) => void;\ntype LoggerConsoleMethods = Record<ConsoleLevel, LoggerMethod>;\n\n/** This may be mutated by the console instrumentation. */\nexport const originalConsoleMethods: {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  [key in ConsoleLevel]?: (...args: any[]) => void;\n} = {};\n\n/** JSDoc */\ninterface Logger extends LoggerConsoleMethods {\n  disable(): void;\n  enable(): void;\n  isEnabled(): boolean;\n}\n\n/**\n * Temporarily disable sentry console instrumentations.\n *\n * @param callback The function to run against the original `console` messages\n * @returns The results of the callback\n */\nexport function consoleSandbox<T>(callback: () => T): T {\n  if (!('console' in GLOBAL_OBJ)) {\n    return callback();\n  }\n\n  const console = GLOBAL_OBJ.console as Console;\n  const wrappedFuncs: Partial<LoggerConsoleMethods> = {};\n\n  const wrappedLevels = Object.keys(originalConsoleMethods) as ConsoleLevel[];\n\n  // Restore all wrapped console methods\n  wrappedLevels.forEach(level => {\n    const originalConsoleMethod = originalConsoleMethods[level] as LoggerMethod;\n    wrappedFuncs[level] = console[level] as LoggerMethod | undefined;\n    console[level] = originalConsoleMethod;\n  });\n\n  try {\n    return callback();\n  } finally {\n    // Revert restoration to wrapped state\n    wrappedLevels.forEach(level => {\n      console[level] = wrappedFuncs[level] as LoggerMethod;\n    });\n  }\n}\n\nfunction makeLogger(): Logger {\n  let enabled = false;\n  const logger: Partial<Logger> = {\n    enable: () => {\n      enabled = true;\n    },\n    disable: () => {\n      enabled = false;\n    },\n    isEnabled: () => enabled,\n  };\n\n  if (__DEBUG_BUILD__) {\n    CONSOLE_LEVELS.forEach(name => {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      logger[name] = (...args: any[]) => {\n        if (enabled) {\n          consoleSandbox(() => {\n            GLOBAL_OBJ.console[name](`${PREFIX}[${name}]:`, ...args);\n          });\n        }\n      };\n    });\n  } else {\n    CONSOLE_LEVELS.forEach(name => {\n      logger[name] = () => undefined;\n    });\n  }\n\n  return logger as Logger;\n}\n\nexport const logger = makeLogger();\n", "import type { DsnC<PERSON><PERSON>, DsnLike, DsnProtocol } from '@sentry/types';\n\nimport { logger } from './logger';\n\n/** Regular expression used to parse a Dsn. */\nconst DSN_REGEX = /^(?:(\\w+):)\\/\\/(?:(\\w+)(?::(\\w+)?)?@)([\\w.-]+)(?::(\\d+))?\\/(.+)/;\n\nfunction isValidProtocol(protocol?: string): protocol is DsnProtocol {\n  return protocol === 'http' || protocol === 'https';\n}\n\n/**\n * Renders the string representation of this Dsn.\n *\n * By default, this will render the public representation without the password\n * component. To get the deprecated private representation, set `withPassword`\n * to true.\n *\n * @param withPassword When set to true, the password will be included.\n */\nexport function dsnToString(dsn: DsnComponents, withPassword: boolean = false): string {\n  const { host, path, pass, port, projectId, protocol, publicKey } = dsn;\n  return (\n    `${protocol}://${publicKey}${withPassword && pass ? `:${pass}` : ''}` +\n    `@${host}${port ? `:${port}` : ''}/${path ? `${path}/` : path}${projectId}`\n  );\n}\n\n/**\n * Parses a Dsn from a given string.\n *\n * @param str A Dsn as string\n * @returns Dsn as DsnComponents or undefined if @param str is not a valid DSN string\n */\nexport function dsnFromString(str: string): DsnComponents | undefined {\n  const match = DSN_REGEX.exec(str);\n\n  if (!match) {\n    // This should be logged to the console\n    // eslint-disable-next-line no-console\n    console.error(`Invalid Sentry Dsn: ${str}`);\n    return undefined;\n  }\n\n  const [protocol, publicKey, pass = '', host, port = '', lastPath] = match.slice(1);\n  let path = '';\n  let projectId = lastPath;\n\n  const split = projectId.split('/');\n  if (split.length > 1) {\n    path = split.slice(0, -1).join('/');\n    projectId = split.pop() as string;\n  }\n\n  if (projectId) {\n    const projectMatch = projectId.match(/^\\d+/);\n    if (projectMatch) {\n      projectId = projectMatch[0];\n    }\n  }\n\n  return dsnFromComponents({ host, pass, path, projectId, port, protocol: protocol as DsnProtocol, publicKey });\n}\n\nfunction dsnFromComponents(components: DsnComponents): DsnComponents {\n  return {\n    protocol: components.protocol,\n    publicKey: components.publicKey || '',\n    pass: components.pass || '',\n    host: components.host,\n    port: components.port || '',\n    path: components.path || '',\n    projectId: components.projectId,\n  };\n}\n\nfunction validateDsn(dsn: DsnComponents): boolean {\n  if (!__DEBUG_BUILD__) {\n    return true;\n  }\n\n  const { port, projectId, protocol } = dsn;\n\n  const requiredComponents: ReadonlyArray<keyof DsnComponents> = ['protocol', 'publicKey', 'host', 'projectId'];\n  const hasMissingRequiredComponent = requiredComponents.find(component => {\n    if (!dsn[component]) {\n      logger.error(`Invalid Sentry Dsn: ${component} missing`);\n      return true;\n    }\n    return false;\n  });\n\n  if (hasMissingRequiredComponent) {\n    return false;\n  }\n\n  if (!projectId.match(/^\\d+$/)) {\n    logger.error(`Invalid Sentry Dsn: Invalid projectId ${projectId}`);\n    return false;\n  }\n\n  if (!isValidProtocol(protocol)) {\n    logger.error(`Invalid Sentry Dsn: Invalid protocol ${protocol}`);\n    return false;\n  }\n\n  if (port && isNaN(parseInt(port, 10))) {\n    logger.error(`Invalid Sentry Dsn: Invalid port ${port}`);\n    return false;\n  }\n\n  return true;\n}\n\n/**\n * Creates a valid Sentry Dsn object, identifying a Sentry instance and project.\n * @returns a valid DsnComponents object or `undefined` if @param from is an invalid DSN source\n */\nexport function makeDsn(from: DsnLike): DsnComponents | undefined {\n  const components = typeof from === 'string' ? dsnFromString(from) : dsnFromComponents(from);\n  if (!components || !validateDsn(components)) {\n    return undefined;\n  }\n  return components;\n}\n", "import type { ConsoleLevel } from './logger';\n\n/** An error emitted by Sentry SDKs and related utilities. */\nexport class SentryError extends Error {\n  /** Display name of this error instance. */\n  public name: string;\n\n  public logLevel: ConsoleLevel;\n\n  public constructor(public message: string, logLevel: ConsoleLevel = 'warn') {\n    super(message);\n\n    this.name = new.target.prototype.constructor.name;\n    // This sets the prototype to be `Error`, not `SentryError`. It's unclear why we do this, but commenting this line\n    // out causes various (seemingly totally unrelated) playwright tests consistently time out. FYI, this makes\n    // instances of `SentryError` fail `obj instanceof SentryError` checks.\n    Object.setPrototypeOf(this, new.target.prototype);\n    this.logLevel = logLevel;\n  }\n}\n", "/* eslint-disable max-lines */\n/* eslint-disable @typescript-eslint/no-explicit-any */\nimport type { WrappedFunction } from '@sentry/types';\n\nimport { htmlTreeAsString } from './browser';\nimport { isElement, isError, isEvent, isInstanceOf, isPlainObject, isPrimitive } from './is';\nimport { logger } from './logger';\nimport { truncate } from './string';\n\n/**\n * Replace a method in an object with a wrapped version of itself.\n *\n * @param source An object that contains a method to be wrapped.\n * @param name The name of the method to be wrapped.\n * @param replacementFactory A higher-order function that takes the original version of the given method and returns a\n * wrapped version. Note: The function returned by `replacementFactory` needs to be a non-arrow function, in order to\n * preserve the correct value of `this`, and the original method must be called using `origMethod.call(this, <other\n * args>)` or `origMethod.apply(this, [<other args>])` (rather than being called directly), again to preserve `this`.\n * @returns void\n */\nexport function fill(source: { [key: string]: any }, name: string, replacementFactory: (...args: any[]) => any): void {\n  if (!(name in source)) {\n    return;\n  }\n\n  const original = source[name] as () => any;\n  const wrapped = replacementFactory(original) as WrappedFunction;\n\n  // Make sure it's a function first, as we need to attach an empty prototype for `defineProperties` to work\n  // otherwise it'll throw \"TypeError: Object.defineProperties called on non-object\"\n  if (typeof wrapped === 'function') {\n    markFunctionWrapped(wrapped, original);\n  }\n\n  source[name] = wrapped;\n}\n\n/**\n * Defines a non-enumerable property on the given object.\n *\n * @param obj The object on which to set the property\n * @param name The name of the property to be set\n * @param value The value to which to set the property\n */\nexport function addNonEnumerableProperty(obj: object, name: string, value: unknown): void {\n  try {\n    Object.defineProperty(obj, name, {\n      // enumerable: false, // the default, so we can save on bundle size by not explicitly setting it\n      value: value,\n      writable: true,\n      configurable: true,\n    });\n  } catch (o_O) {\n    __DEBUG_BUILD__ && logger.log(`Failed to add non-enumerable property \"${name}\" to object`, obj);\n  }\n}\n\n/**\n * Remembers the original function on the wrapped function and\n * patches up the prototype.\n *\n * @param wrapped the wrapper function\n * @param original the original function that gets wrapped\n */\nexport function markFunctionWrapped(wrapped: WrappedFunction, original: WrappedFunction): void {\n  try {\n    const proto = original.prototype || {};\n    wrapped.prototype = original.prototype = proto;\n    addNonEnumerableProperty(wrapped, '__sentry_original__', original);\n  } catch (o_O) {} // eslint-disable-line no-empty\n}\n\n/**\n * This extracts the original function if available.  See\n * `markFunctionWrapped` for more information.\n *\n * @param func the function to unwrap\n * @returns the unwrapped version of the function if available.\n */\nexport function getOriginalFunction(func: WrappedFunction): WrappedFunction | undefined {\n  return func.__sentry_original__;\n}\n\n/**\n * Encodes given object into url-friendly format\n *\n * @param object An object that contains serializable values\n * @returns string Encoded\n */\nexport function urlEncode(object: { [key: string]: any }): string {\n  return Object.keys(object)\n    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(object[key])}`)\n    .join('&');\n}\n\n/**\n * Transforms any `Error` or `Event` into a plain object with all of their enumerable properties, and some of their\n * non-enumerable properties attached.\n *\n * @param value Initial source that we have to transform in order for it to be usable by the serializer\n * @returns An Event or Error turned into an object - or the value argurment itself, when value is neither an Event nor\n *  an Error.\n */\nexport function convertToPlainObject<V>(value: V):\n  | {\n      [ownProps: string]: unknown;\n      type: string;\n      target: string;\n      currentTarget: string;\n      detail?: unknown;\n    }\n  | {\n      [ownProps: string]: unknown;\n      message: string;\n      name: string;\n      stack?: string;\n    }\n  | V {\n  if (isError(value)) {\n    return {\n      message: value.message,\n      name: value.name,\n      stack: value.stack,\n      ...getOwnProperties(value),\n    };\n  } else if (isEvent(value)) {\n    const newObj: {\n      [ownProps: string]: unknown;\n      type: string;\n      target: string;\n      currentTarget: string;\n      detail?: unknown;\n    } = {\n      type: value.type,\n      target: serializeEventTarget(value.target),\n      currentTarget: serializeEventTarget(value.currentTarget),\n      ...getOwnProperties(value),\n    };\n\n    if (typeof CustomEvent !== 'undefined' && isInstanceOf(value, CustomEvent)) {\n      newObj.detail = value.detail;\n    }\n\n    return newObj;\n  } else {\n    return value;\n  }\n}\n\n/** Creates a string representation of the target of an `Event` object */\nfunction serializeEventTarget(target: unknown): string {\n  try {\n    return isElement(target) ? htmlTreeAsString(target) : Object.prototype.toString.call(target);\n  } catch (_oO) {\n    return '<unknown>';\n  }\n}\n\n/** Filters out all but an object's own properties */\nfunction getOwnProperties(obj: unknown): { [key: string]: unknown } {\n  if (typeof obj === 'object' && obj !== null) {\n    const extractedProps: { [key: string]: unknown } = {};\n    for (const property in obj) {\n      if (Object.prototype.hasOwnProperty.call(obj, property)) {\n        extractedProps[property] = (obj as Record<string, unknown>)[property];\n      }\n    }\n    return extractedProps;\n  } else {\n    return {};\n  }\n}\n\n/**\n * Given any captured exception, extract its keys and create a sorted\n * and truncated list that will be used inside the event message.\n * eg. `Non-error exception captured with keys: foo, bar, baz`\n */\nexport function extractExceptionKeysForMessage(exception: Record<string, unknown>, maxLength: number = 40): string {\n  const keys = Object.keys(convertToPlainObject(exception));\n  keys.sort();\n\n  if (!keys.length) {\n    return '[object has no keys]';\n  }\n\n  if (keys[0].length >= maxLength) {\n    return truncate(keys[0], maxLength);\n  }\n\n  for (let includedKeys = keys.length; includedKeys > 0; includedKeys--) {\n    const serialized = keys.slice(0, includedKeys).join(', ');\n    if (serialized.length > maxLength) {\n      continue;\n    }\n    if (includedKeys === keys.length) {\n      return serialized;\n    }\n    return truncate(serialized, maxLength);\n  }\n\n  return '';\n}\n\n/**\n * Given any object, return a new object having removed all fields whose value was `undefined`.\n * Works recursively on objects and arrays.\n *\n * Attention: This function keeps circular references in the returned object.\n */\nexport function dropUndefinedKeys<T>(inputValue: T): T {\n  // This map keeps track of what already visited nodes map to.\n  // Our Set - based memoBuilder doesn't work here because we want to the output object to have the same circular\n  // references as the input object.\n  const memoizationMap = new Map<unknown, unknown>();\n\n  // This function just proxies `_dropUndefinedKeys` to keep the `memoBuilder` out of this function's API\n  return _dropUndefinedKeys(inputValue, memoizationMap);\n}\n\nfunction _dropUndefinedKeys<T>(inputValue: T, memoizationMap: Map<unknown, unknown>): T {\n  if (isPlainObject(inputValue)) {\n    // If this node has already been visited due to a circular reference, return the object it was mapped to in the new object\n    const memoVal = memoizationMap.get(inputValue);\n    if (memoVal !== undefined) {\n      return memoVal as T;\n    }\n\n    const returnValue: { [key: string]: any } = {};\n    // Store the mapping of this value in case we visit it again, in case of circular data\n    memoizationMap.set(inputValue, returnValue);\n\n    for (const key of Object.keys(inputValue)) {\n      if (typeof inputValue[key] !== 'undefined') {\n        returnValue[key] = _dropUndefinedKeys(inputValue[key], memoizationMap);\n      }\n    }\n\n    return returnValue as T;\n  }\n\n  if (Array.isArray(inputValue)) {\n    // If this node has already been visited due to a circular reference, return the array it was mapped to in the new object\n    const memoVal = memoizationMap.get(inputValue);\n    if (memoVal !== undefined) {\n      return memoVal as T;\n    }\n\n    const returnValue: unknown[] = [];\n    // Store the mapping of this value in case we visit it again, in case of circular data\n    memoizationMap.set(inputValue, returnValue);\n\n    inputValue.forEach((item: unknown) => {\n      returnValue.push(_dropUndefinedKeys(item, memoizationMap));\n    });\n\n    return returnValue as unknown as T;\n  }\n\n  return inputValue;\n}\n\n/**\n * Ensure that something is an object.\n *\n * Turns `undefined` and `null` into `String`s and all other primitives into instances of their respective wrapper\n * classes (String, Boolean, Number, etc.). Acts as the identity function on non-primitives.\n *\n * @param wat The subject of the objectification\n * @returns A version of `wat` which can safely be used with `Object` class methods\n */\nexport function objectify(wat: unknown): typeof Object {\n  let objectified;\n  switch (true) {\n    case wat === undefined || wat === null:\n      objectified = new String(wat);\n      break;\n\n    // Though symbols and bigints do have wrapper classes (`Symbol` and `BigInt`, respectively), for whatever reason\n    // those classes don't have constructors which can be used with the `new` keyword. We therefore need to cast each as\n    // an object in order to wrap it.\n    case typeof wat === 'symbol' || typeof wat === 'bigint':\n      objectified = Object(wat);\n      break;\n\n    // this will catch the remaining primitives: `String`, `Number`, and `Boolean`\n    case isPrimitive(wat):\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n      objectified = new (wat as any).constructor(wat);\n      break;\n\n    // by process of elimination, at this point we know that `wat` must already be an object\n    default:\n      objectified = wat;\n      break;\n  }\n  return objectified;\n}\n", "// This code was originally forked from https://github.com/felixge/node-stack-trace\n// Since then it has been highly modified to fit our needs.\n\n// Copyright (c) 2011 <PERSON> (<EMAIL>)//\n//\n//  Permission is hereby granted, free of charge, to any person obtaining a copy\n//  of this software and associated documentation files (the \"Software\"), to deal\n//  in the Software without restriction, including without limitation the rights\n//  to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n//  copies of the Software, and to permit persons to whom the Software is\n//  furnished to do so, subject to the following conditions://\n//\n//  The above copyright notice and this permission notice shall be included in\n//  all copies or substantial portions of the Software.//\n//\n//  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n//  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n//  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n//  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n//  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n//  OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n//  THE SOFTWARE.\n\nimport type { StackLineParserFn } from '@sentry/types';\n\nexport type GetModuleFn = (filename: string | undefined) => string | undefined;\n\n/**\n * Does this filename look like it's part of the app code?\n */\nexport function filenameIsInApp(filename: string, isNative: boolean = false): boolean {\n  const isInternal =\n    isNative ||\n    (filename &&\n      // It's not internal if it's an absolute linux path\n      !filename.startsWith('/') &&\n      // It's not internal if it's an absolute windows path\n      !filename.includes(':\\\\') &&\n      // It's not internal if the path is starting with a dot\n      !filename.startsWith('.') &&\n      // It's not internal if the frame has a protocol. In node, this is usually the case if the file got pre-processed with a bundler like webpack\n      !filename.match(/^[a-zA-Z]([a-zA-Z0-9.\\-+])*:\\/\\//)); // Schema from: https://stackoverflow.com/a/3641782\n\n  // in_app is all that's not an internal Node function or a module within node_modules\n  // note that isNative appears to return true even for node core libraries\n  // see https://github.com/getsentry/raven-node/issues/176\n\n  return !isInternal && filename !== undefined && !filename.includes('node_modules/');\n}\n\n/** Node Stack line parser */\n// eslint-disable-next-line complexity\nexport function node(getModule?: GetModuleFn): StackLineParserFn {\n  const FILENAME_MATCH = /^\\s*[-]{4,}$/;\n  const FULL_MATCH = /at (?:async )?(?:(.+?)\\s+\\()?(?:(.+):(\\d+):(\\d+)?|([^)]+))\\)?/;\n\n  // eslint-disable-next-line complexity\n  return (line: string) => {\n    const lineMatch = line.match(FULL_MATCH);\n\n    if (lineMatch) {\n      let object: string | undefined;\n      let method: string | undefined;\n      let functionName: string | undefined;\n      let typeName: string | undefined;\n      let methodName: string | undefined;\n\n      if (lineMatch[1]) {\n        functionName = lineMatch[1];\n\n        let methodStart = functionName.lastIndexOf('.');\n        if (functionName[methodStart - 1] === '.') {\n          methodStart--;\n        }\n\n        if (methodStart > 0) {\n          object = functionName.slice(0, methodStart);\n          method = functionName.slice(methodStart + 1);\n          const objectEnd = object.indexOf('.Module');\n          if (objectEnd > 0) {\n            functionName = functionName.slice(objectEnd + 1);\n            object = object.slice(0, objectEnd);\n          }\n        }\n        typeName = undefined;\n      }\n\n      if (method) {\n        typeName = object;\n        methodName = method;\n      }\n\n      if (method === '<anonymous>') {\n        methodName = undefined;\n        functionName = undefined;\n      }\n\n      if (functionName === undefined) {\n        methodName = methodName || '<anonymous>';\n        functionName = typeName ? `${typeName}.${methodName}` : methodName;\n      }\n\n      let filename = lineMatch[2] && lineMatch[2].startsWith('file://') ? lineMatch[2].slice(7) : lineMatch[2];\n      const isNative = lineMatch[5] === 'native';\n\n      if (!filename && lineMatch[5] && !isNative) {\n        filename = lineMatch[5];\n      }\n\n      return {\n        filename,\n        module: getModule ? getModule(filename) : undefined,\n        function: functionName,\n        lineno: parseInt(lineMatch[3], 10) || undefined,\n        colno: parseInt(lineMatch[4], 10) || undefined,\n        in_app: filenameIsInApp(filename, isNative),\n      };\n    }\n\n    if (line.match(FILENAME_MATCH)) {\n      return {\n        filename: line,\n      };\n    }\n\n    return undefined;\n  };\n}\n", "import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ack<PERSON>ars<PERSON> } from '@sentry/types';\n\nimport type { GetModuleFn } from './node-stack-trace';\nimport { filenameIsInApp, node } from './node-stack-trace';\n\nexport { filenameIsInApp };\n\nconst STACKTRACE_FRAME_LIMIT = 50;\n// Used to sanitize webpack (error: *) wrapped stack errors\nconst WEBPACK_ERROR_REGEXP = /\\(error: (.*)\\)/;\nconst STRIP_FRAME_REGEXP = /captureMessage|captureException/;\n\n/**\n * Creates a stack parser with the supplied line parsers\n *\n * StackFrames are returned in the correct order for Sentry Exception\n * frames and with Sentry SDK internal frames removed from the top and bottom\n *\n */\nexport function createStackParser(...parsers: StackLineParser[]): StackParser {\n  const sortedParsers = parsers.sort((a, b) => a[0] - b[0]).map(p => p[1]);\n\n  return (stack: string, skipFirst: number = 0): StackFrame[] => {\n    const frames: StackFrame[] = [];\n    const lines = stack.split('\\n');\n\n    for (let i = skipFirst; i < lines.length; i++) {\n      const line = lines[i];\n      // Ignore lines over 1kb as they are unlikely to be stack frames.\n      // Many of the regular expressions use backtracking which results in run time that increases exponentially with\n      // input size. Huge strings can result in hangs/Denial of Service:\n      // https://github.com/getsentry/sentry-javascript/issues/2286\n      if (line.length > 1024) {\n        continue;\n      }\n\n      // https://github.com/getsentry/sentry-javascript/issues/5459\n      // Remove webpack (error: *) wrappers\n      const cleanedLine = WEBPACK_ERROR_REGEXP.test(line) ? line.replace(WEBPACK_ERROR_REGEXP, '$1') : line;\n\n      // https://github.com/getsentry/sentry-javascript/issues/7813\n      // Skip Error: lines\n      if (cleanedLine.match(/\\S*Error: /)) {\n        continue;\n      }\n\n      for (const parser of sortedParsers) {\n        const frame = parser(cleanedLine);\n\n        if (frame) {\n          frames.push(frame);\n          break;\n        }\n      }\n\n      if (frames.length >= STACKTRACE_FRAME_LIMIT) {\n        break;\n      }\n    }\n\n    return stripSentryFramesAndReverse(frames);\n  };\n}\n\n/**\n * Gets a stack parser implementation from Options.stackParser\n * @see Options\n *\n * If options contains an array of line parsers, it is converted into a parser\n */\nexport function stackParserFromStackParserOptions(stackParser: StackParser | StackLineParser[]): StackParser {\n  if (Array.isArray(stackParser)) {\n    return createStackParser(...stackParser);\n  }\n  return stackParser;\n}\n\n/**\n * Removes Sentry frames from the top and bottom of the stack if present and enforces a limit of max number of frames.\n * Assumes stack input is ordered from top to bottom and returns the reverse representation so call site of the\n * function that caused the crash is the last frame in the array.\n * @hidden\n */\nexport function stripSentryFramesAndReverse(stack: ReadonlyArray<StackFrame>): StackFrame[] {\n  if (!stack.length) {\n    return [];\n  }\n\n  const localStack = Array.from(stack);\n\n  // If stack starts with one of our API calls, remove it (starts, meaning it's the top of the stack - aka last call)\n  if (/sentryWrapped/.test(localStack[localStack.length - 1].function || '')) {\n    localStack.pop();\n  }\n\n  // Reversing in the middle of the procedure allows us to just pop the values off the stack\n  localStack.reverse();\n\n  // If stack ends with one of our internal API calls, remove it (ends, meaning it's the bottom of the stack - aka top-most call)\n  if (STRIP_FRAME_REGEXP.test(localStack[localStack.length - 1].function || '')) {\n    localStack.pop();\n\n    // When using synthetic events, we will have a 2 levels deep stack, as `new Error('Sentry syntheticException')`\n    // is produced within the hub itself, making it:\n    //\n    //   Sentry.captureException()\n    //   getCurrentHub().captureException()\n    //\n    // instead of just the top `Sentry` call itself.\n    // This forces us to possibly strip an additional frame in the exact same was as above.\n    if (STRIP_FRAME_REGEXP.test(localStack[localStack.length - 1].function || '')) {\n      localStack.pop();\n    }\n  }\n\n  return localStack.slice(0, STACKTRACE_FRAME_LIMIT).map(frame => ({\n    ...frame,\n    filename: frame.filename || localStack[localStack.length - 1].filename,\n    function: frame.function || '?',\n  }));\n}\n\nconst defaultFunctionName = '<anonymous>';\n\n/**\n * Safely extract function name from itself\n */\nexport function getFunctionName(fn: unknown): string {\n  try {\n    if (!fn || typeof fn !== 'function') {\n      return defaultFunctionName;\n    }\n    return fn.name || defaultFunctionName;\n  } catch (e) {\n    // Just accessing custom props in some Selenium environments\n    // can cause a \"Permission denied\" exception (see raven-js#495).\n    return defaultFunctionName;\n  }\n}\n\n/**\n * Node.js stack line parser\n *\n * This is in @sentry/utils so it can be used from the Electron SDK in the browser for when `nodeIntegration == true`.\n * This allows it to be used without referencing or importing any node specific code which causes bundlers to complain\n */\nexport function nodeStackLineParser(getModule?: GetModuleFn): StackLineParser {\n  return [90, node(getModule)];\n}\n", "import { logger } from './logger';\nimport { getGlobalObject } from './worldwide';\n\n// eslint-disable-next-line deprecation/deprecation\nconst WINDOW = getGlobalObject<Window>();\n\nexport { supportsHistory } from './vendor/supportsHistory';\n\n/**\n * Tells whether current environment supports ErrorEvent objects\n * {@link supportsErrorEvent}.\n *\n * @returns Answer to the given question.\n */\nexport function supportsErrorEvent(): boolean {\n  try {\n    new ErrorEvent('');\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * Tells whether current environment supports DOMError objects\n * {@link supportsDOMError}.\n *\n * @returns Answer to the given question.\n */\nexport function supportsDOMError(): boolean {\n  try {\n    // Chrome: VM89:1 Uncaught TypeError: Failed to construct 'DOMError':\n    // 1 argument required, but only 0 present.\n    // @ts-expect-error It really needs 1 argument, not 0.\n    new DOMError('');\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * Tells whether current environment supports DOMException objects\n * {@link supportsDOMException}.\n *\n * @returns Answer to the given question.\n */\nexport function supportsDOMException(): boolean {\n  try {\n    new DOMException('');\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * Tells whether current environment supports Fetch API\n * {@link supportsFetch}.\n *\n * @returns Answer to the given question.\n */\nexport function supportsFetch(): boolean {\n  if (!('fetch' in WINDOW)) {\n    return false;\n  }\n\n  try {\n    new Headers();\n    new Request('http://www.example.com');\n    new Response();\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n/**\n * isNativeFetch checks if the given function is a native implementation of fetch()\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function isNativeFetch(func: Function): boolean {\n  return func && /^function fetch\\(\\)\\s+\\{\\s+\\[native code\\]\\s+\\}$/.test(func.toString());\n}\n\n/**\n * Tells whether current environment supports Fetch API natively\n * {@link supportsNativeFetch}.\n *\n * @returns true if `window.fetch` is natively implemented, false otherwise\n */\nexport function supportsNativeFetch(): boolean {\n  if (!supportsFetch()) {\n    return false;\n  }\n\n  // Fast path to avoid DOM I/O\n  // eslint-disable-next-line @typescript-eslint/unbound-method\n  if (isNativeFetch(WINDOW.fetch)) {\n    return true;\n  }\n\n  // window.fetch is implemented, but is polyfilled or already wrapped (e.g: by a chrome extension)\n  // so create a \"pure\" iframe to see if that has native fetch\n  let result = false;\n  const doc = WINDOW.document;\n  // eslint-disable-next-line deprecation/deprecation\n  if (doc && typeof (doc.createElement as unknown) === 'function') {\n    try {\n      const sandbox = doc.createElement('iframe');\n      sandbox.hidden = true;\n      doc.head.appendChild(sandbox);\n      if (sandbox.contentWindow && sandbox.contentWindow.fetch) {\n        // eslint-disable-next-line @typescript-eslint/unbound-method\n        result = isNativeFetch(sandbox.contentWindow.fetch);\n      }\n      doc.head.removeChild(sandbox);\n    } catch (err) {\n      __DEBUG_BUILD__ &&\n        logger.warn('Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ', err);\n    }\n  }\n\n  return result;\n}\n\n/**\n * Tells whether current environment supports ReportingObserver API\n * {@link supportsReportingObserver}.\n *\n * @returns Answer to the given question.\n */\nexport function supportsReportingObserver(): boolean {\n  return 'ReportingObserver' in WINDOW;\n}\n\n/**\n * Tells whether current environment supports Referrer Policy API\n * {@link supportsReferrerPolicy}.\n *\n * @returns Answer to the given question.\n */\nexport function supportsReferrerPolicy(): boolean {\n  // Despite all stars in the sky saying that Edge supports old draft syntax, aka 'never', 'always', 'origin' and 'default'\n  // (see https://caniuse.com/#feat=referrer-policy),\n  // it doesn't. And it throws an exception instead of ignoring this parameter...\n  // REF: https://github.com/getsentry/raven-js/issues/1233\n\n  if (!supportsFetch()) {\n    return false;\n  }\n\n  try {\n    new Request('_', {\n      referrerPolicy: 'origin' as ReferrerPolicy,\n    });\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n", "// Based on https://github.com/angular/angular.js/pull/13945/files\n// The MIT License\n\n// Copyright (c) 2010-2016 Google, Inc. http://angularjs.org\n\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\nimport { getGlobalObject } from '../worldwide';\n\n// eslint-disable-next-line deprecation/deprecation\nconst WINDOW = getGlobalObject<Window>();\n\n/**\n * Tells whether current environment supports History API\n * {@link supportsHistory}.\n *\n * @returns Answer to the given question.\n */\nexport function supportsHistory(): boolean {\n  // NOTE: in Chrome App environment, touching history.pushState, *even inside\n  //       a try/catch block*, will cause Chrome to output an error to console.error\n  // borrowed from: https://github.com/angular/angular.js/pull/13945/files\n  /* eslint-disable @typescript-eslint/no-unsafe-member-access */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  const chrome = (WINDOW as any).chrome;\n  const isChromePackagedApp = chrome && chrome.app && chrome.app.runtime;\n  /* eslint-enable @typescript-eslint/no-unsafe-member-access */\n  const hasHistoryApi = 'history' in WINDOW && !!WINDOW.history.pushState && !!WINDOW.history.replaceState;\n\n  return !isChromePackagedApp && hasHistoryApi;\n}\n", "/* eslint-disable max-lines */\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable @typescript-eslint/ban-types */\nimport type {\n  HandlerDataFetch,\n  HandlerDataXhr,\n  SentryWrappedXMLHttpRequest,\n  SentryXhrData,\n  WrappedFunction,\n} from '@sentry/types';\n\nimport { isString } from './is';\nimport type { ConsoleLevel } from './logger';\nimport { CONSOLE_LEVELS, logger, originalConsoleMethods } from './logger';\nimport { addNonEnumerableProperty, fill } from './object';\nimport { getFunctionName } from './stacktrace';\nimport { supportsHistory, supportsNativeFetch } from './supports';\nimport { getGlobalObject, GLOBAL_OBJ } from './worldwide';\n\n// eslint-disable-next-line deprecation/deprecation\nconst WINDOW = getGlobalObject<Window>();\n\nexport const SENTRY_XHR_DATA_KEY = '__sentry_xhr_v2__';\n\nexport type InstrumentHandlerType =\n  | 'console'\n  | 'dom'\n  | 'fetch'\n  | 'history'\n  | 'sentry'\n  | 'xhr'\n  | 'error'\n  | 'unhandledrejection';\nexport type InstrumentHandlerCallback = (data: any) => void;\n\n/**\n * Instrument native APIs to call handlers that can be used to create breadcrumbs, APM spans etc.\n *  - Console API\n *  - Fetch API\n *  - XHR API\n *  - History API\n *  - DOM API (click/typing)\n *  - Error API\n *  - UnhandledRejection API\n */\n\nconst handlers: { [key in InstrumentHandlerType]?: InstrumentHandlerCallback[] } = {};\nconst instrumented: { [key in InstrumentHandlerType]?: boolean } = {};\n\n/** Instruments given API */\nfunction instrument(type: InstrumentHandlerType): void {\n  if (instrumented[type]) {\n    return;\n  }\n\n  instrumented[type] = true;\n\n  switch (type) {\n    case 'console':\n      instrumentConsole();\n      break;\n    case 'dom':\n      instrumentDOM();\n      break;\n    case 'xhr':\n      instrumentXHR();\n      break;\n    case 'fetch':\n      instrumentFetch();\n      break;\n    case 'history':\n      instrumentHistory();\n      break;\n    case 'error':\n      instrumentError();\n      break;\n    case 'unhandledrejection':\n      instrumentUnhandledRejection();\n      break;\n    default:\n      __DEBUG_BUILD__ && logger.warn('unknown instrumentation type:', type);\n      return;\n  }\n}\n\n/**\n * Add handler that will be called when given type of instrumentation triggers.\n * Use at your own risk, this might break without changelog notice, only used internally.\n * @hidden\n */\nexport function addInstrumentationHandler(type: InstrumentHandlerType, callback: InstrumentHandlerCallback): void {\n  handlers[type] = handlers[type] || [];\n  (handlers[type] as InstrumentHandlerCallback[]).push(callback);\n  instrument(type);\n}\n\n/**\n * Reset all instrumentation handlers.\n * This can be used by tests to ensure we have a clean slate of instrumentation handlers.\n */\nexport function resetInstrumentationHandlers(): void {\n  Object.keys(handlers).forEach(key => {\n    handlers[key as InstrumentHandlerType] = undefined;\n  });\n}\n\n/** JSDoc */\nfunction triggerHandlers(type: InstrumentHandlerType, data: any): void {\n  if (!type || !handlers[type]) {\n    return;\n  }\n\n  for (const handler of handlers[type] || []) {\n    try {\n      handler(data);\n    } catch (e) {\n      __DEBUG_BUILD__ &&\n        logger.error(\n          `Error while triggering instrumentation handler.\\nType: ${type}\\nName: ${getFunctionName(handler)}\\nError:`,\n          e,\n        );\n    }\n  }\n}\n\n/** JSDoc */\nfunction instrumentConsole(): void {\n  if (!('console' in GLOBAL_OBJ)) {\n    return;\n  }\n\n  CONSOLE_LEVELS.forEach(function (level: ConsoleLevel): void {\n    if (!(level in GLOBAL_OBJ.console)) {\n      return;\n    }\n\n    fill(GLOBAL_OBJ.console, level, function (originalConsoleMethod: () => any): Function {\n      originalConsoleMethods[level] = originalConsoleMethod;\n\n      return function (...args: any[]): void {\n        triggerHandlers('console', { args, level });\n\n        const log = originalConsoleMethods[level];\n        log && log.apply(GLOBAL_OBJ.console, args);\n      };\n    });\n  });\n}\n\n/** JSDoc */\nfunction instrumentFetch(): void {\n  if (!supportsNativeFetch()) {\n    return;\n  }\n\n  fill(GLOBAL_OBJ, 'fetch', function (originalFetch: () => void): () => void {\n    return function (...args: any[]): void {\n      const { method, url } = parseFetchArgs(args);\n\n      const handlerData: HandlerDataFetch = {\n        args,\n        fetchData: {\n          method,\n          url,\n        },\n        startTimestamp: Date.now(),\n      };\n\n      triggerHandlers('fetch', {\n        ...handlerData,\n      });\n\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n      return originalFetch.apply(GLOBAL_OBJ, args).then(\n        (response: Response) => {\n          triggerHandlers('fetch', {\n            ...handlerData,\n            endTimestamp: Date.now(),\n            response,\n          });\n          return response;\n        },\n        (error: Error) => {\n          triggerHandlers('fetch', {\n            ...handlerData,\n            endTimestamp: Date.now(),\n            error,\n          });\n          // NOTE: If you are a Sentry user, and you are seeing this stack frame,\n          //       it means the sentry.javascript SDK caught an error invoking your application code.\n          //       This is expected behavior and NOT indicative of a bug with sentry.javascript.\n          throw error;\n        },\n      );\n    };\n  });\n}\n\nfunction hasProp<T extends string>(obj: unknown, prop: T): obj is Record<string, string> {\n  return !!obj && typeof obj === 'object' && !!(obj as Record<string, string>)[prop];\n}\n\ntype FetchResource = string | { toString(): string } | { url: string };\n\nfunction getUrlFromResource(resource: FetchResource): string {\n  if (typeof resource === 'string') {\n    return resource;\n  }\n\n  if (!resource) {\n    return '';\n  }\n\n  if (hasProp(resource, 'url')) {\n    return resource.url;\n  }\n\n  if (resource.toString) {\n    return resource.toString();\n  }\n\n  return '';\n}\n\n/**\n * Parses the fetch arguments to find the used Http method and the url of the request\n */\nexport function parseFetchArgs(fetchArgs: unknown[]): { method: string; url: string } {\n  if (fetchArgs.length === 0) {\n    return { method: 'GET', url: '' };\n  }\n\n  if (fetchArgs.length === 2) {\n    const [url, options] = fetchArgs as [FetchResource, object];\n\n    return {\n      url: getUrlFromResource(url),\n      method: hasProp(options, 'method') ? String(options.method).toUpperCase() : 'GET',\n    };\n  }\n\n  const arg = fetchArgs[0];\n  return {\n    url: getUrlFromResource(arg as FetchResource),\n    method: hasProp(arg, 'method') ? String(arg.method).toUpperCase() : 'GET',\n  };\n}\n\n/** JSDoc */\nexport function instrumentXHR(): void {\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n  if (!(WINDOW as any).XMLHttpRequest) {\n    return;\n  }\n\n  const xhrproto = XMLHttpRequest.prototype;\n\n  fill(xhrproto, 'open', function (originalOpen: () => void): () => void {\n    return function (this: XMLHttpRequest & SentryWrappedXMLHttpRequest, ...args: any[]): void {\n      const startTimestamp = Date.now();\n\n      const url = args[1];\n      const xhrInfo: SentryXhrData = (this[SENTRY_XHR_DATA_KEY] = {\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n        method: isString(args[0]) ? args[0].toUpperCase() : args[0],\n        url: args[1],\n        request_headers: {},\n      });\n\n      // if Sentry key appears in URL, don't capture it as a request\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n      if (isString(url) && xhrInfo.method === 'POST' && url.match(/sentry_key/)) {\n        this.__sentry_own_request__ = true;\n      }\n\n      const onreadystatechangeHandler: () => void = () => {\n        // For whatever reason, this is not the same instance here as from the outer method\n        const xhrInfo = this[SENTRY_XHR_DATA_KEY];\n\n        if (!xhrInfo) {\n          return;\n        }\n\n        if (this.readyState === 4) {\n          try {\n            // touching statusCode in some platforms throws\n            // an exception\n            xhrInfo.status_code = this.status;\n          } catch (e) {\n            /* do nothing */\n          }\n\n          triggerHandlers('xhr', {\n            args: args as [string, string],\n            endTimestamp: Date.now(),\n            startTimestamp,\n            xhr: this,\n          } as HandlerDataXhr);\n        }\n      };\n\n      if ('onreadystatechange' in this && typeof this.onreadystatechange === 'function') {\n        fill(this, 'onreadystatechange', function (original: WrappedFunction): Function {\n          return function (this: SentryWrappedXMLHttpRequest, ...readyStateArgs: any[]): void {\n            onreadystatechangeHandler();\n            return original.apply(this, readyStateArgs);\n          };\n        });\n      } else {\n        this.addEventListener('readystatechange', onreadystatechangeHandler);\n      }\n\n      // Intercepting `setRequestHeader` to access the request headers of XHR instance.\n      // This will only work for user/library defined headers, not for the default/browser-assigned headers.\n      // Request cookies are also unavailable for XHR, as `Cookie` header can't be defined by `setRequestHeader`.\n      fill(this, 'setRequestHeader', function (original: WrappedFunction): Function {\n        return function (this: SentryWrappedXMLHttpRequest, ...setRequestHeaderArgs: unknown[]): void {\n          const [header, value] = setRequestHeaderArgs as [string, string];\n\n          const xhrInfo = this[SENTRY_XHR_DATA_KEY];\n\n          if (xhrInfo) {\n            xhrInfo.request_headers[header.toLowerCase()] = value;\n          }\n\n          return original.apply(this, setRequestHeaderArgs);\n        };\n      });\n\n      return originalOpen.apply(this, args);\n    };\n  });\n\n  fill(xhrproto, 'send', function (originalSend: () => void): () => void {\n    return function (this: XMLHttpRequest & SentryWrappedXMLHttpRequest, ...args: any[]): void {\n      const sentryXhrData = this[SENTRY_XHR_DATA_KEY];\n      if (sentryXhrData && args[0] !== undefined) {\n        sentryXhrData.body = args[0];\n      }\n\n      triggerHandlers('xhr', {\n        args,\n        startTimestamp: Date.now(),\n        xhr: this,\n      });\n\n      return originalSend.apply(this, args);\n    };\n  });\n}\n\nlet lastHref: string;\n\n/** JSDoc */\nfunction instrumentHistory(): void {\n  if (!supportsHistory()) {\n    return;\n  }\n\n  const oldOnPopState = WINDOW.onpopstate;\n  WINDOW.onpopstate = function (this: WindowEventHandlers, ...args: any[]): any {\n    const to = WINDOW.location.href;\n    // keep track of the current URL state, as we always receive only the updated state\n    const from = lastHref;\n    lastHref = to;\n    triggerHandlers('history', {\n      from,\n      to,\n    });\n    if (oldOnPopState) {\n      // Apparently this can throw in Firefox when incorrectly implemented plugin is installed.\n      // https://github.com/getsentry/sentry-javascript/issues/3344\n      // https://github.com/bugsnag/bugsnag-js/issues/469\n      try {\n        return oldOnPopState.apply(this, args);\n      } catch (_oO) {\n        // no-empty\n      }\n    }\n  };\n\n  /** @hidden */\n  function historyReplacementFunction(originalHistoryFunction: () => void): () => void {\n    return function (this: History, ...args: any[]): void {\n      const url = args.length > 2 ? args[2] : undefined;\n      if (url) {\n        // coerce to string (this is what pushState does)\n        const from = lastHref;\n        const to = String(url);\n        // keep track of the current URL state, as we always receive only the updated state\n        lastHref = to;\n        triggerHandlers('history', {\n          from,\n          to,\n        });\n      }\n      return originalHistoryFunction.apply(this, args);\n    };\n  }\n\n  fill(WINDOW.history, 'pushState', historyReplacementFunction);\n  fill(WINDOW.history, 'replaceState', historyReplacementFunction);\n}\n\nconst DEBOUNCE_DURATION = 1000;\nlet debounceTimerID: number | undefined;\nlet lastCapturedEvent: Event | undefined;\n\n/**\n * Check whether two DOM events are similar to eachother. For example, two click events on the same button.\n */\nfunction areSimilarDomEvents(a: Event, b: Event): boolean {\n  // If both events have different type, then user definitely performed two separate actions. e.g. click + keypress.\n  if (a.type !== b.type) {\n    return false;\n  }\n\n  try {\n    // If both events have the same type, it's still possible that actions were performed on different targets.\n    // e.g. 2 clicks on different buttons.\n    if (a.target !== b.target) {\n      return false;\n    }\n  } catch (e) {\n    // just accessing `target` property can throw an exception in some rare circumstances\n    // see: https://github.com/getsentry/sentry-javascript/issues/838\n  }\n\n  // If both events have the same type _and_ same `target` (an element which triggered an event, _not necessarily_\n  // to which an event listener was attached), we treat them as the same action, as we want to capture\n  // only one breadcrumb. e.g. multiple clicks on the same button, or typing inside a user input box.\n  return true;\n}\n\n/**\n * Decide whether an event should be captured.\n * @param event event to be captured\n */\nfunction shouldSkipDOMEvent(event: Event): boolean {\n  // We are only interested in filtering `keypress` events for now.\n  if (event.type !== 'keypress') {\n    return false;\n  }\n\n  try {\n    const target = event.target as HTMLElement;\n\n    if (!target || !target.tagName) {\n      return true;\n    }\n\n    // Only consider keypress events on actual input elements. This will disregard keypresses targeting body\n    // e.g.tabbing through elements, hotkeys, etc.\n    if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable) {\n      return false;\n    }\n  } catch (e) {\n    // just accessing `target` property can throw an exception in some rare circumstances\n    // see: https://github.com/getsentry/sentry-javascript/issues/838\n  }\n\n  return true;\n}\n\n/**\n * Wraps addEventListener to capture UI breadcrumbs\n * @param handler function that will be triggered\n * @param globalListener indicates whether event was captured by the global event listener\n * @returns wrapped breadcrumb events handler\n * @hidden\n */\nfunction makeDOMEventHandler(handler: Function, globalListener: boolean = false): (event: Event) => void {\n  return (event: Event & { _sentryCaptured?: true }): void => {\n    // It's possible this handler might trigger multiple times for the same\n    // event (e.g. event propagation through node ancestors).\n    // Ignore if we've already captured that event.\n    if (!event || event['_sentryCaptured']) {\n      return;\n    }\n\n    // We always want to skip _some_ events.\n    if (shouldSkipDOMEvent(event)) {\n      return;\n    }\n\n    // Mark event as \"seen\"\n    addNonEnumerableProperty(event, '_sentryCaptured', true);\n\n    const name = event.type === 'keypress' ? 'input' : event.type;\n\n    // If there is no last captured event, it means that we can safely capture the new event and store it for future comparisons.\n    // If there is a last captured event, see if the new event is different enough to treat it as a unique one.\n    // If that's the case, emit the previous event and store locally the newly-captured DOM event.\n    if (lastCapturedEvent === undefined || !areSimilarDomEvents(lastCapturedEvent, event)) {\n      handler({\n        event: event,\n        name,\n        global: globalListener,\n      });\n      lastCapturedEvent = event;\n    }\n\n    // Start a new debounce timer that will prevent us from capturing multiple events that should be grouped together.\n    clearTimeout(debounceTimerID);\n    debounceTimerID = WINDOW.setTimeout(() => {\n      lastCapturedEvent = undefined;\n    }, DEBOUNCE_DURATION);\n  };\n}\n\ntype AddEventListener = (\n  type: string,\n  listener: EventListenerOrEventListenerObject,\n  options?: boolean | AddEventListenerOptions,\n) => void;\ntype RemoveEventListener = (\n  type: string,\n  listener: EventListenerOrEventListenerObject,\n  options?: boolean | EventListenerOptions,\n) => void;\n\ntype InstrumentedElement = Element & {\n  __sentry_instrumentation_handlers__?: {\n    [key in 'click' | 'keypress']?: {\n      handler?: Function;\n      /** The number of custom listeners attached to this element */\n      refCount: number;\n    };\n  };\n};\n\n/** JSDoc */\nexport function instrumentDOM(): void {\n  if (!WINDOW.document) {\n    return;\n  }\n\n  // Make it so that any click or keypress that is unhandled / bubbled up all the way to the document triggers our dom\n  // handlers. (Normally we have only one, which captures a breadcrumb for each click or keypress.) Do this before\n  // we instrument `addEventListener` so that we don't end up attaching this handler twice.\n  const triggerDOMHandler = triggerHandlers.bind(null, 'dom');\n  const globalDOMEventHandler = makeDOMEventHandler(triggerDOMHandler, true);\n  WINDOW.document.addEventListener('click', globalDOMEventHandler, false);\n  WINDOW.document.addEventListener('keypress', globalDOMEventHandler, false);\n\n  // After hooking into click and keypress events bubbled up to `document`, we also hook into user-handled\n  // clicks & keypresses, by adding an event listener of our own to any element to which they add a listener. That\n  // way, whenever one of their handlers is triggered, ours will be, too. (This is needed because their handler\n  // could potentially prevent the event from bubbling up to our global listeners. This way, our handler are still\n  // guaranteed to fire at least once.)\n  ['EventTarget', 'Node'].forEach((target: string) => {\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n    const proto = (WINDOW as any)[target] && (WINDOW as any)[target].prototype;\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, no-prototype-builtins\n    if (!proto || !proto.hasOwnProperty || !proto.hasOwnProperty('addEventListener')) {\n      return;\n    }\n\n    fill(proto, 'addEventListener', function (originalAddEventListener: AddEventListener): AddEventListener {\n      return function (\n        this: Element,\n        type: string,\n        listener: EventListenerOrEventListenerObject,\n        options?: boolean | AddEventListenerOptions,\n      ): AddEventListener {\n        if (type === 'click' || type == 'keypress') {\n          try {\n            const el = this as InstrumentedElement;\n            const handlers = (el.__sentry_instrumentation_handlers__ = el.__sentry_instrumentation_handlers__ || {});\n            const handlerForType = (handlers[type] = handlers[type] || { refCount: 0 });\n\n            if (!handlerForType.handler) {\n              const handler = makeDOMEventHandler(triggerDOMHandler);\n              handlerForType.handler = handler;\n              originalAddEventListener.call(this, type, handler, options);\n            }\n\n            handlerForType.refCount++;\n          } catch (e) {\n            // Accessing dom properties is always fragile.\n            // Also allows us to skip `addEventListenrs` calls with no proper `this` context.\n          }\n        }\n\n        return originalAddEventListener.call(this, type, listener, options);\n      };\n    });\n\n    fill(\n      proto,\n      'removeEventListener',\n      function (originalRemoveEventListener: RemoveEventListener): RemoveEventListener {\n        return function (\n          this: Element,\n          type: string,\n          listener: EventListenerOrEventListenerObject,\n          options?: boolean | EventListenerOptions,\n        ): () => void {\n          if (type === 'click' || type == 'keypress') {\n            try {\n              const el = this as InstrumentedElement;\n              const handlers = el.__sentry_instrumentation_handlers__ || {};\n              const handlerForType = handlers[type];\n\n              if (handlerForType) {\n                handlerForType.refCount--;\n                // If there are no longer any custom handlers of the current type on this element, we can remove ours, too.\n                if (handlerForType.refCount <= 0) {\n                  originalRemoveEventListener.call(this, type, handlerForType.handler, options);\n                  handlerForType.handler = undefined;\n                  delete handlers[type]; // eslint-disable-line @typescript-eslint/no-dynamic-delete\n                }\n\n                // If there are no longer any custom handlers of any type on this element, cleanup everything.\n                if (Object.keys(handlers).length === 0) {\n                  delete el.__sentry_instrumentation_handlers__;\n                }\n              }\n            } catch (e) {\n              // Accessing dom properties is always fragile.\n              // Also allows us to skip `addEventListenrs` calls with no proper `this` context.\n            }\n          }\n\n          return originalRemoveEventListener.call(this, type, listener, options);\n        };\n      },\n    );\n  });\n}\n\nlet _oldOnErrorHandler: (typeof WINDOW)['onerror'] | null = null;\n/** JSDoc */\nfunction instrumentError(): void {\n  _oldOnErrorHandler = WINDOW.onerror;\n\n  WINDOW.onerror = function (msg: unknown, url: unknown, line: unknown, column: unknown, error: unknown): boolean {\n    triggerHandlers('error', {\n      column,\n      error,\n      line,\n      msg,\n      url,\n    });\n\n    if (_oldOnErrorHandler && !_oldOnErrorHandler.__SENTRY_LOADER__) {\n      // eslint-disable-next-line prefer-rest-params\n      return _oldOnErrorHandler.apply(this, arguments);\n    }\n\n    return false;\n  };\n\n  WINDOW.onerror.__SENTRY_INSTRUMENTED__ = true;\n}\n\nlet _oldOnUnhandledRejectionHandler: (typeof WINDOW)['onunhandledrejection'] | null = null;\n/** JSDoc */\nfunction instrumentUnhandledRejection(): void {\n  _oldOnUnhandledRejectionHandler = WINDOW.onunhandledrejection;\n\n  WINDOW.onunhandledrejection = function (e: any): boolean {\n    triggerHandlers('unhandledrejection', e);\n\n    if (_oldOnUnhandledRejectionHandler && !_oldOnUnhandledRejectionHandler.__SENTRY_LOADER__) {\n      // eslint-disable-next-line prefer-rest-params\n      return _oldOnUnhandledRejectionHandler.apply(this, arguments);\n    }\n\n    return true;\n  };\n\n  WINDOW.onunhandledrejection.__SENTRY_INSTRUMENTED__ = true;\n}\n", "/*\n * This module exists for optimizations in the build process through rollup and terser.  We define some global\n * constants, which can be overridden during build. By guarding certain pieces of code with functions that return these\n * constants, we can control whether or not they appear in the final bundle. (Any code guarded by a false condition will\n * never run, and will hence be dropped during treeshaking.) The two primary uses for this are stripping out calls to\n * `logger` and preventing node-related code from appearing in browser bundles.\n *\n * Attention:\n * This file should not be used to define constants/flags that are intended to be used for tree-shaking conducted by\n * users. These flags should live in their respective packages, as we identified user tooling (specifically webpack)\n * having issues tree-shaking these constants across package boundaries.\n * An example for this is the __SENTRY_DEBUG__ constant. It is declared in each package individually because we want\n * users to be able to shake away expressions that it guards.\n */\n\ndeclare const __SENTRY_BROWSER_BUNDLE__: boolean | undefined;\n\nexport type SdkSource = 'npm' | 'cdn' | 'loader';\n\n/**\n * Figures out if we're building a browser bundle.\n *\n * @returns true if this is a browser bundle build.\n */\nexport function isBrowserBundle(): boolean {\n  return typeof __SENTRY_BROWSER_BUNDLE__ !== 'undefined' && !!__SENTRY_BROWSER_BUNDLE__;\n}\n\n/**\n * Get source of SDK.\n */\nexport function getSDKSource(): SdkSource {\n  // @ts-expect-error __SENTRY_SDK_SOURCE__ is injected by rollup during build process\n  return __SENTRY_SDK_SOURCE__;\n}\n", "/**\n * NOTE: In order to avoid circular dependencies, if you add a function to this module and it needs to print something,\n * you must either a) use `console.log` rather than the logger, or b) put your function elsewhere.\n */\n\nimport { isBrowserBundle } from './env';\n\n/**\n * Checks whether we're in the Node.js or Browser environment\n *\n * @returns Answer to given question\n */\nexport function isNodeEnv(): boolean {\n  // explicitly check for browser bundles as those can be optimized statically\n  // by terser/rollup.\n  return (\n    !isBrowserBundle() &&\n    Object.prototype.toString.call(typeof process !== 'undefined' ? process : 0) === '[object process]'\n  );\n}\n\n/**\n * Requires a module which is protected against bundler minification.\n *\n * @param request The module path to resolve\n */\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types, @typescript-eslint/no-explicit-any\nexport function dynamicRequire(mod: any, request: string): any {\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n  return mod.require(request);\n}\n\n/**\n * Helper for dynamically loading module that should work with linked dependencies.\n * The problem is that we _should_ be using `require(require.resolve(moduleName, { paths: [cwd()] }))`\n * However it's _not possible_ to do that with Webpack, as it has to know all the dependencies during\n * build time. `require.resolve` is also not available in any other way, so we cannot create,\n * a fake helper like we do with `dynamicRequire`.\n *\n * We always prefer to use local package, thus the value is not returned early from each `try/catch` block.\n * That is to mimic the behavior of `require.resolve` exactly.\n *\n * @param moduleName module name to require\n * @returns possibly required module\n */\nexport function loadModule<T>(moduleName: string): T | undefined {\n  let mod: T | undefined;\n\n  try {\n    mod = dynamicRequire(module, moduleName);\n  } catch (e) {\n    // no-empty\n  }\n\n  try {\n    const { cwd } = dynamicRequire(module, 'process');\n    mod = dynamicRequire(module, `${cwd()}/node_modules/${moduleName}`) as T;\n  } catch (e) {\n    // no-empty\n  }\n\n  return mod;\n}\n", "/* eslint-disable @typescript-eslint/no-unsafe-member-access */\n/* eslint-disable @typescript-eslint/no-explicit-any */\n\nexport type MemoFunc = [\n  // memoize\n  (obj: any) => boolean,\n  // unmemoize\n  (obj: any) => void,\n];\n\n/**\n * Helper to decycle json objects\n */\nexport function memoBuilder(): MemoFunc {\n  const hasWeakSet = typeof WeakSet === 'function';\n  const inner: any = hasWeakSet ? new WeakSet() : [];\n  function memoize(obj: any): boolean {\n    if (hasWeakSet) {\n      if (inner.has(obj)) {\n        return true;\n      }\n      inner.add(obj);\n      return false;\n    }\n    // eslint-disable-next-line @typescript-eslint/prefer-for-of\n    for (let i = 0; i < inner.length; i++) {\n      const value = inner[i];\n      if (value === obj) {\n        return true;\n      }\n    }\n    inner.push(obj);\n    return false;\n  }\n\n  function unmemoize(obj: any): void {\n    if (hasWeakSet) {\n      inner.delete(obj);\n    } else {\n      for (let i = 0; i < inner.length; i++) {\n        if (inner[i] === obj) {\n          inner.splice(i, 1);\n          break;\n        }\n      }\n    }\n  }\n  return [memoize, unmemoize];\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\nimport type { Event, Exception, Mechanism, StackFrame } from '@sentry/types';\n\nimport { addNonEnumerableProperty } from './object';\nimport { snipLine } from './string';\nimport { GLOBAL_OBJ } from './worldwide';\n\ninterface CryptoInternal {\n  getRandomValues(array: Uint8Array): Uint8Array;\n  randomUUID?(): string;\n}\n\n/** An interface for common properties on global */\ninterface CryptoGlobal {\n  msCrypto?: CryptoInternal;\n  crypto?: CryptoInternal;\n}\n\n/**\n * UUID4 generator\n *\n * @returns string Generated UUID4.\n */\nexport function uuid4(): string {\n  const gbl = GLOBAL_OBJ as typeof GLOBAL_OBJ & CryptoGlobal;\n  const crypto = gbl.crypto || gbl.msCrypto;\n\n  let getRandomByte = (): number => Math.random() * 16;\n  try {\n    if (crypto && crypto.randomUUID) {\n      return crypto.randomUUID().replace(/-/g, '');\n    }\n    if (crypto && crypto.getRandomValues) {\n      getRandomByte = () => crypto.getRandomValues(new Uint8Array(1))[0];\n    }\n  } catch (_) {\n    // some runtimes can crash invoking crypto\n    // https://github.com/getsentry/sentry-javascript/issues/8935\n  }\n\n  // http://stackoverflow.com/questions/105034/how-to-create-a-guid-uuid-in-javascript/2117523#2117523\n  // Concatenating the following numbers as strings results in '10000000100040008000100000000000'\n  return (([1e7] as unknown as string) + 1e3 + 4e3 + 8e3 + 1e11).replace(/[018]/g, c =>\n    // eslint-disable-next-line no-bitwise\n    ((c as unknown as number) ^ ((getRandomByte() & 15) >> ((c as unknown as number) / 4))).toString(16),\n  );\n}\n\nfunction getFirstException(event: Event): Exception | undefined {\n  return event.exception && event.exception.values ? event.exception.values[0] : undefined;\n}\n\n/**\n * Extracts either message or type+value from an event that can be used for user-facing logs\n * @returns event's description\n */\nexport function getEventDescription(event: Event): string {\n  const { message, event_id: eventId } = event;\n  if (message) {\n    return message;\n  }\n\n  const firstException = getFirstException(event);\n  if (firstException) {\n    if (firstException.type && firstException.value) {\n      return `${firstException.type}: ${firstException.value}`;\n    }\n    return firstException.type || firstException.value || eventId || '<unknown>';\n  }\n  return eventId || '<unknown>';\n}\n\n/**\n * Adds exception values, type and value to an synthetic Exception.\n * @param event The event to modify.\n * @param value Value of the exception.\n * @param type Type of the exception.\n * @hidden\n */\nexport function addExceptionTypeValue(event: Event, value?: string, type?: string): void {\n  const exception = (event.exception = event.exception || {});\n  const values = (exception.values = exception.values || []);\n  const firstException = (values[0] = values[0] || {});\n  if (!firstException.value) {\n    firstException.value = value || '';\n  }\n  if (!firstException.type) {\n    firstException.type = type || 'Error';\n  }\n}\n\n/**\n * Adds exception mechanism data to a given event. Uses defaults if the second parameter is not passed.\n *\n * @param event The event to modify.\n * @param newMechanism Mechanism data to add to the event.\n * @hidden\n */\nexport function addExceptionMechanism(event: Event, newMechanism?: Partial<Mechanism>): void {\n  const firstException = getFirstException(event);\n  if (!firstException) {\n    return;\n  }\n\n  const defaultMechanism = { type: 'generic', handled: true };\n  const currentMechanism = firstException.mechanism;\n  firstException.mechanism = { ...defaultMechanism, ...currentMechanism, ...newMechanism };\n\n  if (newMechanism && 'data' in newMechanism) {\n    const mergedData = { ...(currentMechanism && currentMechanism.data), ...newMechanism.data };\n    firstException.mechanism.data = mergedData;\n  }\n}\n\n// https://semver.org/#is-there-a-suggested-regular-expression-regex-to-check-a-semver-string\nconst SEMVER_REGEXP =\n  /^(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.(0|[1-9]\\d*)(?:-((?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\\.(?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\\+([0-9a-zA-Z-]+(?:\\.[0-9a-zA-Z-]+)*))?$/;\n\n/**\n * Represents Semantic Versioning object\n */\ninterface SemVer {\n  major?: number;\n  minor?: number;\n  patch?: number;\n  prerelease?: string;\n  buildmetadata?: string;\n}\n\n/**\n * Parses input into a SemVer interface\n * @param input string representation of a semver version\n */\nexport function parseSemver(input: string): SemVer {\n  const match = input.match(SEMVER_REGEXP) || [];\n  const major = parseInt(match[1], 10);\n  const minor = parseInt(match[2], 10);\n  const patch = parseInt(match[3], 10);\n  return {\n    buildmetadata: match[5],\n    major: isNaN(major) ? undefined : major,\n    minor: isNaN(minor) ? undefined : minor,\n    patch: isNaN(patch) ? undefined : patch,\n    prerelease: match[4],\n  };\n}\n\n/**\n * This function adds context (pre/post/line) lines to the provided frame\n *\n * @param lines string[] containing all lines\n * @param frame StackFrame that will be mutated\n * @param linesOfContext number of context lines we want to add pre/post\n */\nexport function addContextToFrame(lines: string[], frame: StackFrame, linesOfContext: number = 5): void {\n  // When there is no line number in the frame, attaching context is nonsensical and will even break grouping\n  if (frame.lineno === undefined) {\n    return;\n  }\n\n  const maxLines = lines.length;\n  const sourceLine = Math.max(Math.min(maxLines - 1, frame.lineno - 1), 0);\n\n  frame.pre_context = lines\n    .slice(Math.max(0, sourceLine - linesOfContext), sourceLine)\n    .map((line: string) => snipLine(line, 0));\n\n  frame.context_line = snipLine(lines[Math.min(maxLines - 1, sourceLine)], frame.colno || 0);\n\n  frame.post_context = lines\n    .slice(Math.min(sourceLine + 1, maxLines), sourceLine + 1 + linesOfContext)\n    .map((line: string) => snipLine(line, 0));\n}\n\n/**\n * Checks whether or not we've already captured the given exception (note: not an identical exception - the very object\n * in question), and marks it captured if not.\n *\n * This is useful because it's possible for an error to get captured by more than one mechanism. After we intercept and\n * record an error, we rethrow it (assuming we've intercepted it before it's reached the top-level global handlers), so\n * that we don't interfere with whatever effects the error might have had were the SDK not there. At that point, because\n * the error has been rethrown, it's possible for it to bubble up to some other code we've instrumented. If it's not\n * caught after that, it will bubble all the way up to the global handlers (which of course we also instrument). This\n * function helps us ensure that even if we encounter the same error more than once, we only record it the first time we\n * see it.\n *\n * Note: It will ignore primitives (always return `false` and not mark them as seen), as properties can't be set on\n * them. {@link: Object.objectify} can be used on exceptions to convert any that are primitives into their equivalent\n * object wrapper forms so that this check will always work. However, because we need to flag the exact object which\n * will get rethrown, and because that rethrowing happens outside of the event processing pipeline, the objectification\n * must be done before the exception captured.\n *\n * @param A thrown exception to check or flag as having been seen\n * @returns `true` if the exception has already been captured, `false` if not (with the side effect of marking it seen)\n */\nexport function checkOrSetAlreadyCaught(exception: unknown): boolean {\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n  if (exception && (exception as any).__sentry_captured__) {\n    return true;\n  }\n\n  try {\n    // set it this way rather than by assignment so that it's not ennumerable and therefore isn't recorded by the\n    // `ExtraErrorData` integration\n    addNonEnumerableProperty(exception as { [key: string]: unknown }, '__sentry_captured__', true);\n  } catch (err) {\n    // `exception` is a primitive, so we can't mark it seen\n  }\n\n  return false;\n}\n\n/**\n * Checks whether the given input is already an array, and if it isn't, wraps it in one.\n *\n * @param maybeArray Input to turn into an array, if necessary\n * @returns The input, if already an array, or an array with the input as the only element, if not\n */\nexport function arrayify<T = unknown>(maybeArray: T | T[]): T[] {\n  return Array.isArray(maybeArray) ? maybeArray : [maybeArray];\n}\n", "import type { Primitive } from '@sentry/types';\n\nimport { isNaN, isSyntheticEvent, isVueViewModel } from './is';\nimport type { MemoFunc } from './memo';\nimport { memoBuilder } from './memo';\nimport { convertToPlainObject } from './object';\nimport { getFunctionName } from './stacktrace';\n\ntype Prototype = { constructor: (...args: unknown[]) => unknown };\n// This is a hack to placate TS, relying on the fact that technically, arrays are objects with integer keys. Normally we\n// think of those keys as actual numbers, but `arr['0']` turns out to work just as well as `arr[0]`, and doing it this\n// way lets us use a single type in the places where behave as if we are only dealing with objects, even if some of them\n// might be arrays.\ntype ObjOrArray<T> = { [key: string]: T };\n\n/**\n * Recursively normalizes the given object.\n *\n * - Creates a copy to prevent original input mutation\n * - Skips non-enumerable properties\n * - When stringifying, calls `toJSON` if implemented\n * - Removes circular references\n * - Translates non-serializable values (`undefined`/`NaN`/functions) to serializable format\n * - Translates known global objects/classes to a string representations\n * - Takes care of `Error` object serialization\n * - Optionally limits depth of final output\n * - Optionally limits number of properties/elements included in any single object/array\n *\n * @param input The object to be normalized.\n * @param depth The max depth to which to normalize the object. (Anything deeper stringified whole.)\n * @param maxProperties The max number of elements or properties to be included in any single array or\n * object in the normallized output.\n * @returns A normalized version of the object, or `\"**non-serializable**\"` if any errors are thrown during normalization.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function normalize(input: unknown, depth: number = 100, maxProperties: number = +Infinity): any {\n  try {\n    // since we're at the outermost level, we don't provide a key\n    return visit('', input, depth, maxProperties);\n  } catch (err) {\n    return { ERROR: `**non-serializable** (${err})` };\n  }\n}\n\n/** JSDoc */\nexport function normalizeToSize<T>(\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  object: { [key: string]: any },\n  // Default Node.js REPL depth\n  depth: number = 3,\n  // 100kB, as 200kB is max payload size, so half sounds reasonable\n  maxSize: number = 100 * 1024,\n): T {\n  const normalized = normalize(object, depth);\n\n  if (jsonSize(normalized) > maxSize) {\n    return normalizeToSize(object, depth - 1, maxSize);\n  }\n\n  return normalized as T;\n}\n\n/**\n * Visits a node to perform normalization on it\n *\n * @param key The key corresponding to the given node\n * @param value The node to be visited\n * @param depth Optional number indicating the maximum recursion depth\n * @param maxProperties Optional maximum number of properties/elements included in any single object/array\n * @param memo Optional Memo class handling decycling\n */\nfunction visit(\n  key: string,\n  value: unknown,\n  depth: number = +Infinity,\n  maxProperties: number = +Infinity,\n  memo: MemoFunc = memoBuilder(),\n): Primitive | ObjOrArray<unknown> {\n  const [memoize, unmemoize] = memo;\n\n  // Get the simple cases out of the way first\n  if (\n    value == null || // this matches null and undefined -> eqeq not eqeqeq\n    (['number', 'boolean', 'string'].includes(typeof value) && !isNaN(value))\n  ) {\n    return value as Primitive;\n  }\n\n  const stringified = stringifyValue(key, value);\n\n  // Anything we could potentially dig into more (objects or arrays) will have come back as `\"[object XXXX]\"`.\n  // Everything else will have already been serialized, so if we don't see that pattern, we're done.\n  if (!stringified.startsWith('[object ')) {\n    return stringified;\n  }\n\n  // From here on, we can assert that `value` is either an object or an array.\n\n  // Do not normalize objects that we know have already been normalized. As a general rule, the\n  // \"__sentry_skip_normalization__\" property should only be used sparingly and only should only be set on objects that\n  // have already been normalized.\n  if ((value as ObjOrArray<unknown>)['__sentry_skip_normalization__']) {\n    return value as ObjOrArray<unknown>;\n  }\n\n  // We can set `__sentry_override_normalization_depth__` on an object to ensure that from there\n  // We keep a certain amount of depth.\n  // This should be used sparingly, e.g. we use it for the redux integration to ensure we get a certain amount of state.\n  const remainingDepth =\n    typeof (value as ObjOrArray<unknown>)['__sentry_override_normalization_depth__'] === 'number'\n      ? ((value as ObjOrArray<unknown>)['__sentry_override_normalization_depth__'] as number)\n      : depth;\n\n  // We're also done if we've reached the max depth\n  if (remainingDepth === 0) {\n    // At this point we know `serialized` is a string of the form `\"[object XXXX]\"`. Clean it up so it's just `\"[XXXX]\"`.\n    return stringified.replace('object ', '');\n  }\n\n  // If we've already visited this branch, bail out, as it's circular reference. If not, note that we're seeing it now.\n  if (memoize(value)) {\n    return '[Circular ~]';\n  }\n\n  // If the value has a `toJSON` method, we call it to extract more information\n  const valueWithToJSON = value as unknown & { toJSON?: () => unknown };\n  if (valueWithToJSON && typeof valueWithToJSON.toJSON === 'function') {\n    try {\n      const jsonValue = valueWithToJSON.toJSON();\n      // We need to normalize the return value of `.toJSON()` in case it has circular references\n      return visit('', jsonValue, remainingDepth - 1, maxProperties, memo);\n    } catch (err) {\n      // pass (The built-in `toJSON` failed, but we can still try to do it ourselves)\n    }\n  }\n\n  // At this point we know we either have an object or an array, we haven't seen it before, and we're going to recurse\n  // because we haven't yet reached the max depth. Create an accumulator to hold the results of visiting each\n  // property/entry, and keep track of the number of items we add to it.\n  const normalized = (Array.isArray(value) ? [] : {}) as ObjOrArray<unknown>;\n  let numAdded = 0;\n\n  // Before we begin, convert`Error` and`Event` instances into plain objects, since some of each of their relevant\n  // properties are non-enumerable and otherwise would get missed.\n  const visitable = convertToPlainObject(value as ObjOrArray<unknown>);\n\n  for (const visitKey in visitable) {\n    // Avoid iterating over fields in the prototype if they've somehow been exposed to enumeration.\n    if (!Object.prototype.hasOwnProperty.call(visitable, visitKey)) {\n      continue;\n    }\n\n    if (numAdded >= maxProperties) {\n      normalized[visitKey] = '[MaxProperties ~]';\n      break;\n    }\n\n    // Recursively visit all the child nodes\n    const visitValue = visitable[visitKey];\n    normalized[visitKey] = visit(visitKey, visitValue, remainingDepth - 1, maxProperties, memo);\n\n    numAdded++;\n  }\n\n  // Once we've visited all the branches, remove the parent from memo storage\n  unmemoize(value);\n\n  // Return accumulated values\n  return normalized;\n}\n\n/**\n * @deprecated This export will be removed in v8.\n */\nexport { visit as walk };\n\n/* eslint-disable complexity */\n/**\n * Stringify the given value. Handles various known special values and types.\n *\n * Not meant to be used on simple primitives which already have a string representation, as it will, for example, turn\n * the number 1231 into \"[Object Number]\", nor on `null`, as it will throw.\n *\n * @param value The value to stringify\n * @returns A stringified representation of the given value\n */\nfunction stringifyValue(\n  key: unknown,\n  // this type is a tiny bit of a cheat, since this function does handle NaN (which is technically a number), but for\n  // our internal use, it'll do\n  value: Exclude<unknown, string | number | boolean | null>,\n): string {\n  try {\n    if (key === 'domain' && value && typeof value === 'object' && (value as { _events: unknown })._events) {\n      return '[Domain]';\n    }\n\n    if (key === 'domainEmitter') {\n      return '[DomainEmitter]';\n    }\n\n    // It's safe to use `global`, `window`, and `document` here in this manner, as we are asserting using `typeof` first\n    // which won't throw if they are not present.\n\n    if (typeof global !== 'undefined' && value === global) {\n      return '[Global]';\n    }\n\n    // eslint-disable-next-line no-restricted-globals\n    if (typeof window !== 'undefined' && value === window) {\n      return '[Window]';\n    }\n\n    // eslint-disable-next-line no-restricted-globals\n    if (typeof document !== 'undefined' && value === document) {\n      return '[Document]';\n    }\n\n    if (isVueViewModel(value)) {\n      return '[VueViewModel]';\n    }\n\n    // React's SyntheticEvent thingy\n    if (isSyntheticEvent(value)) {\n      return '[SyntheticEvent]';\n    }\n\n    if (typeof value === 'number' && value !== value) {\n      return '[NaN]';\n    }\n\n    if (typeof value === 'function') {\n      return `[Function: ${getFunctionName(value)}]`;\n    }\n\n    if (typeof value === 'symbol') {\n      return `[${String(value)}]`;\n    }\n\n    // stringified BigInts are indistinguishable from regular numbers, so we need to label them to avoid confusion\n    if (typeof value === 'bigint') {\n      return `[BigInt: ${String(value)}]`;\n    }\n\n    // Now that we've knocked out all the special cases and the primitives, all we have left are objects. Simply casting\n    // them to strings means that instances of classes which haven't defined their `toStringTag` will just come out as\n    // `\"[object Object]\"`. If we instead look at the constructor's name (which is the same as the name of the class),\n    // we can make sure that only plain objects come out that way.\n    const objName = getConstructorName(value);\n\n    // Handle HTML Elements\n    if (/^HTML(\\w*)Element$/.test(objName)) {\n      return `[HTMLElement: ${objName}]`;\n    }\n\n    return `[object ${objName}]`;\n  } catch (err) {\n    return `**non-serializable** (${err})`;\n  }\n}\n/* eslint-enable complexity */\n\nfunction getConstructorName(value: unknown): string {\n  const prototype: Prototype | null = Object.getPrototypeOf(value);\n\n  return prototype ? prototype.constructor.name : 'null prototype';\n}\n\n/** Calculates bytes size of input string */\nfunction utf8Length(value: string): number {\n  // eslint-disable-next-line no-bitwise\n  return ~-encodeURI(value).split(/%..|./).length;\n}\n\n/** Calculates bytes size of input object */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction jsonSize(value: any): number {\n  return utf8Length(JSON.stringify(value));\n}\n", "// Slightly modified (no IE8 support, ES6) and transcribed to TypeScript\n// https://github.com/calvinmetcalf/rollup-plugin-node-builtins/blob/63ab8aacd013767445ca299e468d9a60a95328d7/src/es6/path.js\n//\n// Copyright Joyent, Inc.and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n/** JSDoc */\nfunction normalizeArray(parts: string[], allowAboveRoot?: boolean): string[] {\n  // if the path tries to go above the root, `up` ends up > 0\n  let up = 0;\n  for (let i = parts.length - 1; i >= 0; i--) {\n    const last = parts[i];\n    if (last === '.') {\n      parts.splice(i, 1);\n    } else if (last === '..') {\n      parts.splice(i, 1);\n      up++;\n    } else if (up) {\n      parts.splice(i, 1);\n      up--;\n    }\n  }\n\n  // if the path is allowed to go above the root, restore leading ..s\n  if (allowAboveRoot) {\n    for (; up--; up) {\n      parts.unshift('..');\n    }\n  }\n\n  return parts;\n}\n\n// Split a filename into [root, dir, basename, ext], unix version\n// 'root' is just a slash, or nothing.\nconst splitPathRe = /^(\\S+:\\\\|\\/?)([\\s\\S]*?)((?:\\.{1,2}|[^/\\\\]+?|)(\\.[^./\\\\]*|))(?:[/\\\\]*)$/;\n/** JSDoc */\nfunction splitPath(filename: string): string[] {\n  // Truncate files names greater than 1024 characters to avoid regex dos\n  // https://github.com/getsentry/sentry-javascript/pull/8737#discussion_r1285719172\n  const truncated = filename.length > 1024 ? `<truncated>${filename.slice(-1024)}` : filename;\n  const parts = splitPathRe.exec(truncated);\n  return parts ? parts.slice(1) : [];\n}\n\n// path.resolve([from ...], to)\n// posix version\n/** JSDoc */\nexport function resolve(...args: string[]): string {\n  let resolvedPath = '';\n  let resolvedAbsolute = false;\n\n  for (let i = args.length - 1; i >= -1 && !resolvedAbsolute; i--) {\n    const path = i >= 0 ? args[i] : '/';\n\n    // Skip empty entries\n    if (!path) {\n      continue;\n    }\n\n    resolvedPath = `${path}/${resolvedPath}`;\n    resolvedAbsolute = path.charAt(0) === '/';\n  }\n\n  // At this point the path should be resolved to a full absolute path, but\n  // handle relative paths to be safe (might happen when process.cwd() fails)\n\n  // Normalize the path\n  resolvedPath = normalizeArray(\n    resolvedPath.split('/').filter(p => !!p),\n    !resolvedAbsolute,\n  ).join('/');\n\n  return (resolvedAbsolute ? '/' : '') + resolvedPath || '.';\n}\n\n/** JSDoc */\nfunction trim(arr: string[]): string[] {\n  let start = 0;\n  for (; start < arr.length; start++) {\n    if (arr[start] !== '') {\n      break;\n    }\n  }\n\n  let end = arr.length - 1;\n  for (; end >= 0; end--) {\n    if (arr[end] !== '') {\n      break;\n    }\n  }\n\n  if (start > end) {\n    return [];\n  }\n  return arr.slice(start, end - start + 1);\n}\n\n// path.relative(from, to)\n// posix version\n/** JSDoc */\nexport function relative(from: string, to: string): string {\n  /* eslint-disable no-param-reassign */\n  from = resolve(from).slice(1);\n  to = resolve(to).slice(1);\n  /* eslint-enable no-param-reassign */\n\n  const fromParts = trim(from.split('/'));\n  const toParts = trim(to.split('/'));\n\n  const length = Math.min(fromParts.length, toParts.length);\n  let samePartsLength = length;\n  for (let i = 0; i < length; i++) {\n    if (fromParts[i] !== toParts[i]) {\n      samePartsLength = i;\n      break;\n    }\n  }\n\n  let outputParts = [];\n  for (let i = samePartsLength; i < fromParts.length; i++) {\n    outputParts.push('..');\n  }\n\n  outputParts = outputParts.concat(toParts.slice(samePartsLength));\n\n  return outputParts.join('/');\n}\n\n// path.normalize(path)\n// posix version\n/** JSDoc */\nexport function normalizePath(path: string): string {\n  const isPathAbsolute = isAbsolute(path);\n  const trailingSlash = path.slice(-1) === '/';\n\n  // Normalize the path\n  let normalizedPath = normalizeArray(\n    path.split('/').filter(p => !!p),\n    !isPathAbsolute,\n  ).join('/');\n\n  if (!normalizedPath && !isPathAbsolute) {\n    normalizedPath = '.';\n  }\n  if (normalizedPath && trailingSlash) {\n    normalizedPath += '/';\n  }\n\n  return (isPathAbsolute ? '/' : '') + normalizedPath;\n}\n\n// posix version\n/** JSDoc */\nexport function isAbsolute(path: string): boolean {\n  return path.charAt(0) === '/';\n}\n\n// posix version\n/** JSDoc */\nexport function join(...args: string[]): string {\n  return normalizePath(args.join('/'));\n}\n\n/** JSDoc */\nexport function dirname(path: string): string {\n  const result = splitPath(path);\n  const root = result[0];\n  let dir = result[1];\n\n  if (!root && !dir) {\n    // No dirname whatsoever\n    return '.';\n  }\n\n  if (dir) {\n    // It has a dirname, strip trailing slash\n    dir = dir.slice(0, dir.length - 1);\n  }\n\n  return root + dir;\n}\n\n/** JSDoc */\nexport function basename(path: string, ext?: string): string {\n  let f = splitPath(path)[2];\n  if (ext && f.slice(ext.length * -1) === ext) {\n    f = f.slice(0, f.length - ext.length);\n  }\n  return f;\n}\n", "/* eslint-disable @typescript-eslint/explicit-function-return-type */\n/* eslint-disable @typescript-eslint/typedef */\n/* eslint-disable @typescript-eslint/explicit-module-boundary-types */\n/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { isThenable } from './is';\n\n/** SyncPromise internal states */\nconst enum States {\n  /** Pending */\n  PENDING = 0,\n  /** Resolved / OK */\n  RESOLVED = 1,\n  /** Rejected / Error */\n  REJECTED = 2,\n}\n\n// Overloads so we can call resolvedSyncPromise without arguments and generic argument\nexport function resolvedSyncPromise(): PromiseLike<void>;\nexport function resolvedSyncPromise<T>(value: T | PromiseLike<T>): PromiseLike<T>;\n\n/**\n * Creates a resolved sync promise.\n *\n * @param value the value to resolve the promise with\n * @returns the resolved sync promise\n */\nexport function resolvedSyncPromise<T>(value?: T | PromiseLike<T>): PromiseLike<T> {\n  return new SyncPromise(resolve => {\n    resolve(value);\n  });\n}\n\n/**\n * Creates a rejected sync promise.\n *\n * @param value the value to reject the promise with\n * @returns the rejected sync promise\n */\nexport function rejectedSyncPromise<T = never>(reason?: any): PromiseLike<T> {\n  return new SyncPromise((_, reject) => {\n    reject(reason);\n  });\n}\n\n/**\n * Thenable class that behaves like a Promise and follows it's interface\n * but is not async internally\n */\nclass SyncPromise<T> implements PromiseLike<T> {\n  private _state: States;\n  private _handlers: Array<[boolean, (value: T) => void, (reason: any) => any]>;\n  private _value: any;\n\n  public constructor(\n    executor: (resolve: (value?: T | PromiseLike<T> | null) => void, reject: (reason?: any) => void) => void,\n  ) {\n    this._state = States.PENDING;\n    this._handlers = [];\n\n    try {\n      executor(this._resolve, this._reject);\n    } catch (e) {\n      this._reject(e);\n    }\n  }\n\n  /** JSDoc */\n  public then<TResult1 = T, TResult2 = never>(\n    onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | null,\n    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | null,\n  ): PromiseLike<TResult1 | TResult2> {\n    return new SyncPromise((resolve, reject) => {\n      this._handlers.push([\n        false,\n        result => {\n          if (!onfulfilled) {\n            // TODO: ¯\\_(ツ)_/¯\n            // TODO: FIXME\n            resolve(result as any);\n          } else {\n            try {\n              resolve(onfulfilled(result));\n            } catch (e) {\n              reject(e);\n            }\n          }\n        },\n        reason => {\n          if (!onrejected) {\n            reject(reason);\n          } else {\n            try {\n              resolve(onrejected(reason));\n            } catch (e) {\n              reject(e);\n            }\n          }\n        },\n      ]);\n      this._executeHandlers();\n    });\n  }\n\n  /** JSDoc */\n  public catch<TResult = never>(\n    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | null,\n  ): PromiseLike<T | TResult> {\n    return this.then(val => val, onrejected);\n  }\n\n  /** JSDoc */\n  public finally<TResult>(onfinally?: (() => void) | null): PromiseLike<TResult> {\n    return new SyncPromise<TResult>((resolve, reject) => {\n      let val: TResult | any;\n      let isRejected: boolean;\n\n      return this.then(\n        value => {\n          isRejected = false;\n          val = value;\n          if (onfinally) {\n            onfinally();\n          }\n        },\n        reason => {\n          isRejected = true;\n          val = reason;\n          if (onfinally) {\n            onfinally();\n          }\n        },\n      ).then(() => {\n        if (isRejected) {\n          reject(val);\n          return;\n        }\n\n        resolve(val as unknown as any);\n      });\n    });\n  }\n\n  /** JSDoc */\n  private readonly _resolve = (value?: T | PromiseLike<T> | null) => {\n    this._setResult(States.RESOLVED, value);\n  };\n\n  /** JSDoc */\n  private readonly _reject = (reason?: any) => {\n    this._setResult(States.REJECTED, reason);\n  };\n\n  /** JSDoc */\n  private readonly _setResult = (state: States, value?: T | PromiseLike<T> | any) => {\n    if (this._state !== States.PENDING) {\n      return;\n    }\n\n    if (isThenable(value)) {\n      void (value as PromiseLike<T>).then(this._resolve, this._reject);\n      return;\n    }\n\n    this._state = state;\n    this._value = value;\n\n    this._executeHandlers();\n  };\n\n  /** JSDoc */\n  private readonly _executeHandlers = () => {\n    if (this._state === States.PENDING) {\n      return;\n    }\n\n    const cachedHandlers = this._handlers.slice();\n    this._handlers = [];\n\n    cachedHandlers.forEach(handler => {\n      if (handler[0]) {\n        return;\n      }\n\n      if (this._state === States.RESOLVED) {\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\n        handler[1](this._value as unknown as any);\n      }\n\n      if (this._state === States.REJECTED) {\n        handler[2](this._value);\n      }\n\n      handler[0] = true;\n    });\n  };\n}\n\nexport { SyncPromise };\n", "import { SentryError } from './error';\nimport { rejectedSyncPromise, resolvedSyncPromise, SyncPromise } from './syncpromise';\n\nexport interface PromiseBuffer<T> {\n  // exposes the internal array so tests can assert on the state of it.\n  // XXX: this really should not be public api.\n  $: Array<PromiseLike<T>>;\n  add(taskProducer: () => PromiseLike<T>): PromiseLike<T>;\n  drain(timeout?: number): PromiseLike<boolean>;\n}\n\n/**\n * Creates an new PromiseBuffer object with the specified limit\n * @param limit max number of promises that can be stored in the buffer\n */\nexport function makePromiseBuffer<T>(limit?: number): PromiseBuffer<T> {\n  const buffer: Array<PromiseLike<T>> = [];\n\n  function isReady(): boolean {\n    return limit === undefined || buffer.length < limit;\n  }\n\n  /**\n   * Remove a promise from the queue.\n   *\n   * @param task Can be any PromiseLike<T>\n   * @returns Removed promise.\n   */\n  function remove(task: PromiseLike<T>): PromiseLike<T> {\n    return buffer.splice(buffer.indexOf(task), 1)[0];\n  }\n\n  /**\n   * Add a promise (representing an in-flight action) to the queue, and set it to remove itself on fulfillment.\n   *\n   * @param taskProducer A function producing any PromiseLike<T>; In previous versions this used to be `task:\n   *        PromiseLike<T>`, but under that model, Promises were instantly created on the call-site and their executor\n   *        functions therefore ran immediately. Thus, even if the buffer was full, the action still happened. By\n   *        requiring the promise to be wrapped in a function, we can defer promise creation until after the buffer\n   *        limit check.\n   * @returns The original promise.\n   */\n  function add(taskProducer: () => PromiseLike<T>): PromiseLike<T> {\n    if (!isReady()) {\n      return rejectedSyncPromise(new SentryError('Not adding Promise because buffer limit was reached.'));\n    }\n\n    // start the task and add its promise to the queue\n    const task = taskProducer();\n    if (buffer.indexOf(task) === -1) {\n      buffer.push(task);\n    }\n    void task\n      .then(() => remove(task))\n      // Use `then(null, rejectionHandler)` rather than `catch(rejectionHandler)` so that we can use `PromiseLike`\n      // rather than `Promise`. `PromiseLike` doesn't have a `.catch` method, making its polyfill smaller. (ES5 didn't\n      // have promises, so TS has to polyfill when down-compiling.)\n      .then(null, () =>\n        remove(task).then(null, () => {\n          // We have to add another catch here because `remove()` starts a new promise chain.\n        }),\n      );\n    return task;\n  }\n\n  /**\n   * Wait for all promises in the queue to resolve or for timeout to expire, whichever comes first.\n   *\n   * @param timeout The time, in ms, after which to resolve to `false` if the queue is still non-empty. Passing `0` (or\n   * not passing anything) will make the promise wait as long as it takes for the queue to drain before resolving to\n   * `true`.\n   * @returns A promise which will resolve to `true` if the queue is already empty or drains before the timeout, and\n   * `false` otherwise\n   */\n  function drain(timeout?: number): PromiseLike<boolean> {\n    return new SyncPromise<boolean>((resolve, reject) => {\n      let counter = buffer.length;\n\n      if (!counter) {\n        return resolve(true);\n      }\n\n      // wait for `timeout` ms and then resolve to `false` (if not cancelled first)\n      const capturedSetTimeout = setTimeout(() => {\n        if (timeout && timeout > 0) {\n          resolve(false);\n        }\n      }, timeout);\n\n      // if all promises resolve in time, cancel the timer and resolve to `true`\n      buffer.forEach(item => {\n        void resolvedSyncPromise(item).then(() => {\n          if (!--counter) {\n            clearTimeout(capturedSetTimeout);\n            resolve(true);\n          }\n        }, reject);\n      });\n    });\n  }\n\n  return {\n    $: buffer,\n    add,\n    drain,\n  };\n}\n", "import { dynamicRequire, isNodeEnv } from './node';\nimport { getGlobalObject } from './worldwide';\n\n// eslint-disable-next-line deprecation/deprecation\nconst WINDOW = getGlobalObject<Window>();\n\n/**\n * An object that can return the current timestamp in seconds since the UNIX epoch.\n */\ninterface TimestampSource {\n  nowSeconds(): number;\n}\n\n/**\n * A TimestampSource implementation for environments that do not support the Performance Web API natively.\n *\n * Note that this TimestampSource does not use a monotonic clock. A call to `nowSeconds` may return a timestamp earlier\n * than a previously returned value. We do not try to emulate a monotonic behavior in order to facilitate debugging. It\n * is more obvious to explain \"why does my span have negative duration\" than \"why my spans have zero duration\".\n */\nconst dateTimestampSource: TimestampSource = {\n  nowSeconds: () => Date.now() / 1000,\n};\n\n/**\n * A partial definition of the [Performance Web API]{@link https://developer.mozilla.org/en-US/docs/Web/API/Performance}\n * for accessing a high-resolution monotonic clock.\n */\ninterface Performance {\n  /**\n   * The millisecond timestamp at which measurement began, measured in Unix time.\n   */\n  timeOrigin: number;\n  /**\n   * Returns the current millisecond timestamp, where 0 represents the start of measurement.\n   */\n  now(): number;\n}\n\n/**\n * Returns a wrapper around the native Performance API browser implementation, or undefined for browsers that do not\n * support the API.\n *\n * Wrapping the native API works around differences in behavior from different browsers.\n */\nfunction getBrowserPerformance(): Performance | undefined {\n  const { performance } = WINDOW;\n  if (!performance || !performance.now) {\n    return undefined;\n  }\n\n  // Replace performance.timeOrigin with our own timeOrigin based on Date.now().\n  //\n  // This is a partial workaround for browsers reporting performance.timeOrigin such that performance.timeOrigin +\n  // performance.now() gives a date arbitrarily in the past.\n  //\n  // Additionally, computing timeOrigin in this way fills the gap for browsers where performance.timeOrigin is\n  // undefined.\n  //\n  // The assumption that performance.timeOrigin + performance.now() ~= Date.now() is flawed, but we depend on it to\n  // interact with data coming out of performance entries.\n  //\n  // Note that despite recommendations against it in the spec, browsers implement the Performance API with a clock that\n  // might stop when the computer is asleep (and perhaps under other circumstances). Such behavior causes\n  // performance.timeOrigin + performance.now() to have an arbitrary skew over Date.now(). In laptop computers, we have\n  // observed skews that can be as long as days, weeks or months.\n  //\n  // See https://github.com/getsentry/sentry-javascript/issues/2590.\n  //\n  // BUG: despite our best intentions, this workaround has its limitations. It mostly addresses timings of pageload\n  // transactions, but ignores the skew built up over time that can aversely affect timestamps of navigation\n  // transactions of long-lived web pages.\n  const timeOrigin = Date.now() - performance.now();\n\n  return {\n    now: () => performance.now(),\n    timeOrigin,\n  };\n}\n\n/**\n * Returns the native Performance API implementation from Node.js. Returns undefined in old Node.js versions that don't\n * implement the API.\n */\nfunction getNodePerformance(): Performance | undefined {\n  try {\n    const perfHooks = dynamicRequire(module, 'perf_hooks') as { performance: Performance };\n    return perfHooks.performance;\n  } catch (_) {\n    return undefined;\n  }\n}\n\n/**\n * The Performance API implementation for the current platform, if available.\n */\nconst platformPerformance: Performance | undefined = isNodeEnv() ? getNodePerformance() : getBrowserPerformance();\n\nconst timestampSource: TimestampSource =\n  platformPerformance === undefined\n    ? dateTimestampSource\n    : {\n        nowSeconds: () => (platformPerformance.timeOrigin + platformPerformance.now()) / 1000,\n      };\n\n/**\n * Returns a timestamp in seconds since the UNIX epoch using the Date API.\n */\nexport const dateTimestampInSeconds: () => number = dateTimestampSource.nowSeconds.bind(dateTimestampSource);\n\n/**\n * Returns a timestamp in seconds since the UNIX epoch using either the Performance or Date APIs, depending on the\n * availability of the Performance API.\n *\n * See `usingPerformanceAPI` to test whether the Performance API is used.\n *\n * BUG: Note that because of how browsers implement the Performance API, the clock might stop when the computer is\n * asleep. This creates a skew between `dateTimestampInSeconds` and `timestampInSeconds`. The\n * skew can grow to arbitrary amounts like days, weeks or months.\n * See https://github.com/getsentry/sentry-javascript/issues/2590.\n */\nexport const timestampInSeconds: () => number = timestampSource.nowSeconds.bind(timestampSource);\n\n/**\n * Re-exported with an old name for backwards-compatibility.\n * TODO (v8): Remove this\n *\n * @deprecated Use `timestampInSeconds` instead.\n */\nexport const timestampWithMs = timestampInSeconds;\n\n/**\n * A boolean that is true when timestampInSeconds uses the Performance API to produce monotonic timestamps.\n */\nexport const usingPerformanceAPI = platformPerformance !== undefined;\n\n/**\n * Internal helper to store what is the source of browserPerformanceTimeOrigin below. For debugging only.\n */\nexport let _browserPerformanceTimeOriginMode: string;\n\n/**\n * The number of milliseconds since the UNIX epoch. This value is only usable in a browser, and only when the\n * performance API is available.\n */\nexport const browserPerformanceTimeOrigin = ((): number | undefined => {\n  // Unfortunately browsers may report an inaccurate time origin data, through either performance.timeOrigin or\n  // performance.timing.navigationStart, which results in poor results in performance data. We only treat time origin\n  // data as reliable if they are within a reasonable threshold of the current time.\n\n  const { performance } = WINDOW;\n  if (!performance || !performance.now) {\n    _browserPerformanceTimeOriginMode = 'none';\n    return undefined;\n  }\n\n  const threshold = 3600 * 1000;\n  const performanceNow = performance.now();\n  const dateNow = Date.now();\n\n  // if timeOrigin isn't available set delta to threshold so it isn't used\n  const timeOriginDelta = performance.timeOrigin\n    ? Math.abs(performance.timeOrigin + performanceNow - dateNow)\n    : threshold;\n  const timeOriginIsReliable = timeOriginDelta < threshold;\n\n  // While performance.timing.navigationStart is deprecated in favor of performance.timeOrigin, performance.timeOrigin\n  // is not as widely supported. Namely, performance.timeOrigin is undefined in Safari as of writing.\n  // Also as of writing, performance.timing is not available in Web Workers in mainstream browsers, so it is not always\n  // a valid fallback. In the absence of an initial time provided by the browser, fallback to the current time from the\n  // Date API.\n  // eslint-disable-next-line deprecation/deprecation\n  const navigationStart = performance.timing && performance.timing.navigationStart;\n  const hasNavigationStart = typeof navigationStart === 'number';\n  // if navigationStart isn't available set delta to threshold so it isn't used\n  const navigationStartDelta = hasNavigationStart ? Math.abs(navigationStart + performanceNow - dateNow) : threshold;\n  const navigationStartIsReliable = navigationStartDelta < threshold;\n\n  if (timeOriginIsReliable || navigationStartIsReliable) {\n    // Use the more reliable time origin\n    if (timeOriginDelta <= navigationStartDelta) {\n      _browserPerformanceTimeOriginMode = 'timeOrigin';\n      return performance.timeOrigin;\n    } else {\n      _browserPerformanceTimeOriginMode = 'navigationStart';\n      return navigationStart;\n    }\n  }\n\n  // Either both timeOrigin and navigationStart are skewed or neither is available, fallback to Date.\n  _browserPerformanceTimeOriginMode = 'dateNow';\n  return dateNow;\n})();\n", "import type { DynamicSamplingContext, PropagationContext, TraceparentData } from '@sentry/types';\n\nimport { baggageHeaderToDynamicSamplingContext } from './baggage';\nimport { uuid4 } from './misc';\n\nexport const TRACEPARENT_REGEXP = new RegExp(\n  '^[ \\\\t]*' + // whitespace\n    '([0-9a-f]{32})?' + // trace_id\n    '-?([0-9a-f]{16})?' + // span_id\n    '-?([01])?' + // sampled\n    '[ \\\\t]*$', // whitespace\n);\n\n/**\n * Extract transaction context data from a `sentry-trace` header.\n *\n * @param traceparent Traceparent string\n *\n * @returns Object containing data from the header, or undefined if traceparent string is malformed\n */\nexport function extractTraceparentData(traceparent?: string): TraceparentData | undefined {\n  if (!traceparent) {\n    return undefined;\n  }\n\n  const matches = traceparent.match(TRACEPARENT_REGEXP);\n  if (!matches) {\n    return undefined;\n  }\n\n  let parentSampled: boolean | undefined;\n  if (matches[3] === '1') {\n    parentSampled = true;\n  } else if (matches[3] === '0') {\n    parentSampled = false;\n  }\n\n  return {\n    traceId: matches[1],\n    parentSampled,\n    parentSpanId: matches[2],\n  };\n}\n\n/**\n * Create tracing context from incoming headers.\n */\nexport function tracingContextFromHeaders(\n  sentryTrace: Parameters<typeof extractTraceparentData>[0],\n  baggage: Parameters<typeof baggageHeaderToDynamicSamplingContext>[0],\n): {\n  traceparentData: ReturnType<typeof extractTraceparentData>;\n  dynamicSamplingContext: ReturnType<typeof baggageHeaderToDynamicSamplingContext>;\n  propagationContext: PropagationContext;\n} {\n  const traceparentData = extractTraceparentData(sentryTrace);\n  const dynamicSamplingContext = baggageHeaderToDynamicSamplingContext(baggage);\n\n  const { traceId, parentSpanId, parentSampled } = traceparentData || {};\n\n  const propagationContext: PropagationContext = {\n    traceId: traceId || uuid4(),\n    spanId: uuid4().substring(16),\n    sampled: parentSampled,\n  };\n\n  if (parentSpanId) {\n    propagationContext.parentSpanId = parentSpanId;\n  }\n\n  if (dynamicSamplingContext) {\n    propagationContext.dsc = dynamicSamplingContext as DynamicSamplingContext;\n  }\n\n  return {\n    traceparentData,\n    dynamicSamplingContext,\n    propagationContext,\n  };\n}\n\n/**\n * Create sentry-trace header from span context values.\n */\nexport function generateSentryTraceHeader(\n  traceId: string = uuid4(),\n  spanId: string = uuid4().substring(16),\n  sampled?: boolean,\n): string {\n  let sampledString = '';\n  if (sampled !== undefined) {\n    sampledString = sampled ? '-1' : '-0';\n  }\n  return `${traceId}-${spanId}${sampledString}`;\n}\n", "import type {\n  Attachment,\n  AttachmentItem,\n  BaseEnvelopeHeaders,\n  BaseEnvelopeItemHeaders,\n  DataCategory,\n  DsnComponents,\n  Envelope,\n  EnvelopeItemType,\n  Event,\n  EventEnvelopeHeaders,\n  SdkInfo,\n  SdkMetadata,\n  TextEncoderInternal,\n} from '@sentry/types';\n\nimport { dsnToString } from './dsn';\nimport { normalize } from './normalize';\nimport { dropUndefinedKeys } from './object';\n\n/**\n * Creates an envelope.\n * Make sure to always explicitly provide the generic to this function\n * so that the envelope types resolve correctly.\n */\nexport function createEnvelope<E extends Envelope>(headers: E[0], items: E[1] = []): E {\n  return [headers, items] as E;\n}\n\n/**\n * Add an item to an envelope.\n * Make sure to always explicitly provide the generic to this function\n * so that the envelope types resolve correctly.\n */\nexport function addItemToEnvelope<E extends Envelope>(envelope: E, newItem: E[1][number]): E {\n  const [headers, items] = envelope;\n  return [headers, [...items, newItem]] as unknown as E;\n}\n\n/**\n * Convenience function to loop through the items and item types of an envelope.\n * (This function was mostly created because working with envelope types is painful at the moment)\n *\n * If the callback returns true, the rest of the items will be skipped.\n */\nexport function forEachEnvelopeItem<E extends Envelope>(\n  envelope: Envelope,\n  callback: (envelopeItem: E[1][number], envelopeItemType: E[1][number][0]['type']) => boolean | void,\n): boolean {\n  const envelopeItems = envelope[1];\n\n  for (const envelopeItem of envelopeItems) {\n    const envelopeItemType = envelopeItem[0].type;\n    const result = callback(envelopeItem, envelopeItemType);\n\n    if (result) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\n/**\n * Returns true if the envelope contains any of the given envelope item types\n */\nexport function envelopeContainsItemType(envelope: Envelope, types: EnvelopeItemType[]): boolean {\n  return forEachEnvelopeItem(envelope, (_, type) => types.includes(type));\n}\n\n/**\n * Encode a string to UTF8.\n */\nfunction encodeUTF8(input: string, textEncoder?: TextEncoderInternal): Uint8Array {\n  const utf8 = textEncoder || new TextEncoder();\n  return utf8.encode(input);\n}\n\n/**\n * Serializes an envelope.\n */\nexport function serializeEnvelope(envelope: Envelope, textEncoder?: TextEncoderInternal): string | Uint8Array {\n  const [envHeaders, items] = envelope;\n\n  // Initially we construct our envelope as a string and only convert to binary chunks if we encounter binary data\n  let parts: string | Uint8Array[] = JSON.stringify(envHeaders);\n\n  function append(next: string | Uint8Array): void {\n    if (typeof parts === 'string') {\n      parts = typeof next === 'string' ? parts + next : [encodeUTF8(parts, textEncoder), next];\n    } else {\n      parts.push(typeof next === 'string' ? encodeUTF8(next, textEncoder) : next);\n    }\n  }\n\n  for (const item of items) {\n    const [itemHeaders, payload] = item;\n\n    append(`\\n${JSON.stringify(itemHeaders)}\\n`);\n\n    if (typeof payload === 'string' || payload instanceof Uint8Array) {\n      append(payload);\n    } else {\n      let stringifiedPayload: string;\n      try {\n        stringifiedPayload = JSON.stringify(payload);\n      } catch (e) {\n        // In case, despite all our efforts to keep `payload` circular-dependency-free, `JSON.strinify()` still\n        // fails, we try again after normalizing it again with infinite normalization depth. This of course has a\n        // performance impact but in this case a performance hit is better than throwing.\n        stringifiedPayload = JSON.stringify(normalize(payload));\n      }\n      append(stringifiedPayload);\n    }\n  }\n\n  return typeof parts === 'string' ? parts : concatBuffers(parts);\n}\n\nfunction concatBuffers(buffers: Uint8Array[]): Uint8Array {\n  const totalLength = buffers.reduce((acc, buf) => acc + buf.length, 0);\n\n  const merged = new Uint8Array(totalLength);\n  let offset = 0;\n  for (const buffer of buffers) {\n    merged.set(buffer, offset);\n    offset += buffer.length;\n  }\n\n  return merged;\n}\n\nexport interface TextDecoderInternal {\n  decode(input?: Uint8Array): string;\n}\n\n/**\n * Parses an envelope\n */\nexport function parseEnvelope(\n  env: string | Uint8Array,\n  textEncoder: TextEncoderInternal,\n  textDecoder: TextDecoderInternal,\n): Envelope {\n  let buffer = typeof env === 'string' ? textEncoder.encode(env) : env;\n\n  function readBinary(length: number): Uint8Array {\n    const bin = buffer.subarray(0, length);\n    // Replace the buffer with the remaining data excluding trailing newline\n    buffer = buffer.subarray(length + 1);\n    return bin;\n  }\n\n  function readJson<T>(): T {\n    let i = buffer.indexOf(0xa);\n    // If we couldn't find a newline, we must have found the end of the buffer\n    if (i < 0) {\n      i = buffer.length;\n    }\n\n    return JSON.parse(textDecoder.decode(readBinary(i))) as T;\n  }\n\n  const envelopeHeader = readJson<BaseEnvelopeHeaders>();\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  const items: [any, any][] = [];\n\n  while (buffer.length) {\n    const itemHeader = readJson<BaseEnvelopeItemHeaders>();\n    const binaryLength = typeof itemHeader.length === 'number' ? itemHeader.length : undefined;\n\n    items.push([itemHeader, binaryLength ? readBinary(binaryLength) : readJson()]);\n  }\n\n  return [envelopeHeader, items];\n}\n\n/**\n * Creates attachment envelope items\n */\nexport function createAttachmentEnvelopeItem(\n  attachment: Attachment,\n  textEncoder?: TextEncoderInternal,\n): AttachmentItem {\n  const buffer = typeof attachment.data === 'string' ? encodeUTF8(attachment.data, textEncoder) : attachment.data;\n\n  return [\n    dropUndefinedKeys({\n      type: 'attachment',\n      length: buffer.length,\n      filename: attachment.filename,\n      content_type: attachment.contentType,\n      attachment_type: attachment.attachmentType,\n    }),\n    buffer,\n  ];\n}\n\nconst ITEM_TYPE_TO_DATA_CATEGORY_MAP: Record<EnvelopeItemType, DataCategory> = {\n  session: 'session',\n  sessions: 'session',\n  attachment: 'attachment',\n  transaction: 'transaction',\n  event: 'error',\n  client_report: 'internal',\n  user_report: 'default',\n  profile: 'profile',\n  replay_event: 'replay',\n  replay_recording: 'replay',\n  check_in: 'monitor',\n  // TODO: This is a temporary workaround until we have a proper data category for metrics\n  statsd: 'unknown',\n};\n\n/**\n * Maps the type of an envelope item to a data category.\n */\nexport function envelopeItemTypeToDataCategory(type: EnvelopeItemType): DataCategory {\n  return ITEM_TYPE_TO_DATA_CATEGORY_MAP[type];\n}\n\n/** Extracts the minimal SDK info from from the metadata or an events */\nexport function getSdkMetadataForEnvelopeHeader(metadataOrEvent?: SdkMetadata | Event): SdkInfo | undefined {\n  if (!metadataOrEvent || !metadataOrEvent.sdk) {\n    return;\n  }\n  const { name, version } = metadataOrEvent.sdk;\n  return { name, version };\n}\n\n/**\n * Creates event envelope headers, based on event, sdk info and tunnel\n * Note: This function was extracted from the core package to make it available in Replay\n */\nexport function createEventEnvelopeHeaders(\n  event: Event,\n  sdkInfo: SdkInfo | undefined,\n  tunnel: string | undefined,\n  dsn?: DsnComponents,\n): EventEnvelopeHeaders {\n  const dynamicSamplingContext = event.sdkProcessingMetadata && event.sdkProcessingMetadata.dynamicSamplingContext;\n  return {\n    event_id: event.event_id as string,\n    sent_at: new Date().toISOString(),\n    ...(sdkInfo && { sdk: sdkInfo }),\n    ...(!!tunnel && dsn && { dsn: dsnToString(dsn) }),\n    ...(dynamicSamplingContext && {\n      trace: dropUndefinedKeys({ ...dynamicSamplingContext }),\n    }),\n  };\n}\n", "import type { TransportMakeRequestResponse } from '@sentry/types';\n\n// Intentionally keeping the key broad, as we don't know for sure what rate limit headers get returned from backend\nexport type RateLimits = Record<string, number>;\n\nexport const DEFAULT_RETRY_AFTER = 60 * 1000; // 60 seconds\n\n/**\n * Extracts Retry-After value from the request header or returns default value\n * @param header string representation of 'Retry-After' header\n * @param now current unix timestamp\n *\n */\nexport function parseRetryAfterHeader(header: string, now: number = Date.now()): number {\n  const headerDelay = parseInt(`${header}`, 10);\n  if (!isNaN(headerDelay)) {\n    return headerDelay * 1000;\n  }\n\n  const headerDate = Date.parse(`${header}`);\n  if (!isNaN(headerDate)) {\n    return headerDate - now;\n  }\n\n  return DEFAULT_RETRY_AFTER;\n}\n\n/**\n * Gets the time that the given category is disabled until for rate limiting.\n * In case no category-specific limit is set but a general rate limit across all categories is active,\n * that time is returned.\n *\n * @return the time in ms that the category is disabled until or 0 if there's no active rate limit.\n */\nexport function disabledUntil(limits: RateLimits, category: string): number {\n  return limits[category] || limits.all || 0;\n}\n\n/**\n * Checks if a category is rate limited\n */\nexport function isRateLimited(limits: RateLimits, category: string, now: number = Date.now()): boolean {\n  return disabledUntil(limits, category) > now;\n}\n\n/**\n * Update ratelimits from incoming headers.\n *\n * @return the updated RateLimits object.\n */\nexport function updateRateLimits(\n  limits: RateLimits,\n  { statusCode, headers }: TransportMakeRequestResponse,\n  now: number = Date.now(),\n): RateLimits {\n  const updatedRateLimits: RateLimits = {\n    ...limits,\n  };\n\n  // \"The name is case-insensitive.\"\n  // https://developer.mozilla.org/en-US/docs/Web/API/Headers/get\n  const rateLimitHeader = headers && headers['x-sentry-rate-limits'];\n  const retryAfterHeader = headers && headers['retry-after'];\n\n  if (rateLimitHeader) {\n    /**\n     * rate limit headers are of the form\n     *     <header>,<header>,..\n     * where each <header> is of the form\n     *     <retry_after>: <categories>: <scope>: <reason_code>\n     * where\n     *     <retry_after> is a delay in seconds\n     *     <categories> is the event type(s) (error, transaction, etc) being rate limited and is of the form\n     *         <category>;<category>;...\n     *     <scope> is what's being limited (org, project, or key) - ignored by SDK\n     *     <reason_code> is an arbitrary string like \"org_quota\" - ignored by SDK\n     */\n    for (const limit of rateLimitHeader.trim().split(',')) {\n      const [retryAfter, categories] = limit.split(':', 2);\n      const headerDelay = parseInt(retryAfter, 10);\n      const delay = (!isNaN(headerDelay) ? headerDelay : 60) * 1000; // 60sec default\n      if (!categories) {\n        updatedRateLimits.all = now + delay;\n      } else {\n        for (const category of categories.split(';')) {\n          updatedRateLimits[category] = now + delay;\n        }\n      }\n    }\n  } else if (retryAfterHeader) {\n    updatedRateLimits.all = now + parseRetryAfterHeader(retryAfterHeader, now);\n  } else if (statusCode === 429) {\n    updatedRateLimits.all = now + 60 * 1000;\n  }\n\n  return updatedRateLimits;\n}\n", "import type {\n  Event,\n  EventHint,\n  Exception,\n  Hub,\n  Mechanism,\n  Severity,\n  SeverityLevel,\n  StackFrame,\n  StackParser,\n} from '@sentry/types';\n\nimport { isError, isPlainObject } from './is';\nimport { addExceptionMechanism, addExceptionTypeValue } from './misc';\nimport { normalizeToSize } from './normalize';\nimport { extractExceptionKeysForMessage } from './object';\n\n/**\n * Extracts stack frames from the error.stack string\n */\nexport function parseStackFrames(stackParser: StackParser, error: Error): StackFrame[] {\n  return stackParser(error.stack || '', 1);\n}\n\n/**\n * Extracts stack frames from the error and builds a Sentry Exception\n */\nexport function exceptionFromError(stackParser: StackParser, error: Error): Exception {\n  const exception: Exception = {\n    type: error.name || error.constructor.name,\n    value: error.message,\n  };\n\n  const frames = parseStackFrames(stackParser, error);\n  if (frames.length) {\n    exception.stacktrace = { frames };\n  }\n\n  return exception;\n}\n\nfunction getMessageForObject(exception: object): string {\n  if ('name' in exception && typeof exception.name === 'string') {\n    let message = `'${exception.name}' captured as exception`;\n\n    if ('message' in exception && typeof exception.message === 'string') {\n      message += ` with message '${exception.message}'`;\n    }\n\n    return message;\n  } else if ('message' in exception && typeof exception.message === 'string') {\n    return exception.message;\n  } else {\n    // This will allow us to group events based on top-level keys\n    // which is much better than creating new group when any key/value change\n    return `Object captured as exception with keys: ${extractExceptionKeysForMessage(\n      exception as Record<string, unknown>,\n    )}`;\n  }\n}\n\n/**\n * Builds and Event from a Exception\n * @hidden\n */\nexport function eventFromUnknownInput(\n  getCurrentHub: () => Hub,\n  stackParser: StackParser,\n  exception: unknown,\n  hint?: EventHint,\n): Event {\n  let ex: unknown = exception;\n  const providedMechanism: Mechanism | undefined =\n    hint && hint.data && (hint.data as { mechanism: Mechanism }).mechanism;\n  const mechanism: Mechanism = providedMechanism || {\n    handled: true,\n    type: 'generic',\n  };\n\n  if (!isError(exception)) {\n    if (isPlainObject(exception)) {\n      const hub = getCurrentHub();\n      const client = hub.getClient();\n      const normalizeDepth = client && client.getOptions().normalizeDepth;\n      hub.configureScope(scope => {\n        scope.setExtra('__serialized__', normalizeToSize(exception, normalizeDepth));\n      });\n\n      const message = getMessageForObject(exception);\n      ex = (hint && hint.syntheticException) || new Error(message);\n      (ex as Error).message = message;\n    } else {\n      // This handles when someone does: `throw \"something awesome\";`\n      // We use synthesized Error here so we can extract a (rough) stack trace.\n      ex = (hint && hint.syntheticException) || new Error(exception as string);\n      (ex as Error).message = exception as string;\n    }\n    mechanism.synthetic = true;\n  }\n\n  const event = {\n    exception: {\n      values: [exceptionFromError(stackParser, ex as Error)],\n    },\n  };\n\n  addExceptionTypeValue(event, undefined, undefined);\n  addExceptionMechanism(event, mechanism);\n\n  return {\n    ...event,\n    event_id: hint && hint.event_id,\n  };\n}\n\n/**\n * Builds and Event from a Message\n * @hidden\n */\nexport function eventFromMessage(\n  stackParser: StackParser,\n  message: string,\n  // eslint-disable-next-line deprecation/deprecation\n  level: Severity | SeverityLevel = 'info',\n  hint?: EventHint,\n  attachStacktrace?: boolean,\n): Event {\n  const event: Event = {\n    event_id: hint && hint.event_id,\n    level,\n    message,\n  };\n\n  if (attachStacktrace && hint && hint.syntheticException) {\n    const frames = parseStackFrames(stackParser, hint.syntheticException);\n    if (frames.length) {\n      event.exception = {\n        values: [\n          {\n            value: message,\n            stacktrace: { frames },\n          },\n        ],\n      };\n    }\n  }\n\n  return event;\n}\n", "export const DEFAULT_ENVIRONMENT = 'production';\n", "import type { Event, EventHint, EventProcessor } from '@sentry/types';\nimport { getGlobalSingleton, isThenable, logger, SyncPromise } from '@sentry/utils';\n\n/**\n * Returns the global event processors.\n */\nexport function getGlobalEventProcessors(): EventProcessor[] {\n  return getGlobalSingleton<EventProcessor[]>('globalEventProcessors', () => []);\n}\n\n/**\n * Add a EventProcessor to be kept globally.\n * @param callback EventProcessor to add\n */\nexport function addGlobalEventProcessor(callback: EventProcessor): void {\n  getGlobalEventProcessors().push(callback);\n}\n\n/**\n * Process an array of event processors, returning the processed event (or `null` if the event was dropped).\n */\nexport function notifyEventProcessors(\n  processors: EventProcessor[],\n  event: Event | null,\n  hint: EventHint,\n  index: number = 0,\n): PromiseLike<Event | null> {\n  return new SyncPromise<Event | null>((resolve, reject) => {\n    const processor = processors[index];\n    if (event === null || typeof processor !== 'function') {\n      resolve(event);\n    } else {\n      const result = processor({ ...event }, hint) as Event | null;\n\n      __DEBUG_BUILD__ &&\n        processor.id &&\n        result === null &&\n        logger.log(`Event processor \"${processor.id}\" dropped event`);\n\n      if (isThenable(result)) {\n        void result\n          .then(final => notifyEventProcessors(processors, final, hint, index + 1).then(resolve))\n          .then(null, reject);\n      } else {\n        void notifyEventProcessors(processors, result, hint, index + 1)\n          .then(resolve)\n          .then(null, reject);\n      }\n    }\n  });\n}\n", "import type { SerializedSession, Session, SessionContext, SessionStatus } from '@sentry/types';\nimport { dropUndefinedKeys, timestampInSeconds, uuid4 } from '@sentry/utils';\n\n/**\n * Creates a new `Session` object by setting certain default parameters. If optional @param context\n * is passed, the passed properties are applied to the session object.\n *\n * @param context (optional) additional properties to be applied to the returned session object\n *\n * @returns a new `Session` object\n */\nexport function makeSession(context?: Omit<SessionContext, 'started' | 'status'>): Session {\n  // Both timestamp and started are in seconds since the UNIX epoch.\n  const startingTime = timestampInSeconds();\n\n  const session: Session = {\n    sid: uuid4(),\n    init: true,\n    timestamp: startingTime,\n    started: startingTime,\n    duration: 0,\n    status: 'ok',\n    errors: 0,\n    ignoreDuration: false,\n    toJSON: () => sessionToJSON(session),\n  };\n\n  if (context) {\n    updateSession(session, context);\n  }\n\n  return session;\n}\n\n/**\n * Updates a session object with the properties passed in the context.\n *\n * Note that this function mutates the passed object and returns void.\n * (Had to do this instead of returning a new and updated session because closing and sending a session\n * makes an update to the session after it was passed to the sending logic.\n * @see BaseClient.captureSession )\n *\n * @param session the `Session` to update\n * @param context the `SessionContext` holding the properties that should be updated in @param session\n */\n// eslint-disable-next-line complexity\nexport function updateSession(session: Session, context: SessionContext = {}): void {\n  if (context.user) {\n    if (!session.ipAddress && context.user.ip_address) {\n      session.ipAddress = context.user.ip_address;\n    }\n\n    if (!session.did && !context.did) {\n      session.did = context.user.id || context.user.email || context.user.username;\n    }\n  }\n\n  session.timestamp = context.timestamp || timestampInSeconds();\n\n  if (context.abnormal_mechanism) {\n    session.abnormal_mechanism = context.abnormal_mechanism;\n  }\n\n  if (context.ignoreDuration) {\n    session.ignoreDuration = context.ignoreDuration;\n  }\n  if (context.sid) {\n    // Good enough uuid validation. — Kamil\n    session.sid = context.sid.length === 32 ? context.sid : uuid4();\n  }\n  if (context.init !== undefined) {\n    session.init = context.init;\n  }\n  if (!session.did && context.did) {\n    session.did = `${context.did}`;\n  }\n  if (typeof context.started === 'number') {\n    session.started = context.started;\n  }\n  if (session.ignoreDuration) {\n    session.duration = undefined;\n  } else if (typeof context.duration === 'number') {\n    session.duration = context.duration;\n  } else {\n    const duration = session.timestamp - session.started;\n    session.duration = duration >= 0 ? duration : 0;\n  }\n  if (context.release) {\n    session.release = context.release;\n  }\n  if (context.environment) {\n    session.environment = context.environment;\n  }\n  if (!session.ipAddress && context.ipAddress) {\n    session.ipAddress = context.ipAddress;\n  }\n  if (!session.userAgent && context.userAgent) {\n    session.userAgent = context.userAgent;\n  }\n  if (typeof context.errors === 'number') {\n    session.errors = context.errors;\n  }\n  if (context.status) {\n    session.status = context.status;\n  }\n}\n\n/**\n * Closes a session by setting its status and updating the session object with it.\n * Internally calls `updateSession` to update the passed session object.\n *\n * Note that this function mutates the passed session (@see updateSession for explanation).\n *\n * @param session the `Session` object to be closed\n * @param status the `SessionStatus` with which the session was closed. If you don't pass a status,\n *               this function will keep the previously set status, unless it was `'ok'` in which case\n *               it is changed to `'exited'`.\n */\nexport function closeSession(session: Session, status?: Exclude<SessionStatus, 'ok'>): void {\n  let context = {};\n  if (status) {\n    context = { status };\n  } else if (session.status === 'ok') {\n    context = { status: 'exited' };\n  }\n\n  updateSession(session, context);\n}\n\n/**\n * Serializes a passed session object to a JSON object with a slightly different structure.\n * This is necessary because the Sentry backend requires a slightly different schema of a session\n * than the one the JS SDKs use internally.\n *\n * @param session the session to be converted\n *\n * @returns a JSON object of the passed session\n */\nfunction sessionToJSON(session: Session): SerializedSession {\n  return dropUndefinedKeys({\n    sid: `${session.sid}`,\n    init: session.init,\n    // Make sure that sec is converted to ms for date constructor\n    started: new Date(session.started * 1000).toISOString(),\n    timestamp: new Date(session.timestamp * 1000).toISOString(),\n    status: session.status,\n    errors: session.errors,\n    did: typeof session.did === 'number' || typeof session.did === 'string' ? `${session.did}` : undefined,\n    duration: session.duration,\n    abnormal_mechanism: session.abnormal_mechanism,\n    attrs: {\n      release: session.release,\n      environment: session.environment,\n      ip_address: session.ipAddress,\n      user_agent: session.userAgent,\n    },\n  });\n}\n", "/* eslint-disable max-lines */\nimport type {\n  Attachment,\n  Breadcrumb,\n  CaptureContext,\n  Context,\n  Contexts,\n  Event,\n  EventHint,\n  EventProcessor,\n  Extra,\n  Extras,\n  Primitive,\n  PropagationContext,\n  RequestSession,\n  Scope as ScopeInterface,\n  ScopeContext,\n  Session,\n  Severity,\n  SeverityLevel,\n  Span,\n  Transaction,\n  User,\n} from '@sentry/types';\nimport { arrayify, dateTimestampInSeconds, isPlainObject, uuid4 } from '@sentry/utils';\n\nimport { getGlobalEventProcessors, notifyEventProcessors } from './eventProcessors';\nimport { updateSession } from './session';\n\n/**\n * Default value for maximum number of breadcrumbs added to an event.\n */\nconst DEFAULT_MAX_BREADCRUMBS = 100;\n\n/**\n * Holds additional event information. {@link Scope.applyToEvent} will be\n * called by the client before an event will be sent.\n */\nexport class Scope implements ScopeInterface {\n  /** Flag if notifying is happening. */\n  protected _notifyingListeners: boolean;\n\n  /** Callback for client to receive scope changes. */\n  protected _scopeListeners: Array<(scope: Scope) => void>;\n\n  /** Callback list that will be called after {@link applyToEvent}. */\n  protected _eventProcessors: EventProcessor[];\n\n  /** Array of breadcrumbs. */\n  protected _breadcrumbs: Breadcrumb[];\n\n  /** User */\n  protected _user: User;\n\n  /** Tags */\n  protected _tags: { [key: string]: Primitive };\n\n  /** Extra */\n  protected _extra: Extras;\n\n  /** Contexts */\n  protected _contexts: Contexts;\n\n  /** Attachments */\n  protected _attachments: Attachment[];\n\n  /** Propagation Context for distributed tracing */\n  protected _propagationContext: PropagationContext;\n\n  /**\n   * A place to stash data which is needed at some point in the SDK's event processing pipeline but which shouldn't get\n   * sent to Sentry\n   */\n  protected _sdkProcessingMetadata: { [key: string]: unknown };\n\n  /** Fingerprint */\n  protected _fingerprint?: string[];\n\n  /** Severity */\n  // eslint-disable-next-line deprecation/deprecation\n  protected _level?: Severity | SeverityLevel;\n\n  /** Transaction Name */\n  protected _transactionName?: string;\n\n  /** Span */\n  protected _span?: Span;\n\n  /** Session */\n  protected _session?: Session;\n\n  /** Request Mode Session Status */\n  protected _requestSession?: RequestSession;\n\n  // NOTE: Any field which gets added here should get added not only to the constructor but also to the `clone` method.\n\n  public constructor() {\n    this._notifyingListeners = false;\n    this._scopeListeners = [];\n    this._eventProcessors = [];\n    this._breadcrumbs = [];\n    this._attachments = [];\n    this._user = {};\n    this._tags = {};\n    this._extra = {};\n    this._contexts = {};\n    this._sdkProcessingMetadata = {};\n    this._propagationContext = generatePropagationContext();\n  }\n\n  /**\n   * Inherit values from the parent scope.\n   * @param scope to clone.\n   */\n  public static clone(scope?: Scope): Scope {\n    const newScope = new Scope();\n    if (scope) {\n      newScope._breadcrumbs = [...scope._breadcrumbs];\n      newScope._tags = { ...scope._tags };\n      newScope._extra = { ...scope._extra };\n      newScope._contexts = { ...scope._contexts };\n      newScope._user = scope._user;\n      newScope._level = scope._level;\n      newScope._span = scope._span;\n      newScope._session = scope._session;\n      newScope._transactionName = scope._transactionName;\n      newScope._fingerprint = scope._fingerprint;\n      newScope._eventProcessors = [...scope._eventProcessors];\n      newScope._requestSession = scope._requestSession;\n      newScope._attachments = [...scope._attachments];\n      newScope._sdkProcessingMetadata = { ...scope._sdkProcessingMetadata };\n      newScope._propagationContext = { ...scope._propagationContext };\n    }\n    return newScope;\n  }\n\n  /**\n   * Add internal on change listener. Used for sub SDKs that need to store the scope.\n   * @hidden\n   */\n  public addScopeListener(callback: (scope: Scope) => void): void {\n    this._scopeListeners.push(callback);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public addEventProcessor(callback: EventProcessor): this {\n    this._eventProcessors.push(callback);\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setUser(user: User | null): this {\n    this._user = user || {};\n    if (this._session) {\n      updateSession(this._session, { user });\n    }\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getUser(): User | undefined {\n    return this._user;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getRequestSession(): RequestSession | undefined {\n    return this._requestSession;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setRequestSession(requestSession?: RequestSession): this {\n    this._requestSession = requestSession;\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setTags(tags: { [key: string]: Primitive }): this {\n    this._tags = {\n      ...this._tags,\n      ...tags,\n    };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setTag(key: string, value: Primitive): this {\n    this._tags = { ...this._tags, [key]: value };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setExtras(extras: Extras): this {\n    this._extra = {\n      ...this._extra,\n      ...extras,\n    };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setExtra(key: string, extra: Extra): this {\n    this._extra = { ...this._extra, [key]: extra };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setFingerprint(fingerprint: string[]): this {\n    this._fingerprint = fingerprint;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setLevel(\n    // eslint-disable-next-line deprecation/deprecation\n    level: Severity | SeverityLevel,\n  ): this {\n    this._level = level;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setTransactionName(name?: string): this {\n    this._transactionName = name;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setContext(key: string, context: Context | null): this {\n    if (context === null) {\n      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n      delete this._contexts[key];\n    } else {\n      this._contexts[key] = context;\n    }\n\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setSpan(span?: Span): this {\n    this._span = span;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getSpan(): Span | undefined {\n    return this._span;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getTransaction(): Transaction | undefined {\n    // Often, this span (if it exists at all) will be a transaction, but it's not guaranteed to be. Regardless, it will\n    // have a pointer to the currently-active transaction.\n    const span = this.getSpan();\n    return span && span.transaction;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setSession(session?: Session): this {\n    if (!session) {\n      delete this._session;\n    } else {\n      this._session = session;\n    }\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getSession(): Session | undefined {\n    return this._session;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public update(captureContext?: CaptureContext): this {\n    if (!captureContext) {\n      return this;\n    }\n\n    if (typeof captureContext === 'function') {\n      const updatedScope = (captureContext as <T>(scope: T) => T)(this);\n      return updatedScope instanceof Scope ? updatedScope : this;\n    }\n\n    if (captureContext instanceof Scope) {\n      this._tags = { ...this._tags, ...captureContext._tags };\n      this._extra = { ...this._extra, ...captureContext._extra };\n      this._contexts = { ...this._contexts, ...captureContext._contexts };\n      if (captureContext._user && Object.keys(captureContext._user).length) {\n        this._user = captureContext._user;\n      }\n      if (captureContext._level) {\n        this._level = captureContext._level;\n      }\n      if (captureContext._fingerprint) {\n        this._fingerprint = captureContext._fingerprint;\n      }\n      if (captureContext._requestSession) {\n        this._requestSession = captureContext._requestSession;\n      }\n      if (captureContext._propagationContext) {\n        this._propagationContext = captureContext._propagationContext;\n      }\n    } else if (isPlainObject(captureContext)) {\n      // eslint-disable-next-line no-param-reassign\n      captureContext = captureContext as ScopeContext;\n      this._tags = { ...this._tags, ...captureContext.tags };\n      this._extra = { ...this._extra, ...captureContext.extra };\n      this._contexts = { ...this._contexts, ...captureContext.contexts };\n      if (captureContext.user) {\n        this._user = captureContext.user;\n      }\n      if (captureContext.level) {\n        this._level = captureContext.level;\n      }\n      if (captureContext.fingerprint) {\n        this._fingerprint = captureContext.fingerprint;\n      }\n      if (captureContext.requestSession) {\n        this._requestSession = captureContext.requestSession;\n      }\n      if (captureContext.propagationContext) {\n        this._propagationContext = captureContext.propagationContext;\n      }\n    }\n\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public clear(): this {\n    this._breadcrumbs = [];\n    this._tags = {};\n    this._extra = {};\n    this._user = {};\n    this._contexts = {};\n    this._level = undefined;\n    this._transactionName = undefined;\n    this._fingerprint = undefined;\n    this._requestSession = undefined;\n    this._span = undefined;\n    this._session = undefined;\n    this._notifyScopeListeners();\n    this._attachments = [];\n    this._propagationContext = generatePropagationContext();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public addBreadcrumb(breadcrumb: Breadcrumb, maxBreadcrumbs?: number): this {\n    const maxCrumbs = typeof maxBreadcrumbs === 'number' ? maxBreadcrumbs : DEFAULT_MAX_BREADCRUMBS;\n\n    // No data has been changed, so don't notify scope listeners\n    if (maxCrumbs <= 0) {\n      return this;\n    }\n\n    const mergedBreadcrumb = {\n      timestamp: dateTimestampInSeconds(),\n      ...breadcrumb,\n    };\n\n    const breadcrumbs = this._breadcrumbs;\n    breadcrumbs.push(mergedBreadcrumb);\n    this._breadcrumbs = breadcrumbs.length > maxCrumbs ? breadcrumbs.slice(-maxCrumbs) : breadcrumbs;\n\n    this._notifyScopeListeners();\n\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getLastBreadcrumb(): Breadcrumb | undefined {\n    return this._breadcrumbs[this._breadcrumbs.length - 1];\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public clearBreadcrumbs(): this {\n    this._breadcrumbs = [];\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public addAttachment(attachment: Attachment): this {\n    this._attachments.push(attachment);\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getAttachments(): Attachment[] {\n    return this._attachments;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public clearAttachments(): this {\n    this._attachments = [];\n    return this;\n  }\n\n  /**\n   * Applies data from the scope to the event and runs all event processors on it.\n   *\n   * @param event Event\n   * @param hint Object containing additional information about the original exception, for use by the event processors.\n   * @hidden\n   */\n  public applyToEvent(\n    event: Event,\n    hint: EventHint = {},\n    additionalEventProcessors?: EventProcessor[],\n  ): PromiseLike<Event | null> {\n    if (this._extra && Object.keys(this._extra).length) {\n      event.extra = { ...this._extra, ...event.extra };\n    }\n    if (this._tags && Object.keys(this._tags).length) {\n      event.tags = { ...this._tags, ...event.tags };\n    }\n    if (this._user && Object.keys(this._user).length) {\n      event.user = { ...this._user, ...event.user };\n    }\n    if (this._contexts && Object.keys(this._contexts).length) {\n      event.contexts = { ...this._contexts, ...event.contexts };\n    }\n    if (this._level) {\n      event.level = this._level;\n    }\n    if (this._transactionName) {\n      event.transaction = this._transactionName;\n    }\n\n    // We want to set the trace context for normal events only if there isn't already\n    // a trace context on the event. There is a product feature in place where we link\n    // errors with transaction and it relies on that.\n    if (this._span) {\n      event.contexts = { trace: this._span.getTraceContext(), ...event.contexts };\n      const transaction = this._span.transaction;\n      if (transaction) {\n        event.sdkProcessingMetadata = {\n          dynamicSamplingContext: transaction.getDynamicSamplingContext(),\n          ...event.sdkProcessingMetadata,\n        };\n        const transactionName = transaction.name;\n        if (transactionName) {\n          event.tags = { transaction: transactionName, ...event.tags };\n        }\n      }\n    }\n\n    this._applyFingerprint(event);\n\n    const scopeBreadcrumbs = this._getBreadcrumbs();\n    const breadcrumbs = [...(event.breadcrumbs || []), ...scopeBreadcrumbs];\n    event.breadcrumbs = breadcrumbs.length > 0 ? breadcrumbs : undefined;\n\n    event.sdkProcessingMetadata = {\n      ...event.sdkProcessingMetadata,\n      ...this._sdkProcessingMetadata,\n      propagationContext: this._propagationContext,\n    };\n\n    // TODO (v8): Update this order to be: Global > Client > Scope\n    return notifyEventProcessors(\n      [...(additionalEventProcessors || []), ...getGlobalEventProcessors(), ...this._eventProcessors],\n      event,\n      hint,\n    );\n  }\n\n  /**\n   * Add data which will be accessible during event processing but won't get sent to Sentry\n   */\n  public setSDKProcessingMetadata(newData: { [key: string]: unknown }): this {\n    this._sdkProcessingMetadata = { ...this._sdkProcessingMetadata, ...newData };\n\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setPropagationContext(context: PropagationContext): this {\n    this._propagationContext = context;\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getPropagationContext(): PropagationContext {\n    return this._propagationContext;\n  }\n\n  /**\n   * Get the breadcrumbs for this scope.\n   */\n  protected _getBreadcrumbs(): Breadcrumb[] {\n    return this._breadcrumbs;\n  }\n\n  /**\n   * This will be called on every set call.\n   */\n  protected _notifyScopeListeners(): void {\n    // We need this check for this._notifyingListeners to be able to work on scope during updates\n    // If this check is not here we'll produce endless recursion when something is done with the scope\n    // during the callback.\n    if (!this._notifyingListeners) {\n      this._notifyingListeners = true;\n      this._scopeListeners.forEach(callback => {\n        callback(this);\n      });\n      this._notifyingListeners = false;\n    }\n  }\n\n  /**\n   * Applies fingerprint from the scope to the event if there's one,\n   * uses message if there's one instead or get rid of empty fingerprint\n   */\n  private _applyFingerprint(event: Event): void {\n    // Make sure it's an array first and we actually have something in place\n    event.fingerprint = event.fingerprint ? arrayify(event.fingerprint) : [];\n\n    // If we have something on the scope, then merge it with event\n    if (this._fingerprint) {\n      event.fingerprint = event.fingerprint.concat(this._fingerprint);\n    }\n\n    // If we have no data at all, remove empty array default\n    if (event.fingerprint && !event.fingerprint.length) {\n      delete event.fingerprint;\n    }\n  }\n}\n\nfunction generatePropagationContext(): PropagationContext {\n  return {\n    traceId: uuid4(),\n    spanId: uuid4().substring(16),\n  };\n}\n", "/* eslint-disable max-lines */\nimport type {\n  Breadcrumb,\n  BreadcrumbHint,\n  Client,\n  CustomSamplingContext,\n  Event,\n  EventHint,\n  Extra,\n  Extras,\n  Hub as HubInterface,\n  Integration,\n  IntegrationClass,\n  Primitive,\n  Session,\n  SessionContext,\n  Severity,\n  SeverityLevel,\n  Transaction,\n  TransactionContext,\n  User,\n} from '@sentry/types';\nimport { consoleSandbox, dateTimestampInSeconds, getGlobalSingleton, GLOBAL_OBJ, logger, uuid4 } from '@sentry/utils';\n\nimport { DEFAULT_ENVIRONMENT } from './constants';\nimport { Scope } from './scope';\nimport { closeSession, makeSession, updateSession } from './session';\n\n/**\n * API compatibility version of this hub.\n *\n * WARNING: This number should only be increased when the global interface\n * changes and new methods are introduced.\n *\n * @hidden\n */\nexport const API_VERSION = 4;\n\n/**\n * Default maximum number of breadcrumbs added to an event. Can be overwritten\n * with {@link Options.maxBreadcrumbs}.\n */\nconst DEFAULT_BREADCRUMBS = 100;\n\nexport interface RunWithAsyncContextOptions {\n  /** Whether to reuse an existing async context if one exists. Defaults to false. */\n  reuseExisting?: boolean;\n}\n\n/**\n * @private Private API with no semver guarantees!\n *\n * Strategy used to track async context.\n */\nexport interface AsyncContextStrategy {\n  /**\n   * Gets the current async context. Returns undefined if there is no current async context.\n   */\n  getCurrentHub: () => Hub | undefined;\n  /**\n   * Runs the supplied callback in its own async context.\n   */\n  runWithAsyncContext<T>(callback: () => T, options: RunWithAsyncContextOptions): T;\n}\n\n/**\n * A layer in the process stack.\n * @hidden\n */\nexport interface Layer {\n  client?: Client;\n  scope: Scope;\n}\n\n/**\n * An object that contains a hub and maintains a scope stack.\n * @hidden\n */\nexport interface Carrier {\n  __SENTRY__?: {\n    hub?: Hub;\n    acs?: AsyncContextStrategy;\n    /**\n     * Extra Hub properties injected by various SDKs\n     */\n    integrations?: Integration[];\n    extensions?: {\n      /** Extension methods for the hub, which are bound to the current Hub instance */\n      // eslint-disable-next-line @typescript-eslint/ban-types\n      [key: string]: Function;\n    };\n  };\n}\n\n/**\n * @inheritDoc\n */\nexport class Hub implements HubInterface {\n  /** Is a {@link Layer}[] containing the client and scope */\n  private readonly _stack: Layer[];\n\n  /** Contains the last event id of a captured event.  */\n  private _lastEventId?: string;\n\n  /**\n   * Creates a new instance of the hub, will push one {@link Layer} into the\n   * internal stack on creation.\n   *\n   * @param client bound to the hub.\n   * @param scope bound to the hub.\n   * @param version number, higher number means higher priority.\n   */\n  public constructor(client?: Client, scope: Scope = new Scope(), private readonly _version: number = API_VERSION) {\n    this._stack = [{ scope }];\n    if (client) {\n      this.bindClient(client);\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public isOlderThan(version: number): boolean {\n    return this._version < version;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public bindClient(client?: Client): void {\n    const top = this.getStackTop();\n    top.client = client;\n    if (client && client.setupIntegrations) {\n      client.setupIntegrations();\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public pushScope(): Scope {\n    // We want to clone the content of prev scope\n    const scope = Scope.clone(this.getScope());\n    this.getStack().push({\n      client: this.getClient(),\n      scope,\n    });\n    return scope;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public popScope(): boolean {\n    if (this.getStack().length <= 1) return false;\n    return !!this.getStack().pop();\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public withScope(callback: (scope: Scope) => void): void {\n    const scope = this.pushScope();\n    try {\n      callback(scope);\n    } finally {\n      this.popScope();\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getClient<C extends Client>(): C | undefined {\n    return this.getStackTop().client as C;\n  }\n\n  /** Returns the scope of the top stack. */\n  public getScope(): Scope {\n    return this.getStackTop().scope;\n  }\n\n  /** Returns the scope stack for domains or the process. */\n  public getStack(): Layer[] {\n    return this._stack;\n  }\n\n  /** Returns the topmost scope layer in the order domain > local > process. */\n  public getStackTop(): Layer {\n    return this._stack[this._stack.length - 1];\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureException(exception: unknown, hint?: EventHint): string {\n    const eventId = (this._lastEventId = hint && hint.event_id ? hint.event_id : uuid4());\n    const syntheticException = new Error('Sentry syntheticException');\n    this._withClient((client, scope) => {\n      client.captureException(\n        exception,\n        {\n          originalException: exception,\n          syntheticException,\n          ...hint,\n          event_id: eventId,\n        },\n        scope,\n      );\n    });\n    return eventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureMessage(\n    message: string,\n    // eslint-disable-next-line deprecation/deprecation\n    level?: Severity | SeverityLevel,\n    hint?: EventHint,\n  ): string {\n    const eventId = (this._lastEventId = hint && hint.event_id ? hint.event_id : uuid4());\n    const syntheticException = new Error(message);\n    this._withClient((client, scope) => {\n      client.captureMessage(\n        message,\n        level,\n        {\n          originalException: message,\n          syntheticException,\n          ...hint,\n          event_id: eventId,\n        },\n        scope,\n      );\n    });\n    return eventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureEvent(event: Event, hint?: EventHint): string {\n    const eventId = hint && hint.event_id ? hint.event_id : uuid4();\n    if (!event.type) {\n      this._lastEventId = eventId;\n    }\n\n    this._withClient((client, scope) => {\n      client.captureEvent(event, { ...hint, event_id: eventId }, scope);\n    });\n    return eventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public lastEventId(): string | undefined {\n    return this._lastEventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public addBreadcrumb(breadcrumb: Breadcrumb, hint?: BreadcrumbHint): void {\n    const { scope, client } = this.getStackTop();\n\n    if (!client) return;\n\n    const { beforeBreadcrumb = null, maxBreadcrumbs = DEFAULT_BREADCRUMBS } =\n      (client.getOptions && client.getOptions()) || {};\n\n    if (maxBreadcrumbs <= 0) return;\n\n    const timestamp = dateTimestampInSeconds();\n    const mergedBreadcrumb = { timestamp, ...breadcrumb };\n    const finalBreadcrumb = beforeBreadcrumb\n      ? (consoleSandbox(() => beforeBreadcrumb(mergedBreadcrumb, hint)) as Breadcrumb | null)\n      : mergedBreadcrumb;\n\n    if (finalBreadcrumb === null) return;\n\n    if (client.emit) {\n      client.emit('beforeAddBreadcrumb', finalBreadcrumb, hint);\n    }\n\n    scope.addBreadcrumb(finalBreadcrumb, maxBreadcrumbs);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setUser(user: User | null): void {\n    this.getScope().setUser(user);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setTags(tags: { [key: string]: Primitive }): void {\n    this.getScope().setTags(tags);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setExtras(extras: Extras): void {\n    this.getScope().setExtras(extras);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setTag(key: string, value: Primitive): void {\n    this.getScope().setTag(key, value);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setExtra(key: string, extra: Extra): void {\n    this.getScope().setExtra(key, extra);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  public setContext(name: string, context: { [key: string]: any } | null): void {\n    this.getScope().setContext(name, context);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public configureScope(callback: (scope: Scope) => void): void {\n    const { scope, client } = this.getStackTop();\n    if (client) {\n      callback(scope);\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public run(callback: (hub: Hub) => void): void {\n    const oldHub = makeMain(this);\n    try {\n      callback(this);\n    } finally {\n      makeMain(oldHub);\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getIntegration<T extends Integration>(integration: IntegrationClass<T>): T | null {\n    const client = this.getClient();\n    if (!client) return null;\n    try {\n      return client.getIntegration(integration);\n    } catch (_oO) {\n      __DEBUG_BUILD__ && logger.warn(`Cannot retrieve integration ${integration.id} from the current Hub`);\n      return null;\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public startTransaction(context: TransactionContext, customSamplingContext?: CustomSamplingContext): Transaction {\n    const result = this._callExtensionMethod<Transaction>('startTransaction', context, customSamplingContext);\n\n    if (__DEBUG_BUILD__ && !result) {\n      const client = this.getClient();\n      if (!client) {\n        // eslint-disable-next-line no-console\n        console.warn(\n          \"Tracing extension 'startTransaction' is missing. You should 'init' the SDK before calling 'startTransaction'\",\n        );\n      } else {\n        // eslint-disable-next-line no-console\n        console.warn(`Tracing extension 'startTransaction' has not been added. Call 'addTracingExtensions' before calling 'init':\nSentry.addTracingExtensions();\nSentry.init({...});\n`);\n      }\n    }\n\n    return result;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public traceHeaders(): { [key: string]: string } {\n    return this._callExtensionMethod<{ [key: string]: string }>('traceHeaders');\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureSession(endSession: boolean = false): void {\n    // both send the update and pull the session from the scope\n    if (endSession) {\n      return this.endSession();\n    }\n\n    // only send the update\n    this._sendSessionUpdate();\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public endSession(): void {\n    const layer = this.getStackTop();\n    const scope = layer.scope;\n    const session = scope.getSession();\n    if (session) {\n      closeSession(session);\n    }\n    this._sendSessionUpdate();\n\n    // the session is over; take it off of the scope\n    scope.setSession();\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public startSession(context?: SessionContext): Session {\n    const { scope, client } = this.getStackTop();\n    const { release, environment = DEFAULT_ENVIRONMENT } = (client && client.getOptions()) || {};\n\n    // Will fetch userAgent if called from browser sdk\n    const { userAgent } = GLOBAL_OBJ.navigator || {};\n\n    const session = makeSession({\n      release,\n      environment,\n      user: scope.getUser(),\n      ...(userAgent && { userAgent }),\n      ...context,\n    });\n\n    // End existing session if there's one\n    const currentSession = scope.getSession && scope.getSession();\n    if (currentSession && currentSession.status === 'ok') {\n      updateSession(currentSession, { status: 'exited' });\n    }\n    this.endSession();\n\n    // Afterwards we set the new session on the scope\n    scope.setSession(session);\n\n    return session;\n  }\n\n  /**\n   * Returns if default PII should be sent to Sentry and propagated in ourgoing requests\n   * when Tracing is used.\n   */\n  public shouldSendDefaultPii(): boolean {\n    const client = this.getClient();\n    const options = client && client.getOptions();\n    return Boolean(options && options.sendDefaultPii);\n  }\n\n  /**\n   * Sends the current Session on the scope\n   */\n  private _sendSessionUpdate(): void {\n    const { scope, client } = this.getStackTop();\n\n    const session = scope.getSession();\n    if (session && client && client.captureSession) {\n      client.captureSession(session);\n    }\n  }\n\n  /**\n   * Internal helper function to call a method on the top client if it exists.\n   *\n   * @param method The method to call on the client.\n   * @param args Arguments to pass to the client function.\n   */\n  private _withClient(callback: (client: Client, scope: Scope) => void): void {\n    const { scope, client } = this.getStackTop();\n    if (client) {\n      callback(client, scope);\n    }\n  }\n\n  /**\n   * Calls global extension method and binding current instance to the function call\n   */\n  // @ts-expect-error Function lacks ending return statement and return type does not include 'undefined'. ts(2366)\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private _callExtensionMethod<T>(method: string, ...args: any[]): T {\n    const carrier = getMainCarrier();\n    const sentry = carrier.__SENTRY__;\n    if (sentry && sentry.extensions && typeof sentry.extensions[method] === 'function') {\n      return sentry.extensions[method].apply(this, args);\n    }\n    __DEBUG_BUILD__ && logger.warn(`Extension method ${method} couldn't be found, doing nothing.`);\n  }\n}\n\n/**\n * Returns the global shim registry.\n *\n * FIXME: This function is problematic, because despite always returning a valid Carrier,\n * it has an optional `__SENTRY__` property, which then in turn requires us to always perform an unnecessary check\n * at the call-site. We always access the carrier through this function, so we can guarantee that `__SENTRY__` is there.\n **/\nexport function getMainCarrier(): Carrier {\n  GLOBAL_OBJ.__SENTRY__ = GLOBAL_OBJ.__SENTRY__ || {\n    extensions: {},\n    hub: undefined,\n  };\n  return GLOBAL_OBJ;\n}\n\n/**\n * Replaces the current main hub with the passed one on the global object\n *\n * @returns The old replaced hub\n */\nexport function makeMain(hub: Hub): Hub {\n  const registry = getMainCarrier();\n  const oldHub = getHubFromCarrier(registry);\n  setHubOnCarrier(registry, hub);\n  return oldHub;\n}\n\n/**\n * Returns the default hub instance.\n *\n * If a hub is already registered in the global carrier but this module\n * contains a more recent version, it replaces the registered version.\n * Otherwise, the currently registered hub will be returned.\n */\nexport function getCurrentHub(): Hub {\n  // Get main carrier (global for every environment)\n  const registry = getMainCarrier();\n\n  if (registry.__SENTRY__ && registry.__SENTRY__.acs) {\n    const hub = registry.__SENTRY__.acs.getCurrentHub();\n\n    if (hub) {\n      return hub;\n    }\n  }\n\n  // Return hub that lives on a global object\n  return getGlobalHub(registry);\n}\n\nfunction getGlobalHub(registry: Carrier = getMainCarrier()): Hub {\n  // If there's no hub, or its an old API, assign a new one\n  if (!hasHubOnCarrier(registry) || getHubFromCarrier(registry).isOlderThan(API_VERSION)) {\n    setHubOnCarrier(registry, new Hub());\n  }\n\n  // Return hub that lives on a global object\n  return getHubFromCarrier(registry);\n}\n\n/**\n * @private Private API with no semver guarantees!\n *\n * If the carrier does not contain a hub, a new hub is created with the global hub client and scope.\n */\nexport function ensureHubOnCarrier(carrier: Carrier, parent: Hub = getGlobalHub()): void {\n  // If there's no hub on current domain, or it's an old API, assign a new one\n  if (!hasHubOnCarrier(carrier) || getHubFromCarrier(carrier).isOlderThan(API_VERSION)) {\n    const globalHubTopStack = parent.getStackTop();\n    setHubOnCarrier(carrier, new Hub(globalHubTopStack.client, Scope.clone(globalHubTopStack.scope)));\n  }\n}\n\n/**\n * @private Private API with no semver guarantees!\n *\n * Sets the global async context strategy\n */\nexport function setAsyncContextStrategy(strategy: AsyncContextStrategy | undefined): void {\n  // Get main carrier (global for every environment)\n  const registry = getMainCarrier();\n  registry.__SENTRY__ = registry.__SENTRY__ || {};\n  registry.__SENTRY__.acs = strategy;\n}\n\n/**\n * Runs the supplied callback in its own async context. Async Context strategies are defined per SDK.\n *\n * @param callback The callback to run in its own async context\n * @param options Options to pass to the async context strategy\n * @returns The result of the callback\n */\nexport function runWithAsyncContext<T>(callback: () => T, options: RunWithAsyncContextOptions = {}): T {\n  const registry = getMainCarrier();\n\n  if (registry.__SENTRY__ && registry.__SENTRY__.acs) {\n    return registry.__SENTRY__.acs.runWithAsyncContext(callback, options);\n  }\n\n  // if there was no strategy, fallback to just calling the callback\n  return callback();\n}\n\n/**\n * This will tell whether a carrier has a hub on it or not\n * @param carrier object\n */\nfunction hasHubOnCarrier(carrier: Carrier): boolean {\n  return !!(carrier && carrier.__SENTRY__ && carrier.__SENTRY__.hub);\n}\n\n/**\n * This will create a new {@link Hub} and add to the passed object on\n * __SENTRY__.hub.\n * @param carrier object\n * @hidden\n */\nexport function getHubFromCarrier(carrier: Carrier): Hub {\n  return getGlobalSingleton<Hub>('hub', () => new Hub(), carrier);\n}\n\n/**\n * This will set passed {@link Hub} on the passed object's __SENTRY__.hub attribute\n * @param carrier object\n * @param hub Hub\n * @returns A boolean indicating success or failure\n */\nexport function setHubOnCarrier(carrier: Carrier, hub: Hub): boolean {\n  if (!carrier) return false;\n  const __SENTRY__ = (carrier.__SENTRY__ = carrier.__SENTRY__ || {});\n  __SENTRY__.hub = hub;\n  return true;\n}\n", "import type { Transaction } from '@sentry/types';\n\nimport type { Hub } from '../hub';\nimport { getCurrentHub } from '../hub';\n\n/**\n * The `extractTraceparentData` function and `TRACEPARENT_REGEXP` constant used\n * to be declared in this file. It was later moved into `@sentry/utils` as part of a\n * move to remove `@sentry/tracing` dependencies from `@sentry/node` (`extractTraceparentData`\n * is the only tracing function used by `@sentry/node`).\n *\n * These exports are kept here for backwards compatability's sake.\n *\n * TODO(v7): Reorganize these exports\n *\n * See https://github.com/getsentry/sentry-javascript/issues/4642 for more details.\n */\nexport { TRACEPARENT_REGEXP, extractTraceparentData } from '@sentry/utils';\n\n/** Grabs active transaction off scope, if any */\nexport function getActiveTransaction<T extends Transaction>(maybeHub?: Hub): T | undefined {\n  const hub = maybeHub || getCurrentHub();\n  const scope = hub.getScope();\n  return scope.getTransaction() as T | undefined;\n}\n\n// so it can be used in manual instrumentation without necessitating a hard dependency on @sentry/utils\nexport { stripUrlQueryAndFragment } from '@sentry/utils';\n", "import { addInstrumentationHand<PERSON>, logger } from '@sentry/utils';\n\nimport type { SpanStatusType } from './span';\nimport { getActiveTransaction } from './utils';\n\nlet errorsInstrumented = false;\n\n/**\n * Configures global error listeners\n */\nexport function registerErrorInstrumentation(): void {\n  if (errorsInstrumented) {\n    return;\n  }\n\n  errorsInstrumented = true;\n  addInstrumentationHandler('error', errorCallback);\n  addInstrumentationHandler('unhandledrejection', errorCallback);\n}\n\n/**\n * If an error or unhandled promise occurs, we mark the active transaction as failed\n */\nfunction errorCallback(): void {\n  const activeTransaction = getActiveTransaction();\n  if (activeTransaction) {\n    const status: SpanStatusType = 'internal_error';\n    __DEBUG_BUILD__ && logger.log(`[Tracing] Transaction: ${status} -> Global error occured`);\n    activeTransaction.setStatus(status);\n  }\n}\n\n// The function name will be lost when bundling but we need to be able to identify this listener later to maintain the\n// node.js default exit behaviour\nerrorCallback.tag = 'sentry_tracingErrorCallback';\n", "/* eslint-disable max-lines */\nimport type {\n  Instrumenter,\n  Primitive,\n  Span as SpanInterface,\n  SpanContext,\n  SpanOrigin,\n  TraceContext,\n  Transaction,\n} from '@sentry/types';\nimport { dropUndefinedKeys, generateSentryTraceHeader, logger, timestampInSeconds, uuid4 } from '@sentry/utils';\n\n/**\n * Keeps track of finished spans for a given transaction\n * @internal\n * @hideconstructor\n * @hidden\n */\nexport class SpanRecorder {\n  public spans: Span[];\n\n  private readonly _maxlen: number;\n\n  public constructor(maxlen: number = 1000) {\n    this._maxlen = maxlen;\n    this.spans = [];\n  }\n\n  /**\n   * This is just so that we don't run out of memory while recording a lot\n   * of spans. At some point we just stop and flush out the start of the\n   * trace tree (i.e.the first n spans with the smallest\n   * start_timestamp).\n   */\n  public add(span: Span): void {\n    if (this.spans.length > this._maxlen) {\n      span.spanRecorder = undefined;\n    } else {\n      this.spans.push(span);\n    }\n  }\n}\n\n/**\n * Span contains all data about a span\n */\nexport class Span implements SpanInterface {\n  /**\n   * @inheritDoc\n   */\n  public traceId: string;\n\n  /**\n   * @inheritDoc\n   */\n  public spanId: string;\n\n  /**\n   * @inheritDoc\n   */\n  public parentSpanId?: string;\n\n  /**\n   * Internal keeper of the status\n   */\n  public status?: SpanStatusType | string;\n\n  /**\n   * @inheritDoc\n   */\n  public sampled?: boolean;\n\n  /**\n   * Timestamp in seconds when the span was created.\n   */\n  public startTimestamp: number;\n\n  /**\n   * Timestamp in seconds when the span ended.\n   */\n  public endTimestamp?: number;\n\n  /**\n   * @inheritDoc\n   */\n  public op?: string;\n\n  /**\n   * @inheritDoc\n   */\n  public description?: string;\n\n  /**\n   * @inheritDoc\n   */\n  public tags: { [key: string]: Primitive };\n\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  public data: { [key: string]: any };\n\n  /**\n   * List of spans that were finalized\n   */\n  public spanRecorder?: SpanRecorder;\n\n  /**\n   * @inheritDoc\n   */\n  public transaction?: Transaction;\n\n  /**\n   * The instrumenter that created this span.\n   */\n  public instrumenter: Instrumenter;\n\n  /**\n   * The origin of the span, giving context about what created the span.\n   */\n  public origin?: SpanOrigin;\n\n  /**\n   * You should never call the constructor manually, always use `Sentry.startTransaction()`\n   * or call `startChild()` on an existing span.\n   * @internal\n   * @hideconstructor\n   * @hidden\n   */\n  public constructor(spanContext: SpanContext = {}) {\n    this.traceId = spanContext.traceId || uuid4();\n    this.spanId = spanContext.spanId || uuid4().substring(16);\n    this.startTimestamp = spanContext.startTimestamp || timestampInSeconds();\n    this.tags = spanContext.tags || {};\n    this.data = spanContext.data || {};\n    this.instrumenter = spanContext.instrumenter || 'sentry';\n    this.origin = spanContext.origin || 'manual';\n\n    if (spanContext.parentSpanId) {\n      this.parentSpanId = spanContext.parentSpanId;\n    }\n    // We want to include booleans as well here\n    if ('sampled' in spanContext) {\n      this.sampled = spanContext.sampled;\n    }\n    if (spanContext.op) {\n      this.op = spanContext.op;\n    }\n    if (spanContext.description) {\n      this.description = spanContext.description;\n    }\n    if (spanContext.name) {\n      this.description = spanContext.name;\n    }\n    if (spanContext.status) {\n      this.status = spanContext.status;\n    }\n    if (spanContext.endTimestamp) {\n      this.endTimestamp = spanContext.endTimestamp;\n    }\n  }\n\n  /** An alias for `description` of the Span. */\n  public get name(): string {\n    return this.description || '';\n  }\n  /** Update the name of the span. */\n  public set name(name: string) {\n    this.setName(name);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public startChild(\n    spanContext?: Pick<SpanContext, Exclude<keyof SpanContext, 'sampled' | 'traceId' | 'parentSpanId'>>,\n  ): Span {\n    const childSpan = new Span({\n      ...spanContext,\n      parentSpanId: this.spanId,\n      sampled: this.sampled,\n      traceId: this.traceId,\n    });\n\n    childSpan.spanRecorder = this.spanRecorder;\n    if (childSpan.spanRecorder) {\n      childSpan.spanRecorder.add(childSpan);\n    }\n\n    childSpan.transaction = this.transaction;\n\n    if (__DEBUG_BUILD__ && childSpan.transaction) {\n      const opStr = (spanContext && spanContext.op) || '< unknown op >';\n      const nameStr = childSpan.transaction.name || '< unknown name >';\n      const idStr = childSpan.transaction.spanId;\n\n      const logMessage = `[Tracing] Starting '${opStr}' span on transaction '${nameStr}' (${idStr}).`;\n      childSpan.transaction.metadata.spanMetadata[childSpan.spanId] = { logMessage };\n      logger.log(logMessage);\n    }\n\n    return childSpan;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setTag(key: string, value: Primitive): this {\n    this.tags = { ...this.tags, [key]: value };\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types\n  public setData(key: string, value: any): this {\n    this.data = { ...this.data, [key]: value };\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setStatus(value: SpanStatusType): this {\n    this.status = value;\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setHttpStatus(httpStatus: number): this {\n    this.setTag('http.status_code', String(httpStatus));\n    this.setData('http.response.status_code', httpStatus);\n    const spanStatus = spanStatusfromHttpCode(httpStatus);\n    if (spanStatus !== 'unknown_error') {\n      this.setStatus(spanStatus);\n    }\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setName(name: string): void {\n    this.description = name;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public isSuccess(): boolean {\n    return this.status === 'ok';\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public finish(endTimestamp?: number): void {\n    if (\n      __DEBUG_BUILD__ &&\n      // Don't call this for transactions\n      this.transaction &&\n      this.transaction.spanId !== this.spanId\n    ) {\n      const { logMessage } = this.transaction.metadata.spanMetadata[this.spanId];\n      if (logMessage) {\n        logger.log((logMessage as string).replace('Starting', 'Finishing'));\n      }\n    }\n\n    this.endTimestamp = typeof endTimestamp === 'number' ? endTimestamp : timestampInSeconds();\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public toTraceparent(): string {\n    return generateSentryTraceHeader(this.traceId, this.spanId, this.sampled);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public toContext(): SpanContext {\n    return dropUndefinedKeys({\n      data: this.data,\n      description: this.description,\n      endTimestamp: this.endTimestamp,\n      op: this.op,\n      parentSpanId: this.parentSpanId,\n      sampled: this.sampled,\n      spanId: this.spanId,\n      startTimestamp: this.startTimestamp,\n      status: this.status,\n      tags: this.tags,\n      traceId: this.traceId,\n    });\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public updateWithContext(spanContext: SpanContext): this {\n    this.data = spanContext.data || {};\n    this.description = spanContext.description;\n    this.endTimestamp = spanContext.endTimestamp;\n    this.op = spanContext.op;\n    this.parentSpanId = spanContext.parentSpanId;\n    this.sampled = spanContext.sampled;\n    this.spanId = spanContext.spanId || this.spanId;\n    this.startTimestamp = spanContext.startTimestamp || this.startTimestamp;\n    this.status = spanContext.status;\n    this.tags = spanContext.tags || {};\n    this.traceId = spanContext.traceId || this.traceId;\n\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getTraceContext(): TraceContext {\n    return dropUndefinedKeys({\n      data: Object.keys(this.data).length > 0 ? this.data : undefined,\n      description: this.description,\n      op: this.op,\n      parent_span_id: this.parentSpanId,\n      span_id: this.spanId,\n      status: this.status,\n      tags: Object.keys(this.tags).length > 0 ? this.tags : undefined,\n      trace_id: this.traceId,\n    });\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public toJSON(): {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    data?: { [key: string]: any };\n    description?: string;\n    op?: string;\n    parent_span_id?: string;\n    span_id: string;\n    start_timestamp: number;\n    status?: string;\n    tags?: { [key: string]: Primitive };\n    timestamp?: number;\n    trace_id: string;\n    origin?: SpanOrigin;\n  } {\n    return dropUndefinedKeys({\n      data: Object.keys(this.data).length > 0 ? this.data : undefined,\n      description: this.description,\n      op: this.op,\n      parent_span_id: this.parentSpanId,\n      span_id: this.spanId,\n      start_timestamp: this.startTimestamp,\n      status: this.status,\n      tags: Object.keys(this.tags).length > 0 ? this.tags : undefined,\n      timestamp: this.endTimestamp,\n      trace_id: this.traceId,\n      origin: this.origin,\n    });\n  }\n}\n\nexport type SpanStatusType =\n  /** The operation completed successfully. */\n  | 'ok'\n  /** Deadline expired before operation could complete. */\n  | 'deadline_exceeded'\n  /** 401 Unauthorized (actually does mean unauthenticated according to RFC 7235) */\n  | 'unauthenticated'\n  /** 403 Forbidden */\n  | 'permission_denied'\n  /** 404 Not Found. Some requested entity (file or directory) was not found. */\n  | 'not_found'\n  /** 429 Too Many Requests */\n  | 'resource_exhausted'\n  /** Client specified an invalid argument. 4xx. */\n  | 'invalid_argument'\n  /** 501 Not Implemented */\n  | 'unimplemented'\n  /** 503 Service Unavailable */\n  | 'unavailable'\n  /** Other/generic 5xx. */\n  | 'internal_error'\n  /** Unknown. Any non-standard HTTP status code. */\n  | 'unknown_error'\n  /** The operation was cancelled (typically by the user). */\n  | 'cancelled'\n  /** Already exists (409) */\n  | 'already_exists'\n  /** Operation was rejected because the system is not in a state required for the operation's */\n  | 'failed_precondition'\n  /** The operation was aborted, typically due to a concurrency issue. */\n  | 'aborted'\n  /** Operation was attempted past the valid range. */\n  | 'out_of_range'\n  /** Unrecoverable data loss or corruption */\n  | 'data_loss';\n\n/**\n * Converts a HTTP status code into a {@link SpanStatusType}.\n *\n * @param httpStatus The HTTP response status code.\n * @returns The span status or unknown_error.\n */\nexport function spanStatusfromHttpCode(httpStatus: number): SpanStatusType {\n  if (httpStatus < 400 && httpStatus >= 100) {\n    return 'ok';\n  }\n\n  if (httpStatus >= 400 && httpStatus < 500) {\n    switch (httpStatus) {\n      case 401:\n        return 'unauthenticated';\n      case 403:\n        return 'permission_denied';\n      case 404:\n        return 'not_found';\n      case 409:\n        return 'already_exists';\n      case 413:\n        return 'failed_precondition';\n      case 429:\n        return 'resource_exhausted';\n      default:\n        return 'invalid_argument';\n    }\n  }\n\n  if (httpStatus >= 500 && httpStatus < 600) {\n    switch (httpStatus) {\n      case 501:\n        return 'unimplemented';\n      case 503:\n        return 'unavailable';\n      case 504:\n        return 'deadline_exceeded';\n      default:\n        return 'internal_error';\n    }\n  }\n\n  return 'unknown_error';\n}\n", "import type { Client, DynamicSamplingContext, Scope } from '@sentry/types';\nimport { dropUndefinedKeys } from '@sentry/utils';\n\nimport { DEFAULT_ENVIRONMENT } from '../constants';\n\n/**\n * Creates a dynamic sampling context from a client.\n *\n * Dispatchs the `createDsc` lifecycle hook as a side effect.\n */\nexport function getDynamicSamplingContextFromClient(\n  trace_id: string,\n  client: Client,\n  scope?: Scope,\n): DynamicSamplingContext {\n  const options = client.getOptions();\n\n  const { publicKey: public_key } = client.getDsn() || {};\n  const { segment: user_segment } = (scope && scope.getUser()) || {};\n\n  const dsc = dropUndefinedKeys({\n    environment: options.environment || DEFAULT_ENVIRONMENT,\n    release: options.release,\n    user_segment,\n    public_key,\n    trace_id,\n  }) as DynamicSamplingContext;\n\n  client.emit && client.emit('createDsc', dsc);\n\n  return dsc;\n}\n", "import type {\n  Context,\n  Contexts,\n  DynamicSamplingContext,\n  Measurements,\n  MeasurementUnit,\n  Transaction as TransactionInterface,\n  TransactionContext,\n  TransactionEvent,\n  TransactionMetadata,\n} from '@sentry/types';\nimport { dropUndefinedKeys, logger } from '@sentry/utils';\n\nimport type { Hub } from '../hub';\nimport { getCurrentHub } from '../hub';\nimport { getDynamicSamplingContextFromClient } from './dynamicSamplingContext';\nimport { Span as SpanClass, SpanRecorder } from './span';\n\n/** JSDoc */\nexport class Transaction extends SpanClass implements TransactionInterface {\n  public metadata: TransactionMetadata;\n\n  /**\n   * The reference to the current hub.\n   */\n  public _hub: Hub;\n\n  private _name: string;\n\n  private _measurements: Measurements;\n\n  private _contexts: Contexts;\n\n  private _trimEnd?: boolean;\n\n  private _frozenDynamicSamplingContext: Readonly<Partial<DynamicSamplingContext>> | undefined;\n\n  /**\n   * This constructor should never be called manually. Those instrumenting tracing should use\n   * `Sentry.startTransaction()`, and internal methods should use `hub.startTransaction()`.\n   * @internal\n   * @hideconstructor\n   * @hidden\n   */\n  public constructor(transactionContext: TransactionContext, hub?: Hub) {\n    super(transactionContext);\n    // We need to delete description since it's set by the Span class constructor\n    // but not needed for transactions.\n    delete this.description;\n\n    this._measurements = {};\n    this._contexts = {};\n\n    this._hub = hub || getCurrentHub();\n\n    this._name = transactionContext.name || '';\n\n    this.metadata = {\n      source: 'custom',\n      ...transactionContext.metadata,\n      spanMetadata: {},\n    };\n\n    this._trimEnd = transactionContext.trimEnd;\n\n    // this is because transactions are also spans, and spans have a transaction pointer\n    this.transaction = this;\n\n    // If Dynamic Sampling Context is provided during the creation of the transaction, we freeze it as it usually means\n    // there is incoming Dynamic Sampling Context. (Either through an incoming request, a baggage meta-tag, or other means)\n    const incomingDynamicSamplingContext = this.metadata.dynamicSamplingContext;\n    if (incomingDynamicSamplingContext) {\n      // We shallow copy this in case anything writes to the original reference of the passed in `dynamicSamplingContext`\n      this._frozenDynamicSamplingContext = { ...incomingDynamicSamplingContext };\n    }\n  }\n\n  /** Getter for `name` property */\n  public get name(): string {\n    return this._name;\n  }\n\n  /** Setter for `name` property, which also sets `source` as custom */\n  public set name(newName: string) {\n    this.setName(newName);\n  }\n\n  /**\n   * JSDoc\n   */\n  public setName(name: string, source: TransactionMetadata['source'] = 'custom'): void {\n    this._name = name;\n    this.metadata.source = source;\n  }\n\n  /**\n   * Attaches SpanRecorder to the span itself\n   * @param maxlen maximum number of spans that can be recorded\n   */\n  public initSpanRecorder(maxlen: number = 1000): void {\n    if (!this.spanRecorder) {\n      this.spanRecorder = new SpanRecorder(maxlen);\n    }\n    this.spanRecorder.add(this);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setContext(key: string, context: Context | null): void {\n    if (context === null) {\n      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n      delete this._contexts[key];\n    } else {\n      this._contexts[key] = context;\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setMeasurement(name: string, value: number, unit: MeasurementUnit = ''): void {\n    this._measurements[name] = { value, unit };\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setMetadata(newMetadata: Partial<TransactionMetadata>): void {\n    this.metadata = { ...this.metadata, ...newMetadata };\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public finish(endTimestamp?: number): string | undefined {\n    const transaction = this._finishTransaction(endTimestamp);\n    if (!transaction) {\n      return undefined;\n    }\n    return this._hub.captureEvent(transaction);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public toContext(): TransactionContext {\n    const spanContext = super.toContext();\n\n    return dropUndefinedKeys({\n      ...spanContext,\n      name: this.name,\n      trimEnd: this._trimEnd,\n    });\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public updateWithContext(transactionContext: TransactionContext): this {\n    super.updateWithContext(transactionContext);\n\n    this.name = transactionContext.name || '';\n\n    this._trimEnd = transactionContext.trimEnd;\n\n    return this;\n  }\n\n  /**\n   * @inheritdoc\n   *\n   * @experimental\n   */\n  public getDynamicSamplingContext(): Readonly<Partial<DynamicSamplingContext>> {\n    if (this._frozenDynamicSamplingContext) {\n      return this._frozenDynamicSamplingContext;\n    }\n\n    const hub = this._hub || getCurrentHub();\n    const client = hub.getClient();\n\n    if (!client) return {};\n\n    const scope = hub.getScope();\n    const dsc = getDynamicSamplingContextFromClient(this.traceId, client, scope);\n\n    const maybeSampleRate = this.metadata.sampleRate;\n    if (maybeSampleRate !== undefined) {\n      dsc.sample_rate = `${maybeSampleRate}`;\n    }\n\n    // We don't want to have a transaction name in the DSC if the source is \"url\" because URLs might contain PII\n    const source = this.metadata.source;\n    if (source && source !== 'url') {\n      dsc.transaction = this.name;\n    }\n\n    if (this.sampled !== undefined) {\n      dsc.sampled = String(this.sampled);\n    }\n\n    // Uncomment if we want to make DSC immutable\n    // this._frozenDynamicSamplingContext = dsc;\n\n    return dsc;\n  }\n\n  /**\n   * Override the current hub with a new one.\n   * Used if you want another hub to finish the transaction.\n   *\n   * @internal\n   */\n  public setHub(hub: Hub): void {\n    this._hub = hub;\n  }\n\n  /**\n   * Finish the transaction & prepare the event to send to Sentry.\n   */\n  protected _finishTransaction(endTimestamp?: number): TransactionEvent | undefined {\n    // This transaction is already finished, so we should not flush it again.\n    if (this.endTimestamp !== undefined) {\n      return undefined;\n    }\n\n    if (!this.name) {\n      __DEBUG_BUILD__ && logger.warn('Transaction has no name, falling back to `<unlabeled transaction>`.');\n      this.name = '<unlabeled transaction>';\n    }\n\n    // just sets the end timestamp\n    super.finish(endTimestamp);\n\n    const client = this._hub.getClient();\n    if (client && client.emit) {\n      client.emit('finishTransaction', this);\n    }\n\n    if (this.sampled !== true) {\n      // At this point if `sampled !== true` we want to discard the transaction.\n      __DEBUG_BUILD__ && logger.log('[Tracing] Discarding transaction because its trace was not chosen to be sampled.');\n\n      if (client) {\n        client.recordDroppedEvent('sample_rate', 'transaction');\n      }\n\n      return undefined;\n    }\n\n    const finishedSpans = this.spanRecorder ? this.spanRecorder.spans.filter(s => s !== this && s.endTimestamp) : [];\n\n    if (this._trimEnd && finishedSpans.length > 0) {\n      this.endTimestamp = finishedSpans.reduce((prev: SpanClass, current: SpanClass) => {\n        if (prev.endTimestamp && current.endTimestamp) {\n          return prev.endTimestamp > current.endTimestamp ? prev : current;\n        }\n        return prev;\n      }).endTimestamp;\n    }\n\n    const metadata = this.metadata;\n\n    const transaction: TransactionEvent = {\n      contexts: {\n        ...this._contexts,\n        // We don't want to override trace context\n        trace: this.getTraceContext(),\n      },\n      spans: finishedSpans,\n      start_timestamp: this.startTimestamp,\n      tags: this.tags,\n      timestamp: this.endTimestamp,\n      transaction: this.name,\n      type: 'transaction',\n      sdkProcessingMetadata: {\n        ...metadata,\n        dynamicSamplingContext: this.getDynamicSamplingContext(),\n      },\n      ...(metadata.source && {\n        transaction_info: {\n          source: metadata.source,\n        },\n      }),\n    };\n\n    const hasMeasurements = Object.keys(this._measurements).length > 0;\n\n    if (hasMeasurements) {\n      __DEBUG_BUILD__ &&\n        logger.log(\n          '[Measurements] Adding measurements to transaction',\n          JSON.stringify(this._measurements, undefined, 2),\n        );\n      transaction.measurements = this._measurements;\n    }\n\n    __DEBUG_BUILD__ && logger.log(`[Tracing] Finishing ${this.op} transaction: ${this.name}.`);\n\n    return transaction;\n  }\n}\n", "import type { Options } from '@sentry/types';\n\nimport { getCurrentHub } from '../hub';\n\n// Treeshakable guard to remove all code related to tracing\ndeclare const __SENTRY_TRACING__: boolean | undefined;\n\n/**\n * Determines if tracing is currently enabled.\n *\n * Tracing is enabled when at least one of `tracesSampleRate` and `tracesSampler` is defined in the SDK config.\n */\nexport function hasTracingEnabled(\n  maybeOptions?: Pick<Options, 'tracesSampleRate' | 'tracesSampler' | 'enableTracing'> | undefined,\n): boolean {\n  if (typeof __SENTRY_TRACING__ === 'boolean' && !__SENTRY_TRACING__) {\n    return false;\n  }\n\n  const client = getCurrentHub().getClient();\n  const options = maybeOptions || (client && client.getOptions());\n  return !!options && (options.enableTracing || 'tracesSampleRate' in options || 'tracesSampler' in options);\n}\n", "import type { Options, SamplingContext } from '@sentry/types';\nimport { isNaN, logger } from '@sentry/utils';\n\nimport { hasTracingEnabled } from '../utils/hasTracingEnabled';\nimport type { Transaction } from './transaction';\n\n/**\n * Makes a sampling decision for the given transaction and stores it on the transaction.\n *\n * Called every time a transaction is created. Only transactions which emerge with a `sampled` value of `true` will be\n * sent to Sentry.\n *\n * This method muttes the given `transaction` and will set the `sampled` value on it.\n * It returns the same transaction, for convenience.\n */\nexport function sampleTransaction<T extends Transaction>(\n  transaction: T,\n  options: Pick<Options, 'tracesSampleRate' | 'tracesSampler' | 'enableTracing'>,\n  samplingContext: SamplingContext,\n): T {\n  // nothing to do if tracing is not enabled\n  if (!hasTracingEnabled(options)) {\n    transaction.sampled = false;\n    return transaction;\n  }\n\n  // if the user has forced a sampling decision by passing a `sampled` value in their transaction context, go with that\n  if (transaction.sampled !== undefined) {\n    transaction.setMetadata({\n      sampleRate: Number(transaction.sampled),\n    });\n    return transaction;\n  }\n\n  // we would have bailed already if neither `tracesSampler` nor `tracesSampleRate` nor `enableTracing` were defined, so one of these should\n  // work; prefer the hook if so\n  let sampleRate;\n  if (typeof options.tracesSampler === 'function') {\n    sampleRate = options.tracesSampler(samplingContext);\n    transaction.setMetadata({\n      sampleRate: Number(sampleRate),\n    });\n  } else if (samplingContext.parentSampled !== undefined) {\n    sampleRate = samplingContext.parentSampled;\n  } else if (typeof options.tracesSampleRate !== 'undefined') {\n    sampleRate = options.tracesSampleRate;\n    transaction.setMetadata({\n      sampleRate: Number(sampleRate),\n    });\n  } else {\n    // When `enableTracing === true`, we use a sample rate of 100%\n    sampleRate = 1;\n    transaction.setMetadata({\n      sampleRate,\n    });\n  }\n\n  // Since this is coming from the user (or from a function provided by the user), who knows what we might get. (The\n  // only valid values are booleans or numbers between 0 and 1.)\n  if (!isValidSampleRate(sampleRate)) {\n    __DEBUG_BUILD__ && logger.warn('[Tracing] Discarding transaction because of invalid sample rate.');\n    transaction.sampled = false;\n    return transaction;\n  }\n\n  // if the function returned 0 (or false), or if `tracesSampleRate` is 0, it's a sign the transaction should be dropped\n  if (!sampleRate) {\n    __DEBUG_BUILD__ &&\n      logger.log(\n        `[Tracing] Discarding transaction because ${\n          typeof options.tracesSampler === 'function'\n            ? 'tracesSampler returned 0 or false'\n            : 'a negative sampling decision was inherited or tracesSampleRate is set to 0'\n        }`,\n      );\n    transaction.sampled = false;\n    return transaction;\n  }\n\n  // Now we roll the dice. Math.random is inclusive of 0, but not of 1, so strict < is safe here. In case sampleRate is\n  // a boolean, the < comparison will cause it to be automatically cast to 1 if it's true and 0 if it's false.\n  transaction.sampled = Math.random() < (sampleRate as number | boolean);\n\n  // if we're not going to keep it, we're done\n  if (!transaction.sampled) {\n    __DEBUG_BUILD__ &&\n      logger.log(\n        `[Tracing] Discarding transaction because it's not included in the random sample (sampling rate = ${Number(\n          sampleRate,\n        )})`,\n      );\n    return transaction;\n  }\n\n  __DEBUG_BUILD__ && logger.log(`[Tracing] starting ${transaction.op} transaction - ${transaction.name}`);\n  return transaction;\n}\n\n/**\n * Checks the given sample rate to make sure it is valid type and value (a boolean, or a number between 0 and 1).\n */\nfunction isValidSampleRate(rate: unknown): boolean {\n  // we need to check NaN explicitly because it's of type 'number' and therefore wouldn't get caught by this typecheck\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  if (isNaN(rate) || !(typeof rate === 'number' || typeof rate === 'boolean')) {\n    __DEBUG_BUILD__ &&\n      logger.warn(\n        `[Tracing] Given sample rate is invalid. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(\n          rate,\n        )} of type ${JSON.stringify(typeof rate)}.`,\n      );\n    return false;\n  }\n\n  // in case sampleRate is a boolean, it will get automatically cast to 1 if it's true and 0 if it's false\n  if (rate < 0 || rate > 1) {\n    __DEBUG_BUILD__ &&\n      logger.warn(`[Tracing] Given sample rate is invalid. Sample rate must be between 0 and 1. Got ${rate}.`);\n    return false;\n  }\n  return true;\n}\n", "import type { ClientOptions, CustomSamplingContext, TransactionContext } from '@sentry/types';\nimport { logger } from '@sentry/utils';\n\nimport type { Hub } from '../hub';\nimport { getMainCarrier } from '../hub';\nimport { registerErrorInstrumentation } from './errors';\nimport { IdleTransaction } from './idletransaction';\nimport { sampleTransaction } from './sampling';\nimport { Transaction } from './transaction';\n\n/** Returns all trace headers that are currently on the top scope. */\nfunction traceHeaders(this: Hub): { [key: string]: string } {\n  const scope = this.getScope();\n  const span = scope.getSpan();\n\n  return span\n    ? {\n        'sentry-trace': span.toTraceparent(),\n      }\n    : {};\n}\n\n/**\n * Creates a new transaction and adds a sampling decision if it doesn't yet have one.\n *\n * The Hub.startTransaction method delegates to this method to do its work, passing the Hub instance in as `this`, as if\n * it had been called on the hub directly. Exists as a separate function so that it can be injected into the class as an\n * \"extension method.\"\n *\n * @param this: The Hub starting the transaction\n * @param transactionContext: Data used to configure the transaction\n * @param CustomSamplingContext: Optional data to be provided to the `tracesSampler` function (if any)\n *\n * @returns The new transaction\n *\n * @see {@link Hub.startTransaction}\n */\nfunction _startTransaction(\n  this: Hub,\n  transactionContext: TransactionContext,\n  customSamplingContext?: CustomSamplingContext,\n): Transaction {\n  const client = this.getClient();\n  const options: Partial<ClientOptions> = (client && client.getOptions()) || {};\n\n  const configInstrumenter = options.instrumenter || 'sentry';\n  const transactionInstrumenter = transactionContext.instrumenter || 'sentry';\n\n  if (configInstrumenter !== transactionInstrumenter) {\n    __DEBUG_BUILD__ &&\n      logger.error(\n        `A transaction was started with instrumenter=\\`${transactionInstrumenter}\\`, but the SDK is configured with the \\`${configInstrumenter}\\` instrumenter.\nThe transaction will not be sampled. Please use the ${configInstrumenter} instrumentation to start transactions.`,\n      );\n\n    transactionContext.sampled = false;\n  }\n\n  let transaction = new Transaction(transactionContext, this);\n  transaction = sampleTransaction(transaction, options, {\n    parentSampled: transactionContext.parentSampled,\n    transactionContext,\n    ...customSamplingContext,\n  });\n  if (transaction.sampled) {\n    transaction.initSpanRecorder(options._experiments && (options._experiments.maxSpans as number));\n  }\n  if (client && client.emit) {\n    client.emit('startTransaction', transaction);\n  }\n  return transaction;\n}\n\n/**\n * Create new idle transaction.\n */\nexport function startIdleTransaction(\n  hub: Hub,\n  transactionContext: TransactionContext,\n  idleTimeout: number,\n  finalTimeout: number,\n  onScope?: boolean,\n  customSamplingContext?: CustomSamplingContext,\n  heartbeatInterval?: number,\n): IdleTransaction {\n  const client = hub.getClient();\n  const options: Partial<ClientOptions> = (client && client.getOptions()) || {};\n\n  let transaction = new IdleTransaction(transactionContext, hub, idleTimeout, finalTimeout, heartbeatInterval, onScope);\n  transaction = sampleTransaction(transaction, options, {\n    parentSampled: transactionContext.parentSampled,\n    transactionContext,\n    ...customSamplingContext,\n  });\n  if (transaction.sampled) {\n    transaction.initSpanRecorder(options._experiments && (options._experiments.maxSpans as number));\n  }\n  if (client && client.emit) {\n    client.emit('startTransaction', transaction);\n  }\n  return transaction;\n}\n\n/**\n * Adds tracing extensions to the global hub.\n */\nexport function addTracingExtensions(): void {\n  const carrier = getMainCarrier();\n  if (!carrier.__SENTRY__) {\n    return;\n  }\n  carrier.__SENTRY__.extensions = carrier.__SENTRY__.extensions || {};\n  if (!carrier.__SENTRY__.extensions.startTransaction) {\n    carrier.__SENTRY__.extensions.startTransaction = _startTransaction;\n  }\n  if (!carrier.__SENTRY__.extensions.traceHeaders) {\n    carrier.__SENTRY__.extensions.traceHeaders = traceHeaders;\n  }\n\n  registerErrorInstrumentation();\n}\n", "import type {\n  AggregationCounts,\n  Client,\n  RequestSessionStatus,\n  SessionAggregates,\n  SessionFlusherLike,\n} from '@sentry/types';\nimport { dropUndefinedKeys } from '@sentry/utils';\n\nimport { getCurrentHub } from './hub';\n\ntype ReleaseHealthAttributes = {\n  environment?: string;\n  release: string;\n};\n\n/**\n * @inheritdoc\n */\nexport class Session<PERSON><PERSON>her implements SessionF<PERSON>herLike {\n  public readonly flushTimeout: number;\n  private _pendingAggregates: Record<number, AggregationCounts>;\n  private _sessionAttrs: ReleaseHealthAttributes;\n  private _intervalId: ReturnType<typeof setInterval>;\n  private _isEnabled: boolean;\n  private _client: Client;\n\n  public constructor(client: Client, attrs: ReleaseHealthAttributes) {\n    this._client = client;\n    this.flushTimeout = 60;\n    this._pendingAggregates = {};\n    this._isEnabled = true;\n\n    // Call to setInterval, so that flush is called every 60 seconds\n    this._intervalId = setInterval(() => this.flush(), this.flushTimeout * 1000);\n    this._sessionAttrs = attrs;\n  }\n\n  /** Checks if `pendingAggregates` has entries, and if it does flushes them by calling `sendSession` */\n  public flush(): void {\n    const sessionAggregates = this.getSessionAggregates();\n    if (sessionAggregates.aggregates.length === 0) {\n      return;\n    }\n    this._pendingAggregates = {};\n    this._client.sendSession(sessionAggregates);\n  }\n\n  /** Massages the entries in `pendingAggregates` and returns aggregated sessions */\n  public getSessionAggregates(): SessionAggregates {\n    const aggregates: AggregationCounts[] = Object.keys(this._pendingAggregates).map((key: string) => {\n      return this._pendingAggregates[parseInt(key)];\n    });\n\n    const sessionAggregates: SessionAggregates = {\n      attrs: this._sessionAttrs,\n      aggregates,\n    };\n    return dropUndefinedKeys(sessionAggregates);\n  }\n\n  /** JSDoc */\n  public close(): void {\n    clearInterval(this._intervalId);\n    this._isEnabled = false;\n    this.flush();\n  }\n\n  /**\n   * Wrapper function for _incrementSessionStatusCount that checks if the instance of SessionFlusher is enabled then\n   * fetches the session status of the request from `Scope.getRequestSession().status` on the scope and passes them to\n   * `_incrementSessionStatusCount` along with the start date\n   */\n  public incrementSessionStatusCount(): void {\n    if (!this._isEnabled) {\n      return;\n    }\n    const scope = getCurrentHub().getScope();\n    const requestSession = scope.getRequestSession();\n\n    if (requestSession && requestSession.status) {\n      this._incrementSessionStatusCount(requestSession.status, new Date());\n      // This is not entirely necessarily but is added as a safe guard to indicate the bounds of a request and so in\n      // case captureRequestSession is called more than once to prevent double count\n      scope.setRequestSession(undefined);\n      /* eslint-enable @typescript-eslint/no-unsafe-member-access */\n    }\n  }\n\n  /**\n   * Increments status bucket in pendingAggregates buffer (internal state) corresponding to status of\n   * the session received\n   */\n  private _incrementSessionStatusCount(status: RequestSessionStatus, date: Date): number {\n    // Truncate minutes and seconds on Session Started attribute to have one minute bucket keys\n    const sessionStartedTrunc = new Date(date).setSeconds(0, 0);\n    this._pendingAggregates[sessionStartedTrunc] = this._pendingAggregates[sessionStartedTrunc] || {};\n\n    // corresponds to aggregated sessions in one specific minute bucket\n    // for example, {\"started\":\"2021-03-16T08:00:00.000Z\",\"exited\":4, \"errored\": 1}\n    const aggregationCounts: AggregationCounts = this._pendingAggregates[sessionStartedTrunc];\n    if (!aggregationCounts.started) {\n      aggregationCounts.started = new Date(sessionStartedTrunc).toISOString();\n    }\n\n    switch (status) {\n      case 'errored':\n        aggregationCounts.errored = (aggregationCounts.errored || 0) + 1;\n        return aggregationCounts.errored;\n      case 'ok':\n        aggregationCounts.exited = (aggregationCounts.exited || 0) + 1;\n        return aggregationCounts.exited;\n      default:\n        aggregationCounts.crashed = (aggregationCounts.crashed || 0) + 1;\n        return aggregationCounts.crashed;\n    }\n  }\n}\n", "import type { ClientOptions, DsnComponents, Dsn<PERSON>ike, SdkInfo } from '@sentry/types';\nimport { dsnToString, makeDsn, urlEncode } from '@sentry/utils';\n\nconst SENTRY_API_VERSION = '7';\n\n/** Returns the prefix to construct Sentry ingestion API endpoints. */\nfunction getBaseApiEndpoint(dsn: DsnComponents): string {\n  const protocol = dsn.protocol ? `${dsn.protocol}:` : '';\n  const port = dsn.port ? `:${dsn.port}` : '';\n  return `${protocol}//${dsn.host}${port}${dsn.path ? `/${dsn.path}` : ''}/api/`;\n}\n\n/** Returns the ingest API endpoint for target. */\nfunction _getIngestEndpoint(dsn: DsnComponents): string {\n  return `${getBaseApiEndpoint(dsn)}${dsn.projectId}/envelope/`;\n}\n\n/** Returns a URL-encoded string with auth config suitable for a query string. */\nfunction _encodedAuth(dsn: DsnComponents, sdkInfo: SdkInfo | undefined): string {\n  return urlEncode({\n    // We send only the minimum set of required information. See\n    // https://github.com/getsentry/sentry-javascript/issues/2572.\n    sentry_key: dsn.publicKey,\n    sentry_version: SENTRY_API_VERSION,\n    ...(sdkInfo && { sentry_client: `${sdkInfo.name}/${sdkInfo.version}` }),\n  });\n}\n\n/**\n * Returns the envelope endpoint URL with auth in the query string.\n *\n * Sending auth as part of the query string and not as custom HTTP headers avoids CORS preflight requests.\n */\nexport function getEnvelopeEndpointWithUrlEncodedAuth(\n  dsn: DsnComponents,\n  // TODO (v8): Remove `tunnelOrOptions` in favor of `options`, and use the substitute code below\n  // options: ClientOptions = {} as ClientOptions,\n  tunnelOrOptions: string | ClientOptions = {} as ClientOptions,\n): string {\n  // TODO (v8): Use this code instead\n  // const { tunnel, _metadata = {} } = options;\n  // return tunnel ? tunnel : `${_getIngestEndpoint(dsn)}?${_encodedAuth(dsn, _metadata.sdk)}`;\n\n  const tunnel = typeof tunnelOrOptions === 'string' ? tunnelOrOptions : tunnelOrOptions.tunnel;\n  const sdkInfo =\n    typeof tunnelOrOptions === 'string' || !tunnelOrOptions._metadata ? undefined : tunnelOrOptions._metadata.sdk;\n\n  return tunnel ? tunnel : `${_getIngestEndpoint(dsn)}?${_encodedAuth(dsn, sdkInfo)}`;\n}\n\n/** Returns the url to the report dialog endpoint. */\nexport function getReportDialogEndpoint(\n  dsnLike: DsnLike,\n  dialogOptions: {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    [key: string]: any;\n    user?: { name?: string; email?: string };\n  },\n): string {\n  const dsn = makeDsn(dsnLike);\n  if (!dsn) {\n    return '';\n  }\n\n  const endpoint = `${getBaseApiEndpoint(dsn)}embed/error-page/`;\n\n  let encodedOptions = `dsn=${dsnToString(dsn)}`;\n  for (const key in dialogOptions) {\n    if (key === 'dsn') {\n      continue;\n    }\n\n    if (key === 'user') {\n      const user = dialogOptions.user;\n      if (!user) {\n        continue;\n      }\n      if (user.name) {\n        encodedOptions += `&name=${encodeURIComponent(user.name)}`;\n      }\n      if (user.email) {\n        encodedOptions += `&email=${encodeURIComponent(user.email)}`;\n      }\n    } else {\n      encodedOptions += `&${encodeURIComponent(key)}=${encodeURIComponent(dialogOptions[key] as string)}`;\n    }\n  }\n\n  return `${endpoint}?${encodedOptions}`;\n}\n", "import type {\n  DsnComponents,\n  Event,\n  EventEnvelope,\n  EventItem,\n  SdkInfo,\n  SdkMetadata,\n  Session,\n  SessionAggregates,\n  SessionEnvelope,\n  SessionItem,\n} from '@sentry/types';\nimport {\n  createEnvelope,\n  createEventEnvelopeHeaders,\n  dsnToString,\n  getSdkMetadataForEnvelopeHeader,\n} from '@sentry/utils';\n\n/**\n * Apply SdkInfo (name, version, packages, integrations) to the corresponding event key.\n * Merge with existing data if any.\n **/\nfunction enhanceEventWithSdkInfo(event: Event, sdkInfo?: SdkInfo): Event {\n  if (!sdkInfo) {\n    return event;\n  }\n  event.sdk = event.sdk || {};\n  event.sdk.name = event.sdk.name || sdkInfo.name;\n  event.sdk.version = event.sdk.version || sdkInfo.version;\n  event.sdk.integrations = [...(event.sdk.integrations || []), ...(sdkInfo.integrations || [])];\n  event.sdk.packages = [...(event.sdk.packages || []), ...(sdkInfo.packages || [])];\n  return event;\n}\n\n/** Creates an envelope from a Session */\nexport function createSessionEnvelope(\n  session: Session | SessionAggregates,\n  dsn?: DsnComponents,\n  metadata?: SdkMetadata,\n  tunnel?: string,\n): SessionEnvelope {\n  const sdkInfo = getSdkMetadataForEnvelopeHeader(metadata);\n  const envelopeHeaders = {\n    sent_at: new Date().toISOString(),\n    ...(sdkInfo && { sdk: sdkInfo }),\n    ...(!!tunnel && dsn && { dsn: dsnToString(dsn) }),\n  };\n\n  const envelopeItem: SessionItem =\n    'aggregates' in session ? [{ type: 'sessions' }, session] : [{ type: 'session' }, session.toJSON()];\n\n  return createEnvelope<SessionEnvelope>(envelopeHeaders, [envelopeItem]);\n}\n\n/**\n * Create an Envelope from an event.\n */\nexport function createEventEnvelope(\n  event: Event,\n  dsn?: DsnComponents,\n  metadata?: SdkMetadata,\n  tunnel?: string,\n): EventEnvelope {\n  const sdkInfo = getSdkMetadataForEnvelopeHeader(metadata);\n\n  /*\n    Note: Due to TS, event.type may be `replay_event`, theoretically.\n    In practice, we never call `createEventEnvelope` with `replay_event` type,\n    and we'd have to adjut a looot of types to make this work properly.\n    We want to avoid casting this around, as that could lead to bugs (e.g. when we add another type)\n    So the safe choice is to really guard against the replay_event type here.\n  */\n  const eventType = event.type && event.type !== 'replay_event' ? event.type : 'event';\n\n  enhanceEventWithSdkInfo(event, metadata && metadata.sdk);\n\n  const envelopeHeaders = createEventEnvelopeHeaders(event, sdkInfo, tunnel, dsn);\n\n  // Prevent this data (which, if it exists, was used in earlier steps in the processing pipeline) from being sent to\n  // sentry. (Note: Our use of this property comes and goes with whatever we might be debugging, whatever hacks we may\n  // have temporarily added, etc. Even if we don't happen to be using it at some point in the future, let's not get rid\n  // of this `delete`, lest we miss putting it back in the next time the property is in use.)\n  delete event.sdkProcessingMetadata;\n\n  const eventItem: EventItem = [{ type: eventType }, event];\n  return createEnvelope<EventEnvelope>(envelopeHeaders, [eventItem]);\n}\n", "import type { Client, Event, EventHint, Integration, Options } from '@sentry/types';\nimport { arrayify, logger } from '@sentry/utils';\n\nimport { addGlobalEventProcessor } from './eventProcessors';\nimport { getCurrentHub } from './hub';\n\ndeclare module '@sentry/types' {\n  interface Integration {\n    isDefaultInstance?: boolean;\n  }\n}\n\nexport const installedIntegrations: string[] = [];\n\n/** Map of integrations assigned to a client */\nexport type IntegrationIndex = {\n  [key: string]: Integration;\n};\n\n/**\n * Remove duplicates from the given array, preferring the last instance of any duplicate. Not guaranteed to\n * preseve the order of integrations in the array.\n *\n * @private\n */\nfunction filterDuplicates(integrations: Integration[]): Integration[] {\n  const integrationsByName: { [key: string]: Integration } = {};\n\n  integrations.forEach(currentInstance => {\n    const { name } = currentInstance;\n\n    const existingInstance = integrationsByName[name];\n\n    // We want integrations later in the array to overwrite earlier ones of the same type, except that we never want a\n    // default instance to overwrite an existing user instance\n    if (existingInstance && !existingInstance.isDefaultInstance && currentInstance.isDefaultInstance) {\n      return;\n    }\n\n    integrationsByName[name] = currentInstance;\n  });\n\n  return Object.keys(integrationsByName).map(k => integrationsByName[k]);\n}\n\n/** Gets integrations to install */\nexport function getIntegrationsToSetup(options: Options): Integration[] {\n  const defaultIntegrations = options.defaultIntegrations || [];\n  const userIntegrations = options.integrations;\n\n  // We flag default instances, so that later we can tell them apart from any user-created instances of the same class\n  defaultIntegrations.forEach(integration => {\n    integration.isDefaultInstance = true;\n  });\n\n  let integrations: Integration[];\n\n  if (Array.isArray(userIntegrations)) {\n    integrations = [...defaultIntegrations, ...userIntegrations];\n  } else if (typeof userIntegrations === 'function') {\n    integrations = arrayify(userIntegrations(defaultIntegrations));\n  } else {\n    integrations = defaultIntegrations;\n  }\n\n  const finalIntegrations = filterDuplicates(integrations);\n\n  // The `Debug` integration prints copies of the `event` and `hint` which will be passed to `beforeSend` or\n  // `beforeSendTransaction`. It therefore has to run after all other integrations, so that the changes of all event\n  // processors will be reflected in the printed values. For lack of a more elegant way to guarantee that, we therefore\n  // locate it and, assuming it exists, pop it out of its current spot and shove it onto the end of the array.\n  const debugIndex = findIndex(finalIntegrations, integration => integration.name === 'Debug');\n  if (debugIndex !== -1) {\n    const [debugInstance] = finalIntegrations.splice(debugIndex, 1);\n    finalIntegrations.push(debugInstance);\n  }\n\n  return finalIntegrations;\n}\n\n/**\n * Given a list of integration instances this installs them all. When `withDefaults` is set to `true` then all default\n * integrations are added unless they were already provided before.\n * @param integrations array of integration instances\n * @param withDefault should enable default integrations\n */\nexport function setupIntegrations(client: Client, integrations: Integration[]): IntegrationIndex {\n  const integrationIndex: IntegrationIndex = {};\n\n  integrations.forEach(integration => {\n    // guard against empty provided integrations\n    if (integration) {\n      setupIntegration(client, integration, integrationIndex);\n    }\n  });\n\n  return integrationIndex;\n}\n\n/** Setup a single integration.  */\nexport function setupIntegration(client: Client, integration: Integration, integrationIndex: IntegrationIndex): void {\n  integrationIndex[integration.name] = integration;\n\n  if (installedIntegrations.indexOf(integration.name) === -1) {\n    integration.setupOnce(addGlobalEventProcessor, getCurrentHub);\n    installedIntegrations.push(integration.name);\n  }\n\n  if (client.on && typeof integration.preprocessEvent === 'function') {\n    const callback = integration.preprocessEvent.bind(integration) as typeof integration.preprocessEvent;\n    client.on('preprocessEvent', (event, hint) => callback(event, hint, client));\n  }\n\n  if (client.addEventProcessor && typeof integration.processEvent === 'function') {\n    const callback = integration.processEvent.bind(integration) as typeof integration.processEvent;\n\n    const processor = Object.assign((event: Event, hint: EventHint) => callback(event, hint, client), {\n      id: integration.name,\n    });\n\n    client.addEventProcessor(processor);\n  }\n\n  __DEBUG_BUILD__ && logger.log(`Integration installed: ${integration.name}`);\n}\n\n/** Add an integration to the current hub's client. */\nexport function addIntegration(integration: Integration): void {\n  const client = getCurrentHub().getClient();\n\n  if (!client || !client.addIntegration) {\n    __DEBUG_BUILD__ && logger.warn(`Cannot add integration \"${integration.name}\" because no SDK Client is available.`);\n    return;\n  }\n\n  client.addIntegration(integration);\n}\n\n// Polyfill for Array.findIndex(), which is not supported in ES5\nfunction findIndex<T>(arr: T[], callback: (item: T) => boolean): number {\n  for (let i = 0; i < arr.length; i++) {\n    if (callback(arr[i]) === true) {\n      return i;\n    }\n  }\n\n  return -1;\n}\n", "import type { Client, ClientOptions, Event, EventHint, <PERSON>ack<PERSON>rame, StackParser } from '@sentry/types';\nimport { dateTimestampInSeconds, GLOBAL_OBJ, normalize, resolvedSyncPromise, truncate, uuid4 } from '@sentry/utils';\n\nimport { DEFAULT_ENVIRONMENT } from '../constants';\nimport { getGlobalEventProcessors, notifyEventProcessors } from '../eventProcessors';\nimport { Scope } from '../scope';\n\n/**\n * Adds common information to events.\n *\n * The information includes release and environment from `options`,\n * breadcrumbs and context (extra, tags and user) from the scope.\n *\n * Information that is already present in the event is never overwritten. For\n * nested objects, such as the context, keys are merged.\n *\n * Note: This also triggers callbacks for `addGlobalEventProcessor`, but not `beforeSend`.\n *\n * @param event The original event.\n * @param hint May contain additional information about the original exception.\n * @param scope A scope containing event metadata.\n * @returns A new event with more information.\n * @hidden\n */\nexport function prepareEvent(\n  options: ClientOptions,\n  event: Event,\n  hint: EventHint,\n  scope?: Scope,\n  client?: Client,\n): PromiseLike<Event | null> {\n  const { normalizeDepth = 3, normalizeMaxBreadth = 1_000 } = options;\n  const prepared: Event = {\n    ...event,\n    event_id: event.event_id || hint.event_id || uuid4(),\n    timestamp: event.timestamp || dateTimestampInSeconds(),\n  };\n  const integrations = hint.integrations || options.integrations.map(i => i.name);\n\n  applyClientOptions(prepared, options);\n  applyIntegrationsMetadata(prepared, integrations);\n\n  // Only put debug IDs onto frames for error events.\n  if (event.type === undefined) {\n    applyDebugIds(prepared, options.stackParser);\n  }\n\n  // If we have scope given to us, use it as the base for further modifications.\n  // This allows us to prevent unnecessary copying of data if `captureContext` is not provided.\n  let finalScope = scope;\n  if (hint.captureContext) {\n    finalScope = Scope.clone(finalScope).update(hint.captureContext);\n  }\n\n  // We prepare the result here with a resolved Event.\n  let result = resolvedSyncPromise<Event | null>(prepared);\n\n  const clientEventProcessors = client && client.getEventProcessors ? client.getEventProcessors() : [];\n\n  // This should be the last thing called, since we want that\n  // {@link Hub.addEventProcessor} gets the finished prepared event.\n  //\n  // We need to check for the existence of `finalScope.getAttachments`\n  // because `getAttachments` can be undefined if users are using an older version\n  // of `@sentry/core` that does not have the `getAttachments` method.\n  // See: https://github.com/getsentry/sentry-javascript/issues/5229\n  if (finalScope) {\n    // Collect attachments from the hint and scope\n    if (finalScope.getAttachments) {\n      const attachments = [...(hint.attachments || []), ...finalScope.getAttachments()];\n\n      if (attachments.length) {\n        hint.attachments = attachments;\n      }\n    }\n\n    // In case we have a hub we reassign it.\n    result = finalScope.applyToEvent(prepared, hint, clientEventProcessors);\n  } else {\n    // Apply client & global event processors even if there is no scope\n    // TODO (v8): Update the order to be Global > Client\n    result = notifyEventProcessors([...clientEventProcessors, ...getGlobalEventProcessors()], prepared, hint);\n  }\n\n  return result.then(evt => {\n    if (evt) {\n      // We apply the debug_meta field only after all event processors have ran, so that if any event processors modified\n      // file names (e.g.the RewriteFrames integration) the filename -> debug ID relationship isn't destroyed.\n      // This should not cause any PII issues, since we're only moving data that is already on the event and not adding\n      // any new data\n      applyDebugMeta(evt);\n    }\n\n    if (typeof normalizeDepth === 'number' && normalizeDepth > 0) {\n      return normalizeEvent(evt, normalizeDepth, normalizeMaxBreadth);\n    }\n    return evt;\n  });\n}\n\n/**\n *  Enhances event using the client configuration.\n *  It takes care of all \"static\" values like environment, release and `dist`,\n *  as well as truncating overly long values.\n * @param event event instance to be enhanced\n */\nfunction applyClientOptions(event: Event, options: ClientOptions): void {\n  const { environment, release, dist, maxValueLength = 250 } = options;\n\n  if (!('environment' in event)) {\n    event.environment = 'environment' in options ? environment : DEFAULT_ENVIRONMENT;\n  }\n\n  if (event.release === undefined && release !== undefined) {\n    event.release = release;\n  }\n\n  if (event.dist === undefined && dist !== undefined) {\n    event.dist = dist;\n  }\n\n  if (event.message) {\n    event.message = truncate(event.message, maxValueLength);\n  }\n\n  const exception = event.exception && event.exception.values && event.exception.values[0];\n  if (exception && exception.value) {\n    exception.value = truncate(exception.value, maxValueLength);\n  }\n\n  const request = event.request;\n  if (request && request.url) {\n    request.url = truncate(request.url, maxValueLength);\n  }\n}\n\nconst debugIdStackParserCache = new WeakMap<StackParser, Map<string, StackFrame[]>>();\n\n/**\n * Puts debug IDs into the stack frames of an error event.\n */\nexport function applyDebugIds(event: Event, stackParser: StackParser): void {\n  const debugIdMap = GLOBAL_OBJ._sentryDebugIds;\n\n  if (!debugIdMap) {\n    return;\n  }\n\n  let debugIdStackFramesCache: Map<string, StackFrame[]>;\n  const cachedDebugIdStackFrameCache = debugIdStackParserCache.get(stackParser);\n  if (cachedDebugIdStackFrameCache) {\n    debugIdStackFramesCache = cachedDebugIdStackFrameCache;\n  } else {\n    debugIdStackFramesCache = new Map<string, StackFrame[]>();\n    debugIdStackParserCache.set(stackParser, debugIdStackFramesCache);\n  }\n\n  // Build a map of filename -> debug_id\n  const filenameDebugIdMap = Object.keys(debugIdMap).reduce<Record<string, string>>((acc, debugIdStackTrace) => {\n    let parsedStack: StackFrame[];\n    const cachedParsedStack = debugIdStackFramesCache.get(debugIdStackTrace);\n    if (cachedParsedStack) {\n      parsedStack = cachedParsedStack;\n    } else {\n      parsedStack = stackParser(debugIdStackTrace);\n      debugIdStackFramesCache.set(debugIdStackTrace, parsedStack);\n    }\n\n    for (let i = parsedStack.length - 1; i >= 0; i--) {\n      const stackFrame = parsedStack[i];\n      if (stackFrame.filename) {\n        acc[stackFrame.filename] = debugIdMap[debugIdStackTrace];\n        break;\n      }\n    }\n    return acc;\n  }, {});\n\n  try {\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    event!.exception!.values!.forEach(exception => {\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      exception.stacktrace!.frames!.forEach(frame => {\n        if (frame.filename) {\n          frame.debug_id = filenameDebugIdMap[frame.filename];\n        }\n      });\n    });\n  } catch (e) {\n    // To save bundle size we're just try catching here instead of checking for the existence of all the different objects.\n  }\n}\n\n/**\n * Moves debug IDs from the stack frames of an error event into the debug_meta field.\n */\nexport function applyDebugMeta(event: Event): void {\n  // Extract debug IDs and filenames from the stack frames on the event.\n  const filenameDebugIdMap: Record<string, string> = {};\n  try {\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    event.exception!.values!.forEach(exception => {\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      exception.stacktrace!.frames!.forEach(frame => {\n        if (frame.debug_id) {\n          if (frame.abs_path) {\n            filenameDebugIdMap[frame.abs_path] = frame.debug_id;\n          } else if (frame.filename) {\n            filenameDebugIdMap[frame.filename] = frame.debug_id;\n          }\n          delete frame.debug_id;\n        }\n      });\n    });\n  } catch (e) {\n    // To save bundle size we're just try catching here instead of checking for the existence of all the different objects.\n  }\n\n  if (Object.keys(filenameDebugIdMap).length === 0) {\n    return;\n  }\n\n  // Fill debug_meta information\n  event.debug_meta = event.debug_meta || {};\n  event.debug_meta.images = event.debug_meta.images || [];\n  const images = event.debug_meta.images;\n  Object.keys(filenameDebugIdMap).forEach(filename => {\n    images.push({\n      type: 'sourcemap',\n      code_file: filename,\n      debug_id: filenameDebugIdMap[filename],\n    });\n  });\n}\n\n/**\n * This function adds all used integrations to the SDK info in the event.\n * @param event The event that will be filled with all integrations.\n */\nfunction applyIntegrationsMetadata(event: Event, integrationNames: string[]): void {\n  if (integrationNames.length > 0) {\n    event.sdk = event.sdk || {};\n    event.sdk.integrations = [...(event.sdk.integrations || []), ...integrationNames];\n  }\n}\n\n/**\n * Applies `normalize` function on necessary `Event` attributes to make them safe for serialization.\n * Normalized keys:\n * - `breadcrumbs.data`\n * - `user`\n * - `contexts`\n * - `extra`\n * @param event Event\n * @returns Normalized event\n */\nfunction normalizeEvent(event: Event | null, depth: number, maxBreadth: number): Event | null {\n  if (!event) {\n    return null;\n  }\n\n  const normalized: Event = {\n    ...event,\n    ...(event.breadcrumbs && {\n      breadcrumbs: event.breadcrumbs.map(b => ({\n        ...b,\n        ...(b.data && {\n          data: normalize(b.data, depth, maxBreadth),\n        }),\n      })),\n    }),\n    ...(event.user && {\n      user: normalize(event.user, depth, maxBreadth),\n    }),\n    ...(event.contexts && {\n      contexts: normalize(event.contexts, depth, maxBreadth),\n    }),\n    ...(event.extra && {\n      extra: normalize(event.extra, depth, maxBreadth),\n    }),\n  };\n\n  // event.contexts.trace stores information about a Transaction. Similarly,\n  // event.spans[] stores information about child Spans. Given that a\n  // Transaction is conceptually a Span, normalization should apply to both\n  // Transactions and Spans consistently.\n  // For now the decision is to skip normalization of Transactions and Spans,\n  // so this block overwrites the normalized event to add back the original\n  // Transaction information prior to normalization.\n  if (event.contexts && event.contexts.trace && normalized.contexts) {\n    normalized.contexts.trace = event.contexts.trace;\n\n    // event.contexts.trace.data may contain circular/dangerous data so we need to normalize it\n    if (event.contexts.trace.data) {\n      normalized.contexts.trace.data = normalize(event.contexts.trace.data, depth, maxBreadth);\n    }\n  }\n\n  // event.spans[].data may contain circular/dangerous data so we need to normalize it\n  if (event.spans) {\n    normalized.spans = event.spans.map(span => {\n      // We cannot use the spread operator here because `toJSON` on `span` is non-enumerable\n      if (span.data) {\n        span.data = normalize(span.data, depth, maxBreadth);\n      }\n      return span;\n    });\n  }\n\n  return normalized;\n}\n", "/* eslint-disable max-lines */\nimport type {\n  <PERSON><PERSON><PERSON>rumb,\n  BreadcrumbHint,\n  Client,\n  ClientOptions,\n  DataCategory,\n  DsnComponents,\n  DynamicSamplingContext,\n  Envelope,\n  ErrorEvent,\n  Event,\n  EventDropReason,\n  EventHint,\n  EventProcessor,\n  Integration,\n  IntegrationClass,\n  Outcome,\n  PropagationContext,\n  SdkMetadata,\n  Session,\n  SessionAggregates,\n  Severity,\n  SeverityLevel,\n  Transaction,\n  TransactionEvent,\n  Transport,\n  TransportMakeRequestResponse,\n} from '@sentry/types';\nimport {\n  addItemToEnvelope,\n  checkOrSetAlreadyCaught,\n  createAttachmentEnvelopeItem,\n  isPlainObject,\n  isPrimitive,\n  isThenable,\n  logger,\n  makeDsn,\n  rejectedSyncPromise,\n  resolvedSyncPromise,\n  SentryError,\n  SyncPromise,\n} from '@sentry/utils';\n\nimport { getEnvelopeEndpointWithUrlEncodedAuth } from './api';\nimport { createEventEnvelope, createSessionEnvelope } from './envelope';\nimport type { IntegrationIndex } from './integration';\nimport { setupIntegration, setupIntegrations } from './integration';\nimport type { Scope } from './scope';\nimport { updateSession } from './session';\nimport { getDynamicSamplingContextFromClient } from './tracing/dynamicSamplingContext';\nimport { prepareEvent } from './utils/prepareEvent';\n\nconst ALREADY_SEEN_ERROR = \"Not capturing exception because it's already been captured.\";\n\n/**\n * Base implementation for all JavaScript SDK clients.\n *\n * Call the constructor with the corresponding options\n * specific to the client subclass. To access these options later, use\n * {@link Client.getOptions}.\n *\n * If a Dsn is specified in the options, it will be parsed and stored. Use\n * {@link Client.getDsn} to retrieve the Dsn at any moment. In case the Dsn is\n * invalid, the constructor will throw a {@link SentryException}. Note that\n * without a valid Dsn, the SDK will not send any events to Sentry.\n *\n * Before sending an event, it is passed through\n * {@link BaseClient._prepareEvent} to add SDK information and scope data\n * (breadcrumbs and context). To add more custom information, override this\n * method and extend the resulting prepared event.\n *\n * To issue automatically created events (e.g. via instrumentation), use\n * {@link Client.captureEvent}. It will prepare the event and pass it through\n * the callback lifecycle. To issue auto-breadcrumbs, use\n * {@link Client.addBreadcrumb}.\n *\n * @example\n * class NodeClient extends BaseClient<NodeOptions> {\n *   public constructor(options: NodeOptions) {\n *     super(options);\n *   }\n *\n *   // ...\n * }\n */\nexport abstract class BaseClient<O extends ClientOptions> implements Client<O> {\n  /** Options passed to the SDK. */\n  protected readonly _options: O;\n\n  /** The client Dsn, if specified in options. Without this Dsn, the SDK will be disabled. */\n  protected readonly _dsn?: DsnComponents;\n\n  protected readonly _transport?: Transport;\n\n  /** Array of set up integrations. */\n  protected _integrations: IntegrationIndex;\n\n  /** Indicates whether this client's integrations have been set up. */\n  protected _integrationsInitialized: boolean;\n\n  /** Number of calls being processed */\n  protected _numProcessing: number;\n\n  /** Holds flushable  */\n  private _outcomes: { [key: string]: number };\n\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  private _hooks: Record<string, Function[]>;\n\n  private _eventProcessors: EventProcessor[];\n\n  /**\n   * Initializes this client instance.\n   *\n   * @param options Options for the client.\n   */\n  protected constructor(options: O) {\n    this._options = options;\n    this._integrations = {};\n    this._integrationsInitialized = false;\n    this._numProcessing = 0;\n    this._outcomes = {};\n    this._hooks = {};\n    this._eventProcessors = [];\n\n    if (options.dsn) {\n      this._dsn = makeDsn(options.dsn);\n    } else {\n      __DEBUG_BUILD__ && logger.warn('No DSN provided, client will not send events.');\n    }\n\n    if (this._dsn) {\n      const url = getEnvelopeEndpointWithUrlEncodedAuth(this._dsn, options);\n      this._transport = options.transport({\n        recordDroppedEvent: this.recordDroppedEvent.bind(this),\n        ...options.transportOptions,\n        url,\n      });\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types\n  public captureException(exception: any, hint?: EventHint, scope?: Scope): string | undefined {\n    // ensure we haven't captured this very object before\n    if (checkOrSetAlreadyCaught(exception)) {\n      __DEBUG_BUILD__ && logger.log(ALREADY_SEEN_ERROR);\n      return;\n    }\n\n    let eventId: string | undefined = hint && hint.event_id;\n\n    this._process(\n      this.eventFromException(exception, hint)\n        .then(event => this._captureEvent(event, hint, scope))\n        .then(result => {\n          eventId = result;\n        }),\n    );\n\n    return eventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureMessage(\n    message: string,\n    // eslint-disable-next-line deprecation/deprecation\n    level?: Severity | SeverityLevel,\n    hint?: EventHint,\n    scope?: Scope,\n  ): string | undefined {\n    let eventId: string | undefined = hint && hint.event_id;\n\n    const promisedEvent = isPrimitive(message)\n      ? this.eventFromMessage(String(message), level, hint)\n      : this.eventFromException(message, hint);\n\n    this._process(\n      promisedEvent\n        .then(event => this._captureEvent(event, hint, scope))\n        .then(result => {\n          eventId = result;\n        }),\n    );\n\n    return eventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureEvent(event: Event, hint?: EventHint, scope?: Scope): string | undefined {\n    // ensure we haven't captured this very object before\n    if (hint && hint.originalException && checkOrSetAlreadyCaught(hint.originalException)) {\n      __DEBUG_BUILD__ && logger.log(ALREADY_SEEN_ERROR);\n      return;\n    }\n\n    let eventId: string | undefined = hint && hint.event_id;\n\n    this._process(\n      this._captureEvent(event, hint, scope).then(result => {\n        eventId = result;\n      }),\n    );\n\n    return eventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureSession(session: Session): void {\n    if (!(typeof session.release === 'string')) {\n      __DEBUG_BUILD__ && logger.warn('Discarded session because of missing or non-string release');\n    } else {\n      this.sendSession(session);\n      // After sending, we set init false to indicate it's not the first occurrence\n      updateSession(session, { init: false });\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getDsn(): DsnComponents | undefined {\n    return this._dsn;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getOptions(): O {\n    return this._options;\n  }\n\n  /**\n   * @see SdkMetadata in @sentry/types\n   *\n   * @return The metadata of the SDK\n   */\n  public getSdkMetadata(): SdkMetadata | undefined {\n    return this._options._metadata;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getTransport(): Transport | undefined {\n    return this._transport;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public flush(timeout?: number): PromiseLike<boolean> {\n    const transport = this._transport;\n    if (transport) {\n      return this._isClientDoneProcessing(timeout).then(clientFinished => {\n        return transport.flush(timeout).then(transportFlushed => clientFinished && transportFlushed);\n      });\n    } else {\n      return resolvedSyncPromise(true);\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public close(timeout?: number): PromiseLike<boolean> {\n    return this.flush(timeout).then(result => {\n      this.getOptions().enabled = false;\n      return result;\n    });\n  }\n\n  /** Get all installed event processors. */\n  public getEventProcessors(): EventProcessor[] {\n    return this._eventProcessors;\n  }\n\n  /** @inheritDoc */\n  public addEventProcessor(eventProcessor: EventProcessor): void {\n    this._eventProcessors.push(eventProcessor);\n  }\n\n  /**\n   * Sets up the integrations\n   */\n  public setupIntegrations(forceInitialize?: boolean): void {\n    if ((forceInitialize && !this._integrationsInitialized) || (this._isEnabled() && !this._integrationsInitialized)) {\n      this._integrations = setupIntegrations(this, this._options.integrations);\n      this._integrationsInitialized = true;\n    }\n  }\n\n  /**\n   * Gets an installed integration by its `id`.\n   *\n   * @returns The installed integration or `undefined` if no integration with that `id` was installed.\n   */\n  public getIntegrationById(integrationId: string): Integration | undefined {\n    return this._integrations[integrationId];\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getIntegration<T extends Integration>(integration: IntegrationClass<T>): T | null {\n    try {\n      return (this._integrations[integration.id] as T) || null;\n    } catch (_oO) {\n      __DEBUG_BUILD__ && logger.warn(`Cannot retrieve integration ${integration.id} from the current Client`);\n      return null;\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public addIntegration(integration: Integration): void {\n    setupIntegration(this, integration, this._integrations);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public sendEvent(event: Event, hint: EventHint = {}): void {\n    this.emit('beforeSendEvent', event, hint);\n\n    let env = createEventEnvelope(event, this._dsn, this._options._metadata, this._options.tunnel);\n\n    for (const attachment of hint.attachments || []) {\n      env = addItemToEnvelope(\n        env,\n        createAttachmentEnvelopeItem(\n          attachment,\n          this._options.transportOptions && this._options.transportOptions.textEncoder,\n        ),\n      );\n    }\n\n    const promise = this._sendEnvelope(env);\n    if (promise) {\n      promise.then(sendResponse => this.emit('afterSendEvent', event, sendResponse), null);\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public sendSession(session: Session | SessionAggregates): void {\n    const env = createSessionEnvelope(session, this._dsn, this._options._metadata, this._options.tunnel);\n    void this._sendEnvelope(env);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public recordDroppedEvent(reason: EventDropReason, category: DataCategory, _event?: Event): void {\n    // Note: we use `event` in replay, where we overwrite this hook.\n\n    if (this._options.sendClientReports) {\n      // We want to track each category (error, transaction, session, replay_event) separately\n      // but still keep the distinction between different type of outcomes.\n      // We could use nested maps, but it's much easier to read and type this way.\n      // A correct type for map-based implementation if we want to go that route\n      // would be `Partial<Record<SentryRequestType, Partial<Record<Outcome, number>>>>`\n      // With typescript 4.1 we could even use template literal types\n      const key = `${reason}:${category}`;\n      __DEBUG_BUILD__ && logger.log(`Adding outcome: \"${key}\"`);\n\n      // The following works because undefined + 1 === NaN and NaN is falsy\n      this._outcomes[key] = this._outcomes[key] + 1 || 1;\n    }\n  }\n\n  // Keep on() & emit() signatures in sync with types' client.ts interface\n  /* eslint-disable @typescript-eslint/unified-signatures */\n\n  /** @inheritdoc */\n  public on(hook: 'startTransaction', callback: (transaction: Transaction) => void): void;\n\n  /** @inheritdoc */\n  public on(hook: 'finishTransaction', callback: (transaction: Transaction) => void): void;\n\n  /** @inheritdoc */\n  public on(hook: 'beforeEnvelope', callback: (envelope: Envelope) => void): void;\n\n  /** @inheritdoc */\n  public on(hook: 'beforeSendEvent', callback: (event: Event, hint?: EventHint) => void): void;\n\n  /** @inheritdoc */\n  public on(hook: 'preprocessEvent', callback: (event: Event, hint?: EventHint) => void): void;\n\n  /** @inheritdoc */\n  public on(\n    hook: 'afterSendEvent',\n    callback: (event: Event, sendResponse: TransportMakeRequestResponse | void) => void,\n  ): void;\n\n  /** @inheritdoc */\n  public on(hook: 'beforeAddBreadcrumb', callback: (breadcrumb: Breadcrumb, hint?: BreadcrumbHint) => void): void;\n\n  /** @inheritdoc */\n  public on(hook: 'createDsc', callback: (dsc: DynamicSamplingContext) => void): void;\n\n  /** @inheritdoc */\n  public on(hook: 'otelSpanEnd', callback: (otelSpan: unknown, mutableOptions: { drop: boolean }) => void): void;\n\n  /** @inheritdoc */\n  public on(hook: string, callback: unknown): void {\n    if (!this._hooks[hook]) {\n      this._hooks[hook] = [];\n    }\n\n    // @ts-expect-error We assue the types are correct\n    this._hooks[hook].push(callback);\n  }\n\n  /** @inheritdoc */\n  public emit(hook: 'startTransaction', transaction: Transaction): void;\n\n  /** @inheritdoc */\n  public emit(hook: 'finishTransaction', transaction: Transaction): void;\n\n  /** @inheritdoc */\n  public emit(hook: 'beforeEnvelope', envelope: Envelope): void;\n\n  /** @inheritdoc */\n  public emit(hook: 'beforeSendEvent', event: Event, hint?: EventHint): void;\n\n  /** @inheritdoc */\n  public emit(hook: 'preprocessEvent', event: Event, hint?: EventHint): void;\n\n  /** @inheritdoc */\n  public emit(hook: 'afterSendEvent', event: Event, sendResponse: TransportMakeRequestResponse | void): void;\n\n  /** @inheritdoc */\n  public emit(hook: 'beforeAddBreadcrumb', breadcrumb: Breadcrumb, hint?: BreadcrumbHint): void;\n\n  /** @inheritdoc */\n  public emit(hook: 'createDsc', dsc: DynamicSamplingContext): void;\n\n  /** @inheritdoc */\n  public emit(hook: 'otelSpanEnd', otelSpan: unknown, mutableOptions: { drop: boolean }): void;\n\n  /** @inheritdoc */\n  public emit(hook: string, ...rest: unknown[]): void {\n    if (this._hooks[hook]) {\n      this._hooks[hook].forEach(callback => callback(...rest));\n    }\n  }\n\n  /* eslint-enable @typescript-eslint/unified-signatures */\n\n  /** Updates existing session based on the provided event */\n  protected _updateSessionFromEvent(session: Session, event: Event): void {\n    let crashed = false;\n    let errored = false;\n    const exceptions = event.exception && event.exception.values;\n\n    if (exceptions) {\n      errored = true;\n\n      for (const ex of exceptions) {\n        const mechanism = ex.mechanism;\n        if (mechanism && mechanism.handled === false) {\n          crashed = true;\n          break;\n        }\n      }\n    }\n\n    // A session is updated and that session update is sent in only one of the two following scenarios:\n    // 1. Session with non terminal status and 0 errors + an error occurred -> Will set error count to 1 and send update\n    // 2. Session with non terminal status and 1 error + a crash occurred -> Will set status crashed and send update\n    const sessionNonTerminal = session.status === 'ok';\n    const shouldUpdateAndSend = (sessionNonTerminal && session.errors === 0) || (sessionNonTerminal && crashed);\n\n    if (shouldUpdateAndSend) {\n      updateSession(session, {\n        ...(crashed && { status: 'crashed' }),\n        errors: session.errors || Number(errored || crashed),\n      });\n      this.captureSession(session);\n    }\n  }\n\n  /**\n   * Determine if the client is finished processing. Returns a promise because it will wait `timeout` ms before saying\n   * \"no\" (resolving to `false`) in order to give the client a chance to potentially finish first.\n   *\n   * @param timeout The time, in ms, after which to resolve to `false` if the client is still busy. Passing `0` (or not\n   * passing anything) will make the promise wait as long as it takes for processing to finish before resolving to\n   * `true`.\n   * @returns A promise which will resolve to `true` if processing is already done or finishes before the timeout, and\n   * `false` otherwise\n   */\n  protected _isClientDoneProcessing(timeout?: number): PromiseLike<boolean> {\n    return new SyncPromise(resolve => {\n      let ticked: number = 0;\n      const tick: number = 1;\n\n      const interval = setInterval(() => {\n        if (this._numProcessing == 0) {\n          clearInterval(interval);\n          resolve(true);\n        } else {\n          ticked += tick;\n          if (timeout && ticked >= timeout) {\n            clearInterval(interval);\n            resolve(false);\n          }\n        }\n      }, tick);\n    });\n  }\n\n  /** Determines whether this SDK is enabled and a transport is present. */\n  protected _isEnabled(): boolean {\n    return this.getOptions().enabled !== false && this._transport !== undefined;\n  }\n\n  /**\n   * Adds common information to events.\n   *\n   * The information includes release and environment from `options`,\n   * breadcrumbs and context (extra, tags and user) from the scope.\n   *\n   * Information that is already present in the event is never overwritten. For\n   * nested objects, such as the context, keys are merged.\n   *\n   * @param event The original event.\n   * @param hint May contain additional information about the original exception.\n   * @param scope A scope containing event metadata.\n   * @returns A new event with more information.\n   */\n  protected _prepareEvent(event: Event, hint: EventHint, scope?: Scope): PromiseLike<Event | null> {\n    const options = this.getOptions();\n    const integrations = Object.keys(this._integrations);\n    if (!hint.integrations && integrations.length > 0) {\n      hint.integrations = integrations;\n    }\n\n    this.emit('preprocessEvent', event, hint);\n\n    return prepareEvent(options, event, hint, scope, this).then(evt => {\n      if (evt === null) {\n        return evt;\n      }\n\n      // If a trace context is not set on the event, we use the propagationContext set on the event to\n      // generate a trace context. If the propagationContext does not have a dynamic sampling context, we\n      // also generate one for it.\n      const { propagationContext } = evt.sdkProcessingMetadata || {};\n      const trace = evt.contexts && evt.contexts.trace;\n      if (!trace && propagationContext) {\n        const { traceId: trace_id, spanId, parentSpanId, dsc } = propagationContext as PropagationContext;\n        evt.contexts = {\n          trace: {\n            trace_id,\n            span_id: spanId,\n            parent_span_id: parentSpanId,\n          },\n          ...evt.contexts,\n        };\n\n        const dynamicSamplingContext = dsc ? dsc : getDynamicSamplingContextFromClient(trace_id, this, scope);\n\n        evt.sdkProcessingMetadata = {\n          dynamicSamplingContext,\n          ...evt.sdkProcessingMetadata,\n        };\n      }\n      return evt;\n    });\n  }\n\n  /**\n   * Processes the event and logs an error in case of rejection\n   * @param event\n   * @param hint\n   * @param scope\n   */\n  protected _captureEvent(event: Event, hint: EventHint = {}, scope?: Scope): PromiseLike<string | undefined> {\n    return this._processEvent(event, hint, scope).then(\n      finalEvent => {\n        return finalEvent.event_id;\n      },\n      reason => {\n        if (__DEBUG_BUILD__) {\n          // If something's gone wrong, log the error as a warning. If it's just us having used a `SentryError` for\n          // control flow, log just the message (no stack) as a log-level log.\n          const sentryError = reason as SentryError;\n          if (sentryError.logLevel === 'log') {\n            logger.log(sentryError.message);\n          } else {\n            logger.warn(sentryError);\n          }\n        }\n        return undefined;\n      },\n    );\n  }\n\n  /**\n   * Processes an event (either error or message) and sends it to Sentry.\n   *\n   * This also adds breadcrumbs and context information to the event. However,\n   * platform specific meta data (such as the User's IP address) must be added\n   * by the SDK implementor.\n   *\n   *\n   * @param event The event to send to Sentry.\n   * @param hint May contain additional information about the original exception.\n   * @param scope A scope containing event metadata.\n   * @returns A SyncPromise that resolves with the event or rejects in case event was/will not be send.\n   */\n  protected _processEvent(event: Event, hint: EventHint, scope?: Scope): PromiseLike<Event> {\n    const options = this.getOptions();\n    const { sampleRate } = options;\n\n    const isTransaction = isTransactionEvent(event);\n    const isError = isErrorEvent(event);\n    const eventType = event.type || 'error';\n    const beforeSendLabel = `before send for type \\`${eventType}\\``;\n\n    // 1.0 === 100% events are sent\n    // 0.0 === 0% events are sent\n    // Sampling for transaction happens somewhere else\n    if (isError && typeof sampleRate === 'number' && Math.random() > sampleRate) {\n      this.recordDroppedEvent('sample_rate', 'error', event);\n      return rejectedSyncPromise(\n        new SentryError(\n          `Discarding event because it's not included in the random sample (sampling rate = ${sampleRate})`,\n          'log',\n        ),\n      );\n    }\n\n    const dataCategory: DataCategory = eventType === 'replay_event' ? 'replay' : eventType;\n\n    return this._prepareEvent(event, hint, scope)\n      .then(prepared => {\n        if (prepared === null) {\n          this.recordDroppedEvent('event_processor', dataCategory, event);\n          throw new SentryError('An event processor returned `null`, will not send event.', 'log');\n        }\n\n        const isInternalException = hint.data && (hint.data as { __sentry__: boolean }).__sentry__ === true;\n        if (isInternalException) {\n          return prepared;\n        }\n\n        const result = processBeforeSend(options, prepared, hint);\n        return _validateBeforeSendResult(result, beforeSendLabel);\n      })\n      .then(processedEvent => {\n        if (processedEvent === null) {\n          this.recordDroppedEvent('before_send', dataCategory, event);\n          throw new SentryError(`${beforeSendLabel} returned \\`null\\`, will not send event.`, 'log');\n        }\n\n        const session = scope && scope.getSession();\n        if (!isTransaction && session) {\n          this._updateSessionFromEvent(session, processedEvent);\n        }\n\n        // None of the Sentry built event processor will update transaction name,\n        // so if the transaction name has been changed by an event processor, we know\n        // it has to come from custom event processor added by a user\n        const transactionInfo = processedEvent.transaction_info;\n        if (isTransaction && transactionInfo && processedEvent.transaction !== event.transaction) {\n          const source = 'custom';\n          processedEvent.transaction_info = {\n            ...transactionInfo,\n            source,\n          };\n        }\n\n        this.sendEvent(processedEvent, hint);\n        return processedEvent;\n      })\n      .then(null, reason => {\n        if (reason instanceof SentryError) {\n          throw reason;\n        }\n\n        this.captureException(reason, {\n          data: {\n            __sentry__: true,\n          },\n          originalException: reason,\n        });\n        throw new SentryError(\n          `Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.\\nReason: ${reason}`,\n        );\n      });\n  }\n\n  /**\n   * Occupies the client with processing and event\n   */\n  protected _process<T>(promise: PromiseLike<T>): void {\n    this._numProcessing++;\n    void promise.then(\n      value => {\n        this._numProcessing--;\n        return value;\n      },\n      reason => {\n        this._numProcessing--;\n        return reason;\n      },\n    );\n  }\n\n  /**\n   * @inheritdoc\n   */\n  protected _sendEnvelope(envelope: Envelope): PromiseLike<void | TransportMakeRequestResponse> | void {\n    this.emit('beforeEnvelope', envelope);\n\n    if (this._isEnabled() && this._transport) {\n      return this._transport.send(envelope).then(null, reason => {\n        __DEBUG_BUILD__ && logger.error('Error while sending event:', reason);\n      });\n    } else {\n      __DEBUG_BUILD__ && logger.error('Transport disabled');\n    }\n  }\n\n  /**\n   * Clears outcomes on this client and returns them.\n   */\n  protected _clearOutcomes(): Outcome[] {\n    const outcomes = this._outcomes;\n    this._outcomes = {};\n    return Object.keys(outcomes).map(key => {\n      const [reason, category] = key.split(':') as [EventDropReason, DataCategory];\n      return {\n        reason,\n        category,\n        quantity: outcomes[key],\n      };\n    });\n  }\n\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types\n  public abstract eventFromException(_exception: any, _hint?: EventHint): PromiseLike<Event>;\n\n  /**\n   * @inheritDoc\n   */\n  public abstract eventFromMessage(\n    _message: string,\n    // eslint-disable-next-line deprecation/deprecation\n    _level?: Severity | SeverityLevel,\n    _hint?: EventHint,\n  ): PromiseLike<Event>;\n}\n\n/**\n * Verifies that return value of configured `beforeSend` or `beforeSendTransaction` is of expected type, and returns the value if so.\n */\nfunction _validateBeforeSendResult(\n  beforeSendResult: PromiseLike<Event | null> | Event | null,\n  beforeSendLabel: string,\n): PromiseLike<Event | null> | Event | null {\n  const invalidValueError = `${beforeSendLabel} must return \\`null\\` or a valid event.`;\n  if (isThenable(beforeSendResult)) {\n    return beforeSendResult.then(\n      event => {\n        if (!isPlainObject(event) && event !== null) {\n          throw new SentryError(invalidValueError);\n        }\n        return event;\n      },\n      e => {\n        throw new SentryError(`${beforeSendLabel} rejected with ${e}`);\n      },\n    );\n  } else if (!isPlainObject(beforeSendResult) && beforeSendResult !== null) {\n    throw new SentryError(invalidValueError);\n  }\n  return beforeSendResult;\n}\n\n/**\n * Process the matching `beforeSendXXX` callback.\n */\nfunction processBeforeSend(\n  options: ClientOptions,\n  event: Event,\n  hint: EventHint,\n): PromiseLike<Event | null> | Event | null {\n  const { beforeSend, beforeSendTransaction } = options;\n\n  if (isErrorEvent(event) && beforeSend) {\n    return beforeSend(event, hint);\n  }\n\n  if (isTransactionEvent(event) && beforeSendTransaction) {\n    return beforeSendTransaction(event, hint);\n  }\n\n  return event;\n}\n\nfunction isErrorEvent(event: Event): event is ErrorEvent {\n  return event.type === undefined;\n}\n\nfunction isTransactionEvent(event: Event): event is TransactionEvent {\n  return event.type === 'transaction';\n}\n", "import type {\n  CheckInEnvelope,\n  CheckInItem,\n  DsnComponents,\n  DynamicSamplingContext,\n  SdkMetadata,\n  SerializedCheckIn,\n} from '@sentry/types';\nimport { createEnvelope, dropUndefinedKeys, dsnToString } from '@sentry/utils';\n\n/**\n * Create envelope from check in item.\n */\nexport function createCheckInEnvelope(\n  checkIn: SerializedCheckIn,\n  dynamicSamplingContext?: Partial<DynamicSamplingContext>,\n  metadata?: SdkMetadata,\n  tunnel?: string,\n  dsn?: DsnComponents,\n): CheckInEnvelope {\n  const headers: CheckInEnvelope[0] = {\n    sent_at: new Date().toISOString(),\n  };\n\n  if (metadata && metadata.sdk) {\n    headers.sdk = {\n      name: metadata.sdk.name,\n      version: metadata.sdk.version,\n    };\n  }\n\n  if (!!tunnel && !!dsn) {\n    headers.dsn = dsnToString(dsn);\n  }\n\n  if (dynamicSamplingContext) {\n    headers.trace = dropUndefinedKeys(dynamicSamplingContext) as DynamicSamplingContext;\n  }\n\n  const item = createCheckInEnvelopeItem(checkIn);\n  return createEnvelope<CheckInEnvelope>(headers, [item]);\n}\n\nfunction createCheckInEnvelopeItem(checkIn: SerializedCheckIn): CheckInItem {\n  const checkInHeaders: CheckInItem[0] = {\n    type: 'check_in',\n  };\n  return [checkInHeaders, checkIn];\n}\n", "import type {\n  BaseTransportOptions,\n  CheckIn,\n  ClientOptions,\n  DynamicSamplingContext,\n  Event,\n  EventHint,\n  MonitorConfig,\n  SerializedCheckIn,\n  Severity,\n  SeverityLevel,\n  TraceContext,\n} from '@sentry/types';\nimport { eventFromMessage, eventFromUnknownInput, logger, resolvedSyncPromise, uuid4 } from '@sentry/utils';\n\nimport { BaseClient } from './baseclient';\nimport { createCheckInEnvelope } from './checkin';\nimport { getCurrentHub } from './hub';\nimport type { Scope } from './scope';\nimport { SessionFlusher } from './sessionflusher';\nimport { addTracingExtensions, getDynamicSamplingContextFromClient } from './tracing';\n\nexport interface ServerRuntimeClientOptions extends ClientOptions<BaseTransportOptions> {\n  platform?: string;\n  runtime?: { name: string; version?: string };\n  serverName?: string;\n}\n\n/**\n * The Sentry Server Runtime Client SDK.\n */\nexport class ServerRuntimeClient<\n  O extends ClientOptions & ServerRuntimeClientOptions = ServerRuntimeClientOptions,\n> extends BaseClient<O> {\n  protected _sessionFlusher: SessionFlusher | undefined;\n\n  /**\n   * Creates a new Edge SDK instance.\n   * @param options Configuration options for this SDK.\n   */\n  public constructor(options: O) {\n    // Server clients always support tracing\n    addTracingExtensions();\n\n    super(options);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public eventFromException(exception: unknown, hint?: EventHint): PromiseLike<Event> {\n    return resolvedSyncPromise(eventFromUnknownInput(getCurrentHub, this._options.stackParser, exception, hint));\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public eventFromMessage(\n    message: string,\n    // eslint-disable-next-line deprecation/deprecation\n    level: Severity | SeverityLevel = 'info',\n    hint?: EventHint,\n  ): PromiseLike<Event> {\n    return resolvedSyncPromise(\n      eventFromMessage(this._options.stackParser, message, level, hint, this._options.attachStacktrace),\n    );\n  }\n\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types\n  public captureException(exception: any, hint?: EventHint, scope?: Scope): string | undefined {\n    // Check if the flag `autoSessionTracking` is enabled, and if `_sessionFlusher` exists because it is initialised only\n    // when the `requestHandler` middleware is used, and hence the expectation is to have SessionAggregates payload\n    // sent to the Server only when the `requestHandler` middleware is used\n    if (this._options.autoSessionTracking && this._sessionFlusher && scope) {\n      const requestSession = scope.getRequestSession();\n\n      // Necessary checks to ensure this is code block is executed only within a request\n      // Should override the status only if `requestSession.status` is `Ok`, which is its initial stage\n      if (requestSession && requestSession.status === 'ok') {\n        requestSession.status = 'errored';\n      }\n    }\n\n    return super.captureException(exception, hint, scope);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureEvent(event: Event, hint?: EventHint, scope?: Scope): string | undefined {\n    // Check if the flag `autoSessionTracking` is enabled, and if `_sessionFlusher` exists because it is initialised only\n    // when the `requestHandler` middleware is used, and hence the expectation is to have SessionAggregates payload\n    // sent to the Server only when the `requestHandler` middleware is used\n    if (this._options.autoSessionTracking && this._sessionFlusher && scope) {\n      const eventType = event.type || 'exception';\n      const isException =\n        eventType === 'exception' && event.exception && event.exception.values && event.exception.values.length > 0;\n\n      // If the event is of type Exception, then a request session should be captured\n      if (isException) {\n        const requestSession = scope.getRequestSession();\n\n        // Ensure that this is happening within the bounds of a request, and make sure not to override\n        // Session Status if Errored / Crashed\n        if (requestSession && requestSession.status === 'ok') {\n          requestSession.status = 'errored';\n        }\n      }\n    }\n\n    return super.captureEvent(event, hint, scope);\n  }\n\n  /**\n   *\n   * @inheritdoc\n   */\n  public close(timeout?: number): PromiseLike<boolean> {\n    if (this._sessionFlusher) {\n      this._sessionFlusher.close();\n    }\n    return super.close(timeout);\n  }\n\n  /** Method that initialises an instance of SessionFlusher on Client */\n  public initSessionFlusher(): void {\n    const { release, environment } = this._options;\n    if (!release) {\n      __DEBUG_BUILD__ && logger.warn('Cannot initialise an instance of SessionFlusher if no release is provided!');\n    } else {\n      this._sessionFlusher = new SessionFlusher(this, {\n        release,\n        environment,\n      });\n    }\n  }\n\n  /**\n   * Create a cron monitor check in and send it to Sentry.\n   *\n   * @param checkIn An object that describes a check in.\n   * @param upsertMonitorConfig An optional object that describes a monitor config. Use this if you want\n   * to create a monitor automatically when sending a check in.\n   */\n  public captureCheckIn(checkIn: CheckIn, monitorConfig?: MonitorConfig, scope?: Scope): string {\n    const id = checkIn.status !== 'in_progress' && checkIn.checkInId ? checkIn.checkInId : uuid4();\n    if (!this._isEnabled()) {\n      __DEBUG_BUILD__ && logger.warn('SDK not enabled, will not capture checkin.');\n      return id;\n    }\n\n    const options = this.getOptions();\n    const { release, environment, tunnel } = options;\n\n    const serializedCheckIn: SerializedCheckIn = {\n      check_in_id: id,\n      monitor_slug: checkIn.monitorSlug,\n      status: checkIn.status,\n      release,\n      environment,\n    };\n\n    if (checkIn.status !== 'in_progress') {\n      serializedCheckIn.duration = checkIn.duration;\n    }\n\n    if (monitorConfig) {\n      serializedCheckIn.monitor_config = {\n        schedule: monitorConfig.schedule,\n        checkin_margin: monitorConfig.checkinMargin,\n        max_runtime: monitorConfig.maxRuntime,\n        timezone: monitorConfig.timezone,\n      };\n    }\n\n    const [dynamicSamplingContext, traceContext] = this._getTraceInfoFromScope(scope);\n    if (traceContext) {\n      serializedCheckIn.contexts = {\n        trace: traceContext,\n      };\n    }\n\n    const envelope = createCheckInEnvelope(\n      serializedCheckIn,\n      dynamicSamplingContext,\n      this.getSdkMetadata(),\n      tunnel,\n      this.getDsn(),\n    );\n\n    __DEBUG_BUILD__ && logger.info('Sending checkin:', checkIn.monitorSlug, checkIn.status);\n    void this._sendEnvelope(envelope);\n    return id;\n  }\n\n  /**\n   * Method responsible for capturing/ending a request session by calling `incrementSessionStatusCount` to increment\n   * appropriate session aggregates bucket\n   */\n  protected _captureRequestSession(): void {\n    if (!this._sessionFlusher) {\n      __DEBUG_BUILD__ && logger.warn('Discarded request mode session because autoSessionTracking option was disabled');\n    } else {\n      this._sessionFlusher.incrementSessionStatusCount();\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  protected _prepareEvent(event: Event, hint: EventHint, scope?: Scope): PromiseLike<Event | null> {\n    if (this._options.platform) {\n      event.platform = event.platform || this._options.platform;\n    }\n\n    if (this._options.runtime) {\n      event.contexts = {\n        ...event.contexts,\n        runtime: (event.contexts || {}).runtime || this._options.runtime,\n      };\n    }\n\n    if (this._options.serverName) {\n      event.server_name = event.server_name || this._options.serverName;\n    }\n\n    return super._prepareEvent(event, hint, scope);\n  }\n\n  /** Extract trace information from scope */\n  private _getTraceInfoFromScope(\n    scope: Scope | undefined,\n  ): [dynamicSamplingContext: Partial<DynamicSamplingContext> | undefined, traceContext: TraceContext | undefined] {\n    if (!scope) {\n      return [undefined, undefined];\n    }\n\n    const span = scope.getSpan();\n    if (span) {\n      const samplingContext = span.transaction ? span.transaction.getDynamicSamplingContext() : undefined;\n      return [samplingContext, span.getTraceContext()];\n    }\n\n    const { traceId, spanId, parentSpanId, dsc } = scope.getPropagationContext();\n    const traceContext: TraceContext = {\n      trace_id: traceId,\n      span_id: spanId,\n      parent_span_id: parentSpanId,\n    };\n    if (dsc) {\n      return [dsc, traceContext];\n    }\n\n    return [getDynamicSamplingContextFromClient(traceId, this, scope), traceContext];\n  }\n}\n", "import type {\n  Envelope,\n  EnvelopeItem,\n  EnvelopeItemType,\n  Event,\n  EventDropReason,\n  EventItem,\n  InternalBaseTransportOptions,\n  Transport,\n  TransportMakeRequestResponse,\n  TransportRequestExecutor,\n} from '@sentry/types';\nimport type { PromiseBuffer, RateLimits } from '@sentry/utils';\nimport {\n  createEnvelope,\n  envelopeItemTypeToDataCategory,\n  forEachEnvelopeItem,\n  isRateLimited,\n  logger,\n  makePromiseBuffer,\n  resolvedSyncPromise,\n  SentryError,\n  serializeEnvelope,\n  updateRateLimits,\n} from '@sentry/utils';\n\nexport const DEFAULT_TRANSPORT_BUFFER_SIZE = 30;\n\n/**\n * Creates an instance of a Sentry `Transport`\n *\n * @param options\n * @param makeRequest\n */\nexport function createTransport(\n  options: InternalBaseTransportOptions,\n  makeRequest: TransportRequestExecutor,\n  buffer: PromiseBuffer<void | TransportMakeRequestResponse> = makePromiseBuffer(\n    options.bufferSize || DEFAULT_TRANSPORT_BUFFER_SIZE,\n  ),\n): Transport {\n  let rateLimits: RateLimits = {};\n  const flush = (timeout?: number): PromiseLike<boolean> => buffer.drain(timeout);\n\n  function send(envelope: Envelope): PromiseLike<void | TransportMakeRequestResponse> {\n    const filteredEnvelopeItems: EnvelopeItem[] = [];\n\n    // Drop rate limited items from envelope\n    forEachEnvelopeItem(envelope, (item, type) => {\n      const envelopeItemDataCategory = envelopeItemTypeToDataCategory(type);\n      if (isRateLimited(rateLimits, envelopeItemDataCategory)) {\n        const event: Event | undefined = getEventForEnvelopeItem(item, type);\n        options.recordDroppedEvent('ratelimit_backoff', envelopeItemDataCategory, event);\n      } else {\n        filteredEnvelopeItems.push(item);\n      }\n    });\n\n    // Skip sending if envelope is empty after filtering out rate limited events\n    if (filteredEnvelopeItems.length === 0) {\n      return resolvedSyncPromise();\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const filteredEnvelope: Envelope = createEnvelope(envelope[0], filteredEnvelopeItems as any);\n\n    // Creates client report for each item in an envelope\n    const recordEnvelopeLoss = (reason: EventDropReason): void => {\n      forEachEnvelopeItem(filteredEnvelope, (item, type) => {\n        const event: Event | undefined = getEventForEnvelopeItem(item, type);\n        options.recordDroppedEvent(reason, envelopeItemTypeToDataCategory(type), event);\n      });\n    };\n\n    const requestTask = (): PromiseLike<void | TransportMakeRequestResponse> =>\n      makeRequest({ body: serializeEnvelope(filteredEnvelope, options.textEncoder) }).then(\n        response => {\n          // We don't want to throw on NOK responses, but we want to at least log them\n          if (response.statusCode !== undefined && (response.statusCode < 200 || response.statusCode >= 300)) {\n            __DEBUG_BUILD__ && logger.warn(`Sentry responded with status code ${response.statusCode} to sent event.`);\n          }\n\n          rateLimits = updateRateLimits(rateLimits, response);\n          return response;\n        },\n        error => {\n          recordEnvelopeLoss('network_error');\n          throw error;\n        },\n      );\n\n    return buffer.add(requestTask).then(\n      result => result,\n      error => {\n        if (error instanceof SentryError) {\n          __DEBUG_BUILD__ && logger.error('Skipped sending event because buffer is full.');\n          recordEnvelopeLoss('queue_overflow');\n          return resolvedSyncPromise();\n        } else {\n          throw error;\n        }\n      },\n    );\n  }\n\n  // We use this to identifify if the transport is the base transport\n  // TODO (v8): Remove this again as we'll no longer need it\n  send.__sentry__baseTransport__ = true;\n\n  return {\n    send,\n    flush,\n  };\n}\n\nfunction getEventForEnvelopeItem(item: Envelope[1][number], type: EnvelopeItemType): Event | undefined {\n  if (type !== 'event' && type !== 'transaction') {\n    return undefined;\n  }\n\n  return Array.isArray(item) ? (item as EventItem)[1] : undefined;\n}\n", "import { G<PERSON><PERSON><PERSON><PERSON>_O<PERSON>J, isError, isPlainObject, extractExceptionKeysForMessage, normalizeToSize, addExceptionTypeValue, addExceptionMechanism, isInstanceOf, resolvedSyncPromise, createStack<PERSON>arser, nodeStackLineParser, basename, rejectedSyncPromise, stackParserFromStackParserOptions } from '@sentry/utils';\nexport { Dedupe, ExtraErrorData, RewriteFrames, SessionTiming, Transaction } from '@sentry/integrations';\nimport { ServerRuntimeClient, createTransport, Hub, getIntegrationsToSetup } from '@sentry/core';\n\nfunction isObject(value) {\n    return typeof value === 'object' && value !== null;\n}\nfunction isMechanism(value) {\n    return (isObject(value) &&\n        'handled' in value &&\n        typeof value.handled === 'boolean' &&\n        'type' in value &&\n        typeof value.type === 'string');\n}\nfunction containsMechanism(value) {\n    return (isObject(value) && 'mechanism' in value && isMechanism(value['mechanism']));\n}\n/**\n * Tries to find release in a global\n */\nfunction getSentryRelease() {\n    // Most of the plugins from https://docs.sentry.io/platforms/javascript/sourcemaps/uploading/ inject SENTRY_RELEASE global to the bundle\n    if (GLOBAL_OBJ.SENTRY_RELEASE && GLOBAL_OBJ.SENTRY_RELEASE.id) {\n        return GLOBAL_OBJ.SENTRY_RELEASE.id;\n    }\n}\n/**\n * Creates an entry on existing object and returns it, or creates a new object with the entry if it doesn't exist.\n *\n * @param target\n * @param entry\n * @returns Object with new entry.\n */\nfunction setOnOptional(target, entry) {\n    if (target !== undefined) {\n        target[entry[0]] = entry[1];\n        return target;\n    }\n    else {\n        return { [entry[0]]: entry[1] };\n    }\n}\n\n/**\n * Extracts stack frames from the error.stack string\n */\nfunction parseStackFrames(stackParser, error) {\n    return stackParser(error.stack || '', 1);\n}\n/**\n * There are cases where stacktrace.message is an Event object\n * https://github.com/getsentry/sentry-javascript/issues/1949\n * In this specific case we try to extract stacktrace.message.error.message\n */\nfunction extractMessage(ex) {\n    const message = ex && ex.message;\n    if (!message) {\n        return 'No error message';\n    }\n    if (message.error && typeof message.error.message === 'string') {\n        return message.error.message;\n    }\n    return message;\n}\n/**\n * Extracts stack frames from the error and builds a Sentry Exception\n */\nfunction exceptionFromError(stackParser, error) {\n    const exception = {\n        type: error.name || error.constructor.name,\n        value: extractMessage(error),\n    };\n    const frames = parseStackFrames(stackParser, error);\n    if (frames.length) {\n        exception.stacktrace = { frames };\n    }\n    if (exception.type === undefined && exception.value === '') {\n        exception.value = 'Unrecoverable error caught';\n    }\n    return exception;\n}\n/**\n * Builds and Event from a Exception\n */\nfunction eventFromUnknownInput(sdk, stackParser, exception, hint) {\n    let ex;\n    const providedMechanism = hint && hint.data && containsMechanism(hint.data)\n        ? hint.data.mechanism\n        : undefined;\n    const mechanism = providedMechanism ?? {\n        handled: true,\n        type: 'generic',\n    };\n    if (!isError(exception)) {\n        if (isPlainObject(exception)) {\n            // This will allow us to group events based on top-level keys\n            // which is much better than creating new group when any key/value change\n            const message = `Non-Error exception captured with keys: ${extractExceptionKeysForMessage(exception)}`;\n            const client = sdk?.getClient();\n            const normalizeDepth = client && client.getOptions().normalizeDepth;\n            sdk?.configureScope((scope) => {\n                scope.setExtra('__serialized__', normalizeToSize(exception, normalizeDepth));\n            });\n            ex = (hint && hint.syntheticException) || new Error(message);\n            ex.message = message;\n        }\n        else {\n            // This handles when someone does: `throw \"something awesome\";`\n            // We use synthesized Error here so we can extract a (rough) stack trace.\n            ex = (hint && hint.syntheticException) || new Error(exception);\n            ex.message = exception;\n        }\n        mechanism.synthetic = true;\n    }\n    else {\n        ex = exception;\n    }\n    const event = {\n        exception: {\n            values: [exceptionFromError(stackParser, ex)],\n        },\n    };\n    addExceptionTypeValue(event, undefined, undefined);\n    addExceptionMechanism(event, mechanism);\n    return {\n        ...event,\n        event_id: hint && hint.event_id,\n    };\n}\n/**\n * Builds and Event from a Message\n */\nfunction eventFromMessage(stackParser, message, level = 'info', hint, attachStacktrace) {\n    const event = {\n        event_id: hint && hint.event_id,\n        level,\n        message,\n    };\n    if (attachStacktrace && hint && hint.syntheticException) {\n        const frames = parseStackFrames(stackParser, hint.syntheticException);\n        if (frames.length) {\n            event.exception = {\n                values: [\n                    {\n                        value: message,\n                        stacktrace: { frames },\n                    },\n                ],\n            };\n        }\n    }\n    return event;\n}\n\nconst DEFAULT_LIMIT = 5;\nclass LinkedErrors {\n    static id = 'LinkedErrors';\n    name = LinkedErrors.id;\n    limit;\n    constructor(options = {}) {\n        this.limit = options.limit || DEFAULT_LIMIT;\n    }\n    setupOnce(addGlobalEventProcessor, getCurrentHub) {\n        const client = getCurrentHub().getClient();\n        if (!client) {\n            return;\n        }\n        addGlobalEventProcessor((event, hint) => {\n            const self = getCurrentHub().getIntegration(LinkedErrors);\n            if (!self) {\n                return event;\n            }\n            return handler(client.getOptions().stackParser, self.limit, event, hint);\n        });\n    }\n}\nfunction handler(parser, limit, event, hint) {\n    if (!event.exception ||\n        !event.exception.values ||\n        !hint ||\n        !isInstanceOf(hint.originalException, Error)) {\n        return event;\n    }\n    const linkedErrors = walkErrorTree(parser, limit, hint.originalException);\n    event.exception.values = [...linkedErrors, ...event.exception.values];\n    return event;\n}\nfunction walkErrorTree(parser, limit, error, stack = []) {\n    if (!isInstanceOf(error.cause, Error) || stack.length + 1 >= limit) {\n        return stack;\n    }\n    const exception = exceptionFromError(parser, error.cause);\n    return walkErrorTree(parser, limit, error.cause, [\n        exception,\n        ...stack,\n    ]);\n}\n\nconst defaultRequestDataOptions = {\n    allowedHeaders: ['CF-RAY', 'CF-Worker'],\n};\nclass RequestData {\n    static id = 'RequestData';\n    name = RequestData.id;\n    #options;\n    constructor(options = {}) {\n        this.#options = { ...defaultRequestDataOptions, ...options };\n    }\n    setupOnce(addGlobalEventProcessor, getCurrentHub) {\n        const client = getCurrentHub().getClient();\n        if (!client) {\n            return;\n        }\n        addGlobalEventProcessor((event) => {\n            const { sdkProcessingMetadata } = event;\n            const self = getCurrentHub().getIntegration(RequestData);\n            if (!self || !sdkProcessingMetadata) {\n                return event;\n            }\n            if ('request' in sdkProcessingMetadata &&\n                sdkProcessingMetadata.request instanceof Request) {\n                event.request = toEventRequest(sdkProcessingMetadata.request, this.#options);\n                event.user = toEventUser(event.user ?? {}, sdkProcessingMetadata.request, this.#options);\n            }\n            if ('requestData' in sdkProcessingMetadata) {\n                if (event.request) {\n                    event.request.data = sdkProcessingMetadata.requestData;\n                }\n                else {\n                    event.request = {\n                        data: sdkProcessingMetadata.requestData,\n                    };\n                }\n            }\n            return event;\n        });\n    }\n}\n/**\n * Applies allowlists on existing user object.\n *\n * @param user\n * @param request\n * @param options\n * @returns New copy of user\n */\nfunction toEventUser(user, request, options) {\n    const ip_address = request.headers.get('CF-Connecting-IP');\n    const { allowedIps } = options;\n    const newUser = { ...user };\n    if (!('ip_address' in user) && // If ip_address is already set from explicitly called setUser, we don't want to overwrite it\n        ip_address &&\n        allowedIps !== undefined &&\n        testAllowlist(ip_address, allowedIps)) {\n        newUser.ip_address = ip_address;\n    }\n    return Object.keys(newUser).length > 0 ? newUser : undefined;\n}\n/**\n * Converts data from fetch event's Request to Sentry Request used in Sentry Event\n *\n * @param request Native Request object\n * @param options Integration options\n * @returns Sentry Request object\n */\nfunction toEventRequest(request, options) {\n    // Build cookies\n    const cookieString = request.headers.get('cookie');\n    let cookies = undefined;\n    if (cookieString) {\n        try {\n            cookies = parseCookie(cookieString);\n        }\n        catch (e) {\n            // Cookie string failed to parse, no need to do anything\n        }\n    }\n    const headers = {};\n    // Build headers (omit cookie header, because we used it in the previous step)\n    for (const [k, v] of request.headers.entries()) {\n        if (k !== 'cookie') {\n            headers[k] = v;\n        }\n    }\n    const eventRequest = {\n        method: request.method,\n        cookies,\n        headers,\n    };\n    try {\n        const url = new URL(request.url);\n        eventRequest.url = `${url.protocol}//${url.hostname}${url.pathname}`;\n        eventRequest.query_string = url.search;\n    }\n    catch (e) {\n        // `new URL` failed, let's try to split URL the primitive way\n        const qi = request.url.indexOf('?');\n        if (qi < 0) {\n            // no query string\n            eventRequest.url = request.url;\n        }\n        else {\n            eventRequest.url = request.url.substr(0, qi);\n            eventRequest.query_string = request.url.substr(qi + 1);\n        }\n    }\n    // Let's try to remove sensitive data from incoming Request\n    const { allowedHeaders, allowedCookies, allowedSearchParams } = options;\n    if (allowedHeaders !== undefined && eventRequest.headers) {\n        eventRequest.headers = applyAllowlistToObject(eventRequest.headers, allowedHeaders);\n        if (Object.keys(eventRequest.headers).length === 0) {\n            delete eventRequest.headers;\n        }\n    }\n    else {\n        delete eventRequest.headers;\n    }\n    if (allowedCookies !== undefined && eventRequest.cookies) {\n        eventRequest.cookies = applyAllowlistToObject(eventRequest.cookies, allowedCookies);\n        if (Object.keys(eventRequest.cookies).length === 0) {\n            delete eventRequest.cookies;\n        }\n    }\n    else {\n        delete eventRequest.cookies;\n    }\n    if (allowedSearchParams !== undefined) {\n        const params = Object.fromEntries(new URLSearchParams(eventRequest.query_string));\n        const allowedParams = new URLSearchParams();\n        Object.keys(applyAllowlistToObject(params, allowedSearchParams)).forEach((allowedKey) => {\n            allowedParams.set(allowedKey, params[allowedKey]);\n        });\n        eventRequest.query_string = allowedParams.toString();\n    }\n    else {\n        delete eventRequest.query_string;\n    }\n    return eventRequest;\n}\n/**\n * Helper function that tests 'allowlist' on string.\n *\n * @param target\n * @param allowlist\n * @returns True if target is allowed.\n */\nfunction testAllowlist(target, allowlist) {\n    if (typeof allowlist === 'boolean') {\n        return allowlist;\n    }\n    else if (allowlist instanceof RegExp) {\n        return allowlist.test(target);\n    }\n    else if (Array.isArray(allowlist)) {\n        const allowlistLowercased = allowlist.map((item) => item.toLowerCase());\n        return allowlistLowercased.includes(target);\n    }\n    else {\n        return false;\n    }\n}\n/**\n * Helper function that applies 'allowlist' to target's entries.\n *\n * @param target\n * @param allowlist\n * @returns New object with allowed keys.\n */\nfunction applyAllowlistToObject(target, allowlist) {\n    let predicate = () => false;\n    if (typeof allowlist === 'boolean') {\n        return allowlist ? target : {};\n    }\n    else if (allowlist instanceof RegExp) {\n        predicate = (item) => allowlist.test(item);\n    }\n    else if (Array.isArray(allowlist)) {\n        const allowlistLowercased = allowlist.map((item) => item.toLowerCase());\n        predicate = (item) => allowlistLowercased.includes(item.toLowerCase());\n    }\n    else {\n        return {};\n    }\n    return Object.keys(target)\n        .filter(predicate)\n        .reduce((allowed, key) => {\n        allowed[key] = target[key];\n        return allowed;\n    }, {});\n}\n/**\n * Converts cookie string to an object.\n *\n * @param cookieString\n * @returns Object of cookie entries, or empty object if something went wrong during the conversion.\n */\nfunction parseCookie(cookieString) {\n    if (typeof cookieString !== 'string') {\n        return {};\n    }\n    try {\n        return cookieString\n            .split(';')\n            .map((part) => part.split('='))\n            .reduce((acc, [cookieKey, cookieValue]) => {\n            acc[decodeURIComponent(cookieKey.trim())] = decodeURIComponent(cookieValue.trim());\n            return acc;\n        }, {});\n    }\n    catch {\n        return {};\n    }\n}\n\n/**\n * Installs integrations on the current scope.\n *\n * @param integrations array of integration instances\n */\nfunction setupIntegrations(integrations, sdk) {\n    const integrationIndex = {};\n    integrations.forEach((integration) => {\n        integrationIndex[integration.name] = integration;\n        integration.setupOnce((callback) => {\n            sdk.getScope()?.addEventProcessor(callback);\n        }, () => sdk);\n    });\n    return integrationIndex;\n}\n\n/**\n * The Cloudflare Workers SDK Client.\n */\nclass ToucanClient extends ServerRuntimeClient {\n    /**\n     * Some functions need to access the Hub (Toucan instance) this client is bound to,\n     * but calling 'getCurrentHub()' is unsafe because it uses globals.\n     * So we store a reference to the Hub after binding to it and provide it to methods that need it.\n     */\n    #sdk = null;\n    /**\n     * Creates a new Toucan SDK instance.\n     * @param options Configuration options for this SDK.\n     */\n    constructor(options) {\n        options._metadata = options._metadata || {};\n        options._metadata.sdk = options._metadata.sdk || {\n            name: 'toucan-js',\n            packages: [\n                {\n                    name: 'npm:' + 'toucan-js',\n                    version: '3.3.1',\n                },\n            ],\n            version: '3.3.1',\n        };\n        super(options);\n    }\n    /**\n     * By default, integrations are stored in a global. We want to store them in a local instance because they may have contextual data, such as event request.\n     */\n    setupIntegrations() {\n        if (this._isEnabled() && !this._integrationsInitialized && this.#sdk) {\n            this._integrations = setupIntegrations(this._options.integrations, this.#sdk);\n            this._integrationsInitialized = true;\n        }\n    }\n    eventFromException(exception, hint) {\n        return resolvedSyncPromise(eventFromUnknownInput(this.#sdk, this._options.stackParser, exception, hint));\n    }\n    eventFromMessage(message, level = 'info', hint) {\n        return resolvedSyncPromise(eventFromMessage(this._options.stackParser, message, level, hint, this._options.attachStacktrace));\n    }\n    _prepareEvent(event, hint, scope) {\n        event.platform = event.platform || 'javascript';\n        if (this.getOptions().request) {\n            // Set 'request' on sdkProcessingMetadata to be later processed by RequestData integration\n            event.sdkProcessingMetadata = setOnOptional(event.sdkProcessingMetadata, [\n                'request',\n                this.getOptions().request,\n            ]);\n        }\n        if (this.getOptions().requestData) {\n            // Set 'requestData' on sdkProcessingMetadata to be later processed by RequestData integration\n            event.sdkProcessingMetadata = setOnOptional(event.sdkProcessingMetadata, [\n                'requestData',\n                this.getOptions().requestData,\n            ]);\n        }\n        return super._prepareEvent(event, hint, scope);\n    }\n    getSdk() {\n        return this.#sdk;\n    }\n    setSdk(sdk) {\n        this.#sdk = sdk;\n    }\n    /**\n     * Sets the request body context on all future events.\n     *\n     * @param body Request body.\n     * @example\n     * const body = await request.text();\n     * toucan.setRequestBody(body);\n     */\n    setRequestBody(body) {\n        this.getOptions().requestData = body;\n    }\n    /**\n     * Enable/disable the SDK.\n     *\n     * @param enabled\n     */\n    setEnabled(enabled) {\n        this.getOptions().enabled = enabled;\n    }\n}\n\n/**\n * Stack line parser for Cloudflare Workers.\n * This wraps node stack parser and adjusts root paths to match with source maps.\n *\n */\nfunction workersStackLineParser(getModule) {\n    const [arg1, arg2] = nodeStackLineParser(getModule);\n    const fn = (line) => {\n        const result = arg2(line);\n        if (result) {\n            const filename = result.filename;\n            // Workers runtime runs a single bundled file that is always in a virtual root\n            result.abs_path =\n                filename !== undefined && !filename.startsWith('/')\n                    ? `/${filename}`\n                    : filename;\n            // There is no way to tell what code is in_app and what comes from dependencies (node_modules), since we have one bundled file.\n            // So everything is in_app, unless an error comes from runtime function (ie. JSON.parse), which is determined by the presence of filename.\n            result.in_app = filename !== undefined;\n        }\n        return result;\n    };\n    return [arg1, fn];\n}\n/**\n * Gets the module from filename.\n *\n * @param filename\n * @returns Module name\n */\nfunction getModule(filename) {\n    if (!filename) {\n        return;\n    }\n    // In Cloudflare Workers there is always only one bundled file\n    return basename(filename, '.js');\n}\n/** Cloudflare Workers stack parser */\nconst defaultStackParser = createStackParser(workersStackLineParser(getModule));\n\n/**\n * Creates a Transport that uses native fetch. This transport automatically extends the Workers lifetime with 'waitUntil'.\n */\nfunction makeFetchTransport(options) {\n    function makeRequest({ body, }) {\n        try {\n            const fetchFn = options.fetcher ?? fetch;\n            const request = fetchFn(options.url, {\n                method: 'POST',\n                headers: options.headers,\n                body,\n            }).then((response) => {\n                return {\n                    statusCode: response.status,\n                    headers: {\n                        'retry-after': response.headers.get('Retry-After'),\n                        'x-sentry-rate-limits': response.headers.get('X-Sentry-Rate-Limits'),\n                    },\n                };\n            });\n            /**\n             * Call waitUntil to extend Workers Event lifetime\n             */\n            if (options.context) {\n                options.context.waitUntil(request);\n            }\n            return request;\n        }\n        catch (e) {\n            return rejectedSyncPromise(e);\n        }\n    }\n    return createTransport(options, makeRequest);\n}\n\n/**\n * The Cloudflare Workers SDK.\n */\nclass Toucan extends Hub {\n    constructor(options) {\n        options.defaultIntegrations =\n            options.defaultIntegrations === false\n                ? []\n                : [\n                    ...(Array.isArray(options.defaultIntegrations)\n                        ? options.defaultIntegrations\n                        : [\n                            new RequestData(options.requestDataOptions),\n                            new LinkedErrors(),\n                        ]),\n                ];\n        if (options.release === undefined) {\n            const detectedRelease = getSentryRelease();\n            if (detectedRelease !== undefined) {\n                options.release = detectedRelease;\n            }\n        }\n        const client = new ToucanClient({\n            ...options,\n            transport: makeFetchTransport,\n            integrations: getIntegrationsToSetup(options),\n            stackParser: stackParserFromStackParserOptions(options.stackParser || defaultStackParser),\n            transportOptions: {\n                ...options.transportOptions,\n                context: options.context,\n            },\n        });\n        super(client);\n        client.setSdk(this);\n        client.setupIntegrations();\n    }\n    /**\n     * Sets the request body context on all future events.\n     *\n     * @param body Request body.\n     * @example\n     * const body = await request.text();\n     * toucan.setRequestBody(body);\n     */\n    setRequestBody(body) {\n        this.getClient()?.setRequestBody(body);\n    }\n    /**\n     * Enable/disable the SDK.\n     *\n     * @param enabled\n     */\n    setEnabled(enabled) {\n        this.getClient()?.setEnabled(enabled);\n    }\n    /**\n     * Create a cron monitor check in and send it to Sentry.\n     *\n     * @param checkIn An object that describes a check in.\n     * @param upsertMonitorConfig An optional object that describes a monitor config. Use this if you want\n     * to create a monitor automatically when sending a check in.\n     */\n    captureCheckIn(checkIn, monitorConfig, scope) {\n        if (checkIn.status === 'in_progress') {\n            this.setContext('monitor', { slug: checkIn.monitorSlug });\n        }\n        const client = this.getClient();\n        return client.captureCheckIn(checkIn, monitorConfig, scope);\n    }\n}\n\nexport { LinkedErrors, RequestData, Toucan };\n", "import { Toucan } from \"toucan-js\";\n\nexport function setupSentry(\n\trequest: Request,\n\tcontext: ExecutionContext | undefined,\n\tdsn: string,\n\tclientId: string,\n\tclientSecret: string\n): Toucan | undefined {\n\t// Are we running locally without access to Sentry secrets? If so, don't initialise Sentry\n\tif (!(dsn && clientId && clientSecret)) {\n\t\treturn undefined;\n\t}\n\tconst sentry = new Toucan({\n\t\tdsn,\n\t\trequest,\n\t\tcontext,\n\t\tsampleRate: 1.0,\n\t\trequestDataOptions: {\n\t\t\tallowedHeaders: [\n\t\t\t\t\"user-agent\",\n\t\t\t\t\"cf-challenge\",\n\t\t\t\t\"accept-encoding\",\n\t\t\t\t\"accept-language\",\n\t\t\t\t\"cf-ray\",\n\t\t\t\t\"content-length\",\n\t\t\t\t\"content-type\",\n\t\t\t\t\"host\",\n\t\t\t],\n\t\t\tallowedSearchParams: /(.*)/,\n\t\t},\n\n\t\ttransportOptions: {\n\t\t\theaders: {\n\t\t\t\t\"CF-Access-Client-ID\": clientId,\n\t\t\t\t\"CF-Access-Client-Secret\": clientSecret,\n\t\t\t},\n\t\t},\n\t});\n\tconst colo = request.cf?.colo ?? \"UNKNOWN\";\n\tsentry.setTag(\"colo\", colo as string);\n\n\tconst userAgent = request.headers.get(\"user-agent\") ?? \"UA UNKNOWN\";\n\tsentry.setUser({ userAgent: userAgent, colo: colo });\n\treturn sentry;\n}\n", "import {\n\tCONTENT_HASH_SIZE,\n\tENTRY_SIZE,\n\tHEADER_SIZE,\n\tPATH_HASH_SIZE,\n} from \"../../utils/constants\";\n\nexport class AssetsManifest {\n\tprivate data: ArrayBuffer;\n\n\tconstructor(data: ArrayBuffer) {\n\t\tthis.data = data;\n\t}\n\n\tasync get(pathname: string) {\n\t\tconst pathHash = await hashPath(pathname);\n\t\tconst entry = binarySearch(\n\t\t\tnew Uint8Array(this.data, HEADER_SIZE),\n\t\t\tpathHash\n\t\t);\n\t\treturn entry ? contentHashToKey(entry) : null;\n\t}\n}\n\nconst hashPath = async (path: string) => {\n\tconst encoder = new TextEncoder();\n\tconst data = encoder.encode(path);\n\tconst hashBuffer = await crypto.subtle.digest(\"SHA-256\", data.buffer);\n\treturn new Uint8Array(hashBuffer, 0, PATH_HASH_SIZE);\n};\n\nconst binarySearch = (\n\tarr: Uint8Array,\n\tsearchValue: Uint8Array\n): Uint8Array | false => {\n\tif (arr.byteLength === 0) {\n\t\treturn false;\n\t}\n\tconst offset =\n\t\tarr.byteOffset + ((arr.byteLength / ENTRY_SIZE) >> 1) * ENTRY_SIZE;\n\tconst current = new Uint8Array(arr.buffer, offset, PATH_HASH_SIZE);\n\tif (current.byteLength !== searchValue.byteLength) {\n\t\tthrow new TypeError(\n\t\t\t\"Search value and current value are of different lengths\"\n\t\t);\n\t}\n\tconst cmp = compare(searchValue, current);\n\tif (cmp < 0) {\n\t\tconst nextOffset = arr.byteOffset;\n\t\tconst nextLength = offset - arr.byteOffset;\n\t\treturn binarySearch(\n\t\t\tnew Uint8Array(arr.buffer, nextOffset, nextLength),\n\t\t\tsearchValue\n\t\t);\n\t} else if (cmp > 0) {\n\t\tconst nextOffset = offset + ENTRY_SIZE;\n\t\tconst nextLength = arr.buffer.byteLength - offset - ENTRY_SIZE;\n\t\treturn binarySearch(\n\t\t\tnew Uint8Array(arr.buffer, nextOffset, nextLength),\n\t\t\tsearchValue\n\t\t);\n\t} else {\n\t\treturn new Uint8Array(arr.buffer, offset, ENTRY_SIZE);\n\t}\n};\n\nconst compare = (a: Uint8Array, b: Uint8Array) => {\n\tif (a.byteLength < b.byteLength) {\n\t\treturn -1;\n\t}\n\tif (a.byteLength > b.byteLength) {\n\t\treturn 1;\n\t}\n\n\tfor (const [i, v] of a.entries()) {\n\t\tif (v < b[i]) {\n\t\t\treturn -1;\n\t\t}\n\t\tif (v > b[i]) {\n\t\t\treturn 1;\n\t\t}\n\t}\n\n\treturn 0;\n};\n\nconst contentHashToKey = (buffer: Uint8Array) => {\n\tconst contentHash = buffer.slice(\n\t\tPATH_HASH_SIZE,\n\t\tPATH_HASH_SIZE + CONTENT_HASH_SIZE\n\t);\n\treturn [...contentHash].map((b) => b.toString(16).padStart(2, \"0\")).join(\"\");\n};\n", "import type { AssetConfig } from \"../../utils/types\";\n\nexport const applyConfigurationDefaults = (\n\tconfiguration?: AssetConfig\n): Required<AssetConfig> => {\n\treturn {\n\t\thtml_handling: configuration?.html_handling ?? \"auto-trailing-slash\",\n\t\tnot_found_handling: configuration?.not_found_handling ?? \"none\",\n\t};\n};\n", "export class OkResponse extends Response {\n\tconstructor(body: BodyInit | null, init?: ResponseInit) {\n\t\tsuper(body, {\n\t\t\t...init,\n\t\t\tstatus: 200,\n\t\t});\n\t}\n}\n\nexport class NotFoundResponse extends Response {\n\tconstructor(...[body, init]: ConstructorParameters<typeof Response>) {\n\t\tsuper(body, {\n\t\t\t...init,\n\t\t\tstatus: 404,\n\t\t\tstatusText: \"Not Found\",\n\t\t});\n\t}\n}\n\nexport class MethodNotAllowedResponse extends Response {\n\tconstructor(...[body, init]: ConstructorParameters<typeof Response>) {\n\t\tsuper(body, {\n\t\t\t...init,\n\t\t\tstatus: 405,\n\t\t\tstatusText: \"Method Not Allowed\",\n\t\t});\n\t}\n}\n\nexport class InternalServerErrorResponse extends Response {\n\tconstructor(err: Error, init?: ResponseInit) {\n\t\tsuper(null, {\n\t\t\t...init,\n\t\t\tstatus: 500,\n\t\t});\n\t}\n}\n\nexport class NotModifiedResponse extends Response {\n\tconstructor(...[_body, init]: ConstructorParameters<typeof Response>) {\n\t\tsuper(null, {\n\t\t\t...init,\n\t\t\tstatus: 304,\n\t\t\tstatusText: \"Not Modified\",\n\t\t});\n\t}\n}\n\nexport class TemporaryRedirectResponse extends Response {\n\tconstructor(location: string, init?: ResponseInit) {\n\t\tsuper(null, {\n\t\t\t...init,\n\t\t\tstatus: 307,\n\t\t\tstatusText: \"Temporary Redirect\",\n\t\t\theaders: {\n\t\t\t\t...init?.headers,\n\t\t\t\tLocation: location,\n\t\t\t},\n\t\t});\n\t}\n}\n", "// have the browser check in with the server to make sure its local cache is valid before using it\nexport const CACHE_CONTROL_BROWSER = \"public, max-age=0, must-revalidate\";\n", "import { CACHE_CONTROL_BROWSER } from \"../constants\";\n\n/**\n * Returns a Headers object that contains additional headers (to those\n * present in the original request) that the Assets Server Worker\n * should attach to its response.\n *\n */\nexport function getHeaders(\n\teTag: string,\n\tcontentType: string,\n\trequest: Request\n) {\n\tconst headers = new Headers({\n\t\t\"Content-Type\": contentType,\n\t\tETag: `\"${eTag}\"`,\n\t});\n\n\tif (isCacheable(request)) {\n\t\theaders.append(\"Cache-Control\", CACHE_CONTROL_BROWSER);\n\t}\n\n\treturn headers;\n}\n\nfunction isCacheable(request: Request) {\n\treturn !request.headers.has(\"Authorization\") && !request.headers.has(\"Range\");\n}\n", "import {\n\tInternalServerErrorResponse,\n\tMethodNotAllowedResponse,\n\tNotFoundResponse,\n\tNotModifiedResponse,\n\tOkResponse,\n\tTemporaryRedirectResponse,\n} from \"./responses\";\nimport { getHeaders } from \"./utils/headers\";\nimport type { AssetConfig } from \"../../utils/types\";\nimport type EntrypointType from \"./index\";\n\nexport const handleRequest = async (\n\trequest: Request,\n\tconfiguration: Required<AssetConfig>,\n\texists: typeof EntrypointType.prototype.unstable_exists,\n\tgetByETag: typeof EntrypointType.prototype.unstable_getByETag\n) => {\n\tconst { pathname, search } = new URL(request.url);\n\n\tconst intent = await getIntent(pathname, configuration, exists);\n\tif (!intent) {\n\t\treturn new NotFoundResponse();\n\t}\n\n\t// if there was a POST etc. to a route without an asset\n\t// this should be passed onto a user worker if one exists\n\t// so prioritise returning a 404 over 405?\n\tconst method = request.method.toUpperCase();\n\tif (![\"GET\", \"HEAD\"].includes(method)) {\n\t\treturn new MethodNotAllowedResponse();\n\t}\n\tif (intent.redirect) {\n\t\treturn new TemporaryRedirectResponse(intent.redirect + search);\n\t}\n\tif (!intent.asset) {\n\t\treturn new InternalServerErrorResponse(new Error(\"Unknown action\"));\n\t}\n\n\tconst asset = await getByETag(intent.asset.eTag);\n\n\tconst headers = getHeaders(intent.asset.eTag, asset.contentType, request);\n\n\tconst strongETag = `\"${intent.asset.eTag}\"`;\n\tconst weakETag = `W/${strongETag}`;\n\tconst ifNoneMatch = request.headers.get(\"If-None-Match\") || \"\";\n\tif ([weakETag, strongETag].includes(ifNoneMatch)) {\n\t\treturn new NotModifiedResponse(null, { headers });\n\t}\n\n\tconst body = method === \"HEAD\" ? null : asset.readableStream;\n\tswitch (intent.asset.status) {\n\t\tcase 404:\n\t\t\treturn new NotFoundResponse(body, { headers });\n\t\tcase 200:\n\t\t\treturn new OkResponse(body, { headers });\n\t}\n};\n\ntype Intent =\n\t| {\n\t\t\tasset: { eTag: string; status: 200 | 404 };\n\t\t\tredirect: null;\n\t  }\n\t| { asset: null; redirect: string }\n\t| null;\n\nexport const getIntent = async (\n\tpathname: string,\n\tconfiguration: Required<AssetConfig>,\n\texists: typeof EntrypointType.prototype.unstable_exists,\n\tskipRedirects = false\n): Promise<Intent> => {\n\tswitch (configuration.html_handling) {\n\t\tcase \"auto-trailing-slash\": {\n\t\t\treturn htmlHandlingAutoTrailingSlash(\n\t\t\t\tpathname,\n\t\t\t\tconfiguration,\n\t\t\t\texists,\n\t\t\t\tskipRedirects\n\t\t\t);\n\t\t}\n\t\tcase \"force-trailing-slash\": {\n\t\t\treturn htmlHandlingForceTrailingSlash(\n\t\t\t\tpathname,\n\t\t\t\tconfiguration,\n\t\t\t\texists,\n\t\t\t\tskipRedirects\n\t\t\t);\n\t\t}\n\t\tcase \"drop-trailing-slash\": {\n\t\t\treturn htmlHandlingDropTrailingSlash(\n\t\t\t\tpathname,\n\t\t\t\tconfiguration,\n\t\t\t\texists,\n\t\t\t\tskipRedirects\n\t\t\t);\n\t\t}\n\t\tcase \"none\": {\n\t\t\treturn htmlHandlingNone(pathname, configuration, exists);\n\t\t}\n\t}\n};\n\nconst htmlHandlingAutoTrailingSlash = async (\n\tpathname: string,\n\tconfiguration: Required<AssetConfig>,\n\texists: typeof EntrypointType.prototype.unstable_exists,\n\tskipRedirects: boolean\n): Promise<Intent> => {\n\tlet redirectResult: Intent = null;\n\tlet eTagResult: string | null = null;\n\tconst exactETag = await exists(pathname);\n\tif (pathname.endsWith(\"/index\")) {\n\t\tif (exactETag) {\n\t\t\t// there's a binary /index file\n\t\t\treturn { asset: { eTag: exactETag, status: 200 }, redirect: null };\n\t\t} else {\n\t\t\tif (\n\t\t\t\t(redirectResult = await safeRedirect(\n\t\t\t\t\t`${pathname}.html`,\n\t\t\t\t\tpathname.slice(0, -\"index\".length),\n\t\t\t\t\tconfiguration,\n\t\t\t\t\texists,\n\t\t\t\t\tskipRedirects\n\t\t\t\t))\n\t\t\t) {\n\t\t\t\t// /foo/index.html exists so redirect to /foo/\n\t\t\t\treturn redirectResult;\n\t\t\t} else if (\n\t\t\t\t(redirectResult = await safeRedirect(\n\t\t\t\t\t`${pathname.slice(0, -\"/index\".length)}.html`,\n\t\t\t\t\tpathname.slice(0, -\"/index\".length),\n\t\t\t\t\tconfiguration,\n\t\t\t\t\texists,\n\t\t\t\t\tskipRedirects\n\t\t\t\t))\n\t\t\t) {\n\t\t\t\t// /foo.html exists so redirect to /foo\n\t\t\t\treturn redirectResult;\n\t\t\t}\n\t\t}\n\t} else if (pathname.endsWith(\"/index.html\")) {\n\t\tif (\n\t\t\t(redirectResult = await safeRedirect(\n\t\t\t\tpathname,\n\t\t\t\tpathname.slice(0, -\"index.html\".length),\n\t\t\t\tconfiguration,\n\t\t\t\texists,\n\t\t\t\tskipRedirects\n\t\t\t))\n\t\t) {\n\t\t\t// /foo/index.html exists so redirect to /foo/\n\t\t\treturn redirectResult;\n\t\t} else if (\n\t\t\t(redirectResult = await safeRedirect(\n\t\t\t\t`${pathname.slice(0, -\"/index.html\".length)}.html`,\n\t\t\t\tpathname.slice(0, -\"/index.html\".length),\n\t\t\t\tconfiguration,\n\t\t\t\texists,\n\t\t\t\tskipRedirects\n\t\t\t))\n\t\t) {\n\t\t\t// /foo.html exists so redirect to /foo\n\t\t\treturn redirectResult;\n\t\t}\n\t} else if (pathname.endsWith(\"/\")) {\n\t\tif ((eTagResult = await exists(`${pathname}index.html`))) {\n\t\t\t// /foo/index.html exists so serve at /foo/\n\t\t\treturn { asset: { eTag: eTagResult, status: 200 }, redirect: null };\n\t\t} else if (\n\t\t\t(redirectResult = await safeRedirect(\n\t\t\t\t`${pathname.slice(0, -\"/\".length)}.html`,\n\t\t\t\tpathname.slice(0, -\"/\".length),\n\t\t\t\tconfiguration,\n\t\t\t\texists,\n\t\t\t\tskipRedirects\n\t\t\t))\n\t\t) {\n\t\t\t// /foo.html exists so redirect to /foo\n\t\t\treturn redirectResult;\n\t\t}\n\t} else if (pathname.endsWith(\".html\")) {\n\t\tif (\n\t\t\t(redirectResult = await safeRedirect(\n\t\t\t\tpathname,\n\t\t\t\tpathname.slice(0, -\".html\".length),\n\t\t\t\tconfiguration,\n\t\t\t\texists,\n\t\t\t\tskipRedirects\n\t\t\t))\n\t\t) {\n\t\t\t// /foo.html exists so redirect to /foo\n\t\t\treturn redirectResult;\n\t\t} else if (\n\t\t\t(redirectResult = await safeRedirect(\n\t\t\t\t`${pathname.slice(0, -\".html\".length)}/index.html`,\n\t\t\t\t`${pathname.slice(0, -\".html\".length)}/`,\n\t\t\t\tconfiguration,\n\t\t\t\texists,\n\t\t\t\tskipRedirects\n\t\t\t))\n\t\t) {\n\t\t\t// request for /foo.html but /foo/index.html exists so redirect to /foo/\n\t\t\treturn redirectResult;\n\t\t}\n\t}\n\n\tif (exactETag) {\n\t\t// there's a binary /foo file\n\t\treturn { asset: { eTag: exactETag, status: 200 }, redirect: null };\n\t} else if ((eTagResult = await exists(`${pathname}.html`))) {\n\t\t// foo.html exists so serve at /foo\n\t\treturn { asset: { eTag: eTagResult, status: 200 }, redirect: null };\n\t} else if (\n\t\t(redirectResult = await safeRedirect(\n\t\t\t`${pathname}/index.html`,\n\t\t\t`${pathname}/`,\n\t\t\tconfiguration,\n\t\t\texists,\n\t\t\tskipRedirects\n\t\t))\n\t) {\n\t\t// /foo/index.html exists so redirect to /foo/\n\t\treturn redirectResult;\n\t}\n\n\treturn notFound(pathname, configuration, exists);\n};\n\nconst htmlHandlingForceTrailingSlash = async (\n\tpathname: string,\n\tconfiguration: Required<AssetConfig>,\n\texists: typeof EntrypointType.prototype.unstable_exists,\n\tskipRedirects: boolean\n): Promise<Intent> => {\n\tlet redirectResult: Intent = null;\n\tlet eTagResult: string | null = null;\n\tconst exactETag = await exists(pathname);\n\tif (pathname.endsWith(\"/index\")) {\n\t\tif (exactETag) {\n\t\t\t// there's a binary /index file\n\t\t\treturn { asset: { eTag: exactETag, status: 200 }, redirect: null };\n\t\t} else {\n\t\t\tif (\n\t\t\t\t(redirectResult = await safeRedirect(\n\t\t\t\t\t`${pathname}.html`,\n\t\t\t\t\tpathname.slice(0, -\"index\".length),\n\t\t\t\t\tconfiguration,\n\t\t\t\t\texists,\n\t\t\t\t\tskipRedirects\n\t\t\t\t))\n\t\t\t) {\n\t\t\t\t// /foo/index.html exists so redirect to /foo/\n\t\t\t\treturn redirectResult;\n\t\t\t} else if (\n\t\t\t\t(redirectResult = await safeRedirect(\n\t\t\t\t\t`${pathname.slice(0, -\"/index\".length)}.html`,\n\t\t\t\t\tpathname.slice(0, -\"index\".length),\n\t\t\t\t\tconfiguration,\n\t\t\t\t\texists,\n\t\t\t\t\tskipRedirects\n\t\t\t\t))\n\t\t\t) {\n\t\t\t\t// /foo.html exists so redirect to /foo/\n\t\t\t\treturn redirectResult;\n\t\t\t}\n\t\t}\n\t} else if (pathname.endsWith(\"/index.html\")) {\n\t\tif (\n\t\t\t(redirectResult = await safeRedirect(\n\t\t\t\tpathname,\n\t\t\t\tpathname.slice(0, -\"index.html\".length),\n\t\t\t\tconfiguration,\n\t\t\t\texists,\n\t\t\t\tskipRedirects\n\t\t\t))\n\t\t) {\n\t\t\t// /foo/index.html exists so redirect to /foo/\n\t\t\treturn redirectResult;\n\t\t} else if (\n\t\t\t(redirectResult = await safeRedirect(\n\t\t\t\t`${pathname.slice(0, -\"/index.html\".length)}.html`,\n\t\t\t\tpathname.slice(0, -\"index.html\".length),\n\t\t\t\tconfiguration,\n\t\t\t\texists,\n\t\t\t\tskipRedirects\n\t\t\t))\n\t\t) {\n\t\t\t// /foo.html exists so redirect to /foo/\n\t\t\treturn redirectResult;\n\t\t}\n\t} else if (pathname.endsWith(\"/\")) {\n\t\tif ((eTagResult = await exists(`${pathname}index.html`))) {\n\t\t\t// /foo/index.html exists so serve at /foo/\n\t\t\treturn { asset: { eTag: eTagResult, status: 200 }, redirect: null };\n\t\t} else if (\n\t\t\t(eTagResult = await exists(`${pathname.slice(0, -\"/\".length)}.html`))\n\t\t) {\n\t\t\t// /foo.html exists so serve at /foo/\n\t\t\treturn { asset: { eTag: eTagResult, status: 200 }, redirect: null };\n\t\t}\n\t} else if (pathname.endsWith(\".html\")) {\n\t\tif (\n\t\t\t(redirectResult = await safeRedirect(\n\t\t\t\tpathname,\n\t\t\t\t`${pathname.slice(0, -\".html\".length)}/`,\n\t\t\t\tconfiguration,\n\t\t\t\texists,\n\t\t\t\tskipRedirects\n\t\t\t))\n\t\t) {\n\t\t\t// /foo.html exists so redirect to /foo/\n\t\t\treturn redirectResult;\n\t\t} else if (exactETag) {\n\t\t\t// there's both /foo.html and /foo/index.html so we serve /foo.html at /foo.html only\n\t\t\treturn { asset: { eTag: exactETag, status: 200 }, redirect: null };\n\t\t} else if (\n\t\t\t(redirectResult = await safeRedirect(\n\t\t\t\t`${pathname.slice(0, -\".html\".length)}/index.html`,\n\t\t\t\t`${pathname.slice(0, -\".html\".length)}/`,\n\t\t\t\tconfiguration,\n\t\t\t\texists,\n\t\t\t\tskipRedirects\n\t\t\t))\n\t\t) {\n\t\t\t// /foo/index.html exists so redirect to /foo/\n\t\t\treturn redirectResult;\n\t\t}\n\t}\n\n\tif (exactETag) {\n\t\t// there's a binary /foo file\n\t\treturn { asset: { eTag: exactETag, status: 200 }, redirect: null };\n\t} else if (\n\t\t(redirectResult = await safeRedirect(\n\t\t\t`${pathname}.html`,\n\t\t\t`${pathname}/`,\n\t\t\tconfiguration,\n\t\t\texists,\n\t\t\tskipRedirects\n\t\t))\n\t) {\n\t\t// /foo.html exists so redirect to /foo/\n\t\treturn redirectResult;\n\t} else if (\n\t\t(redirectResult = await safeRedirect(\n\t\t\t`${pathname}/index.html`,\n\t\t\t`${pathname}/`,\n\t\t\tconfiguration,\n\t\t\texists,\n\t\t\tskipRedirects\n\t\t))\n\t) {\n\t\t// /foo/index.html exists so redirect to /foo/\n\t\treturn redirectResult;\n\t}\n\n\treturn notFound(pathname, configuration, exists);\n};\n\nconst htmlHandlingDropTrailingSlash = async (\n\tpathname: string,\n\tconfiguration: Required<AssetConfig>,\n\texists: typeof EntrypointType.prototype.unstable_exists,\n\tskipRedirects: boolean\n): Promise<Intent> => {\n\tlet redirectResult: Intent = null;\n\tlet eTagResult: string | null = null;\n\tconst exactETag = await exists(pathname);\n\tif (pathname.endsWith(\"/index\")) {\n\t\tif (exactETag) {\n\t\t\t// there's a binary /index file\n\t\t\treturn { asset: { eTag: exactETag, status: 200 }, redirect: null };\n\t\t} else {\n\t\t\tif (pathname === \"/index\") {\n\t\t\t\tif (\n\t\t\t\t\t(redirectResult = await safeRedirect(\n\t\t\t\t\t\t\"/index.html\",\n\t\t\t\t\t\t\"/\",\n\t\t\t\t\t\tconfiguration,\n\t\t\t\t\t\texists,\n\t\t\t\t\t\tskipRedirects\n\t\t\t\t\t))\n\t\t\t\t) {\n\t\t\t\t\treturn redirectResult;\n\t\t\t\t}\n\t\t\t} else if (\n\t\t\t\t(redirectResult = await safeRedirect(\n\t\t\t\t\t`${pathname.slice(0, -\"/index\".length)}.html`,\n\t\t\t\t\tpathname.slice(0, -\"/index\".length),\n\t\t\t\t\tconfiguration,\n\t\t\t\t\texists,\n\t\t\t\t\tskipRedirects\n\t\t\t\t))\n\t\t\t) {\n\t\t\t\t// /foo.html exists so redirect to /foo\n\t\t\t\treturn redirectResult;\n\t\t\t} else if (\n\t\t\t\t(redirectResult = await safeRedirect(\n\t\t\t\t\t`${pathname}.html`,\n\t\t\t\t\tpathname.slice(0, -\"/index\".length),\n\t\t\t\t\tconfiguration,\n\t\t\t\t\texists,\n\t\t\t\t\tskipRedirects\n\t\t\t\t))\n\t\t\t) {\n\t\t\t\t// /foo/index.html exists so redirect to /foo\n\t\t\t\treturn redirectResult;\n\t\t\t}\n\t\t}\n\t} else if (pathname.endsWith(\"/index.html\")) {\n\t\t// special handling so you don't drop / if the path is just /\n\t\tif (pathname === \"/index.html\") {\n\t\t\tif (\n\t\t\t\t(redirectResult = await safeRedirect(\n\t\t\t\t\t\"/index.html\",\n\t\t\t\t\t\"/\",\n\t\t\t\t\tconfiguration,\n\t\t\t\t\texists,\n\t\t\t\t\tskipRedirects\n\t\t\t\t))\n\t\t\t) {\n\t\t\t\treturn redirectResult;\n\t\t\t}\n\t\t} else if (\n\t\t\t(redirectResult = await safeRedirect(\n\t\t\t\tpathname,\n\t\t\t\tpathname.slice(0, -\"/index.html\".length),\n\t\t\t\tconfiguration,\n\t\t\t\texists,\n\t\t\t\tskipRedirects\n\t\t\t))\n\t\t) {\n\t\t\t// /foo/index.html exists so redirect to /foo\n\t\t\treturn redirectResult;\n\t\t} else if (exactETag) {\n\t\t\t// there's both /foo.html and /foo/index.html so we serve /foo/index.html at /foo/index.html only\n\t\t\treturn { asset: { eTag: exactETag, status: 200 }, redirect: null };\n\t\t} else if (\n\t\t\t(redirectResult = await safeRedirect(\n\t\t\t\t`${pathname.slice(0, -\"/index.html\".length)}.html`,\n\t\t\t\tpathname.slice(0, -\"/index.html\".length),\n\t\t\t\tconfiguration,\n\t\t\t\texists,\n\t\t\t\tskipRedirects\n\t\t\t))\n\t\t) {\n\t\t\t// /foo.html exists so redirect to /foo\n\t\t\treturn redirectResult;\n\t\t}\n\t} else if (pathname.endsWith(\"/\")) {\n\t\tif (pathname === \"/\") {\n\t\t\tif ((eTagResult = await exists(\"/index.html\"))) {\n\t\t\t\t// /index.html exists so serve at /\n\t\t\t\treturn { asset: { eTag: eTagResult, status: 200 }, redirect: null };\n\t\t\t}\n\t\t} else if (\n\t\t\t(redirectResult = await safeRedirect(\n\t\t\t\t`${pathname.slice(0, -\"/\".length)}.html`,\n\t\t\t\tpathname.slice(0, -\"/\".length),\n\t\t\t\tconfiguration,\n\t\t\t\texists,\n\t\t\t\tskipRedirects\n\t\t\t))\n\t\t) {\n\t\t\t// /foo.html exists so redirect to /foo\n\t\t\treturn redirectResult;\n\t\t} else if (\n\t\t\t(redirectResult = await safeRedirect(\n\t\t\t\t`${pathname.slice(0, -\"/\".length)}/index.html`,\n\t\t\t\tpathname.slice(0, -\"/\".length),\n\t\t\t\tconfiguration,\n\t\t\t\texists,\n\t\t\t\tskipRedirects\n\t\t\t))\n\t\t) {\n\t\t\t// /foo/index.html exists so redirect to /foo\n\t\t\treturn redirectResult;\n\t\t}\n\t} else if (pathname.endsWith(\".html\")) {\n\t\tif (\n\t\t\t(redirectResult = await safeRedirect(\n\t\t\t\tpathname,\n\t\t\t\tpathname.slice(0, -\".html\".length),\n\t\t\t\tconfiguration,\n\t\t\t\texists,\n\t\t\t\tskipRedirects\n\t\t\t))\n\t\t) {\n\t\t\t// /foo.html exists so redirect to /foo\n\t\t\treturn redirectResult;\n\t\t} else if (\n\t\t\t(redirectResult = await safeRedirect(\n\t\t\t\t`${pathname.slice(0, -\".html\".length)}/index.html`,\n\t\t\t\tpathname.slice(0, -\".html\".length),\n\t\t\t\tconfiguration,\n\t\t\t\texists,\n\t\t\t\tskipRedirects\n\t\t\t))\n\t\t) {\n\t\t\t// /foo/index.html exists so redirect to /foo\n\t\t\treturn redirectResult;\n\t\t}\n\t}\n\n\tif (exactETag) {\n\t\t// there's a binary /foo file\n\t\treturn { asset: { eTag: exactETag, status: 200 }, redirect: null };\n\t} else if ((eTagResult = await exists(`${pathname}.html`))) {\n\t\t// /foo.html exists so serve at /foo\n\t\treturn { asset: { eTag: eTagResult, status: 200 }, redirect: null };\n\t} else if ((eTagResult = await exists(`${pathname}/index.html`))) {\n\t\t// /foo/index.html exists so serve at /foo\n\t\treturn { asset: { eTag: eTagResult, status: 200 }, redirect: null };\n\t}\n\n\treturn notFound(pathname, configuration, exists);\n};\n\nconst htmlHandlingNone = async (\n\tpathname: string,\n\tconfiguration: Required<AssetConfig>,\n\texists: typeof EntrypointType.prototype.unstable_exists\n): Promise<Intent> => {\n\tconst exactETag = await exists(pathname);\n\tif (exactETag) {\n\t\treturn { asset: { eTag: exactETag, status: 200 }, redirect: null };\n\t} else {\n\t\treturn notFound(pathname, configuration, exists);\n\t}\n};\n\nconst notFound = async (\n\tpathname: string,\n\tconfiguration: Required<AssetConfig>,\n\texists: typeof EntrypointType.prototype.unstable_exists\n): Promise<Intent> => {\n\tswitch (configuration.not_found_handling) {\n\t\tcase \"single-page-application\": {\n\t\t\tconst eTag = await exists(\"/index.html\");\n\t\t\tif (eTag) {\n\t\t\t\treturn { asset: { eTag, status: 200 }, redirect: null };\n\t\t\t}\n\t\t\treturn null;\n\t\t}\n\t\tcase \"404-page\": {\n\t\t\tlet cwd = pathname;\n\t\t\twhile (cwd) {\n\t\t\t\tcwd = cwd.slice(0, cwd.lastIndexOf(\"/\"));\n\t\t\t\tconst eTag = await exists(`${cwd}/404.html`);\n\t\t\t\tif (eTag) {\n\t\t\t\t\treturn { asset: { eTag, status: 404 }, redirect: null };\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn null;\n\t\t}\n\t\tcase \"none\":\n\t\tdefault: {\n\t\t\treturn null;\n\t\t}\n\t}\n};\n\nconst safeRedirect = async (\n\tfile: string,\n\tdestination: string,\n\tconfiguration: Required<AssetConfig>,\n\texists: typeof EntrypointType.prototype.unstable_exists,\n\tskip: boolean\n): Promise<Intent> => {\n\tif (skip) {\n\t\treturn null;\n\t}\n\n\tif (!(await exists(destination))) {\n\t\tconst intent = await getIntent(destination, configuration, exists, true);\n\t\tif (intent?.asset && intent.asset.eTag === (await exists(file))) {\n\t\t\treturn {\n\t\t\t\tasset: null,\n\t\t\t\tredirect: destination,\n\t\t\t};\n\t\t}\n\t}\n\n\treturn null;\n};\n", "export type AssetMetadata = {\n\tcontentType: string;\n};\n\nexport async function getAssetWithMetadataFromKV(\n\tassetsKVNamespace: KVNamespace,\n\tassetKey: string,\n\tretries = 1\n) {\n\tlet attempts = 0;\n\n\twhile (attempts <= retries) {\n\t\ttry {\n\t\t\treturn await assetsKVNamespace.getWithMetadata<AssetMetadata>(assetKey, {\n\t\t\t\ttype: \"stream\",\n\t\t\t\tcacheTtl: 31536000, // 1 year\n\t\t\t});\n\t\t} catch (err) {\n\t\t\tif (attempts >= retries) {\n\t\t\t\tthrow new Error(\n\t\t\t\t\t`Requested asset ${assetKey} could not be fetched from KV namespace.`\n\t\t\t\t);\n\t\t\t}\n\n\t\t\t// Exponential backoff, 1 second first time, then 2 second, then 4 second etc.\n\t\t\tawait new Promise((resolvePromise) =>\n\t\t\t\tsetTimeout(resolvePromise, Math.pow(2, attempts++) * 1000)\n\t\t\t);\n\t\t}\n\t}\n}\n"], "mappings": "kgBAAA,OAAS,oBAAAA,OAAwB,qBCMjC,IAAAC,GAAA,OAAA,UAAA,SASA,SAAAC,EAAAC,EAAA,CACA,OAAAF,GAAA,KAAAE,CAAA,EAAA,CACA,IAAA,iBACA,IAAA,qBACA,IAAA,wBACA,MAAA,GACA,QACA,OAAAC,EAAAD,EAAA,KAAA,CACA,CACA,CAQA,SAAAE,GAAAF,EAAAG,EAAA,CACA,OAAAL,GAAA,KAAAE,CAAA,IAAA,WAAAG,IACA,CA0CA,SAAAC,EAAAC,EAAA,CACA,OAAAC,GAAAD,EAAA,QAAA,CACA,CASA,SAAAE,GAAAF,EAAA,CACA,OAAAA,IAAA,MAAA,OAAAA,GAAA,UAAA,OAAAA,GAAA,UACA,CASA,SAAAG,EAAAH,EAAA,CACA,OAAAC,GAAAD,EAAA,QAAA,CACA,CASA,SAAAI,GAAAJ,EAAA,CACA,OAAA,OAAA,MAAA,KAAAK,EAAAL,EAAA,KAAA,CACA,CASA,SAAAM,GAAAN,EAAA,CACA,OAAA,OAAA,QAAA,KAAAK,EAAAL,EAAA,OAAA,CACA,CAiBA,SAAAO,EAAAC,EAAA,CAEA,MAAA,GAAAA,GAAAA,EAAA,MAAA,OAAAA,EAAA,MAAA,WACA,CASA,SAAAC,GAAAD,EAAA,CACA,OAAAE,EAAAF,CAAA,GAAA,gBAAAA,GAAA,mBAAAA,GAAA,oBAAAA,CACA,CASA,SAAAG,GAAAH,EAAA,CACA,OAAA,OAAAA,GAAA,UAAAA,IAAAA,CACA,CAUA,SAAAI,EAAAJ,EAAAK,EAAA,CACA,GAAA,CACA,OAAAL,aAAAK,CACA,MAAA,CACA,MAAA,EACA,CACA,CAcA,SAAAC,GAAAN,EAAA,CAEA,MAAA,CAAA,EAAA,OAAAA,GAAA,UAAAA,IAAA,OAAAA,EAAA,SAAAA,EAAA,QACA,CC1LA,SAAAO,EAAAC,EAAAC,EAAA,EAAA,CACA,OAAA,OAAAD,GAAA,UAAAC,IAAA,GAGAD,EAAA,QAAAC,EAFAD,EAEA,GAAAA,EAAA,MAAA,EAAAC,CAAA,MACA,CC0EA,SAAAC,GAAAC,EAAA,CACA,OAAAA,GAAAA,EAAA,MAAA,KAAAA,EAAA,MACA,CAGA,IAAAC,EACA,OAAA,YAAA,UAAAF,GAAA,UAAA,GAEA,OAAA,QAAA,UAAAA,GAAA,MAAA,GACA,OAAA,MAAA,UAAAA,GAAA,IAAA,GACA,OAAA,QAAA,UAAAA,GAAA,MAAA,GACA,UAAA,CACA,OAAA,IACA,EAAA,GACA,CAAA,EAKA,SAAAG,GAAA,CACA,OAAAD,CACA,CAaA,SAAAE,GAAAC,EAAAC,EAAAL,EAAA,CACA,IAAAM,EAAAN,GAAAC,EACAM,EAAAD,EAAA,WAAAA,EAAA,YAAA,CAAA,EAEA,OADAC,EAAAH,CAAA,IAAAG,EAAAH,CAAA,EAAAC,EAAA,EAEA,CC7HA,IAAAG,GAAAC,EAAA,EAEAC,GAAA,GAQA,SAAAC,GACAC,EACAC,EAAA,CAAA,EACA,CAKA,GAAA,CAAAD,EACA,MAAA,YAOA,GAAA,CACA,IAAAE,EAAAF,EACAG,EAAA,EACAC,EAAA,CAAA,EACAC,EAAA,EACAC,EAAA,EACAC,EAAA,MACAC,EAAAD,EAAA,OACAE,EACAC,EAAA,MAAA,QAAAT,CAAA,EAAAA,EAAAA,EAAA,SACAU,EAAA,CAAA,MAAA,QAAAV,CAAA,GAAAA,EAAA,iBAAAH,GAEA,KAAAI,GAAAG,IAAAF,IACAM,EAAAG,GAAAV,EAAAQ,CAAA,EAKA,EAAAD,IAAA,QAAAJ,EAAA,GAAAC,EAAAF,EAAA,OAAAI,EAAAC,EAAA,QAAAE,KAIAP,EAAA,KAAAK,CAAA,EAEAH,GAAAG,EAAA,OACAP,EAAAA,EAAA,WAGA,OAAAE,EAAA,QAAA,EAAA,KAAAG,CAAA,CACA,MAAA,CACA,MAAA,WACA,CACA,CAOA,SAAAK,GAAAC,EAAAH,EAAA,CACA,IAAAV,EAAAa,EAOAT,EAAA,CAAA,EACAU,EACAC,EACAC,EACAC,EACAC,EAEA,GAAA,CAAAlB,GAAA,CAAAA,EAAA,QACA,MAAA,GAGAI,EAAA,KAAAJ,EAAA,QAAA,YAAA,CAAA,EAGA,IAAAmB,EACAT,GAAAA,EAAA,OACAA,EAAA,OAAAU,GAAApB,EAAA,aAAAoB,CAAA,CAAA,EAAA,IAAAA,GAAA,CAAAA,EAAApB,EAAA,aAAAoB,CAAA,CAAA,CAAA,EACA,KAEA,GAAAD,GAAAA,EAAA,OACAA,EAAA,QAAAE,GAAA,CACAjB,EAAA,KAAA,IAAAiB,EAAA,CAAA,MAAAA,EAAA,CAAA,KAAA,CACA,CAAA,UAEArB,EAAA,IACAI,EAAA,KAAA,IAAAJ,EAAA,IAAA,EAIAc,EAAAd,EAAA,UACAc,GAAAQ,EAAAR,CAAA,EAEA,IADAC,EAAAD,EAAA,MAAA,KAAA,EACAI,EAAA,EAAAA,EAAAH,EAAA,OAAAG,IACAd,EAAA,KAAA,IAAAW,EAAAG,CAAA,GAAA,EAIA,IAAAK,EAAA,CAAA,aAAA,OAAA,OAAA,QAAA,KAAA,EACA,IAAAL,EAAA,EAAAA,EAAAK,EAAA,OAAAL,IACAF,EAAAO,EAAAL,CAAA,EACAD,EAAAjB,EAAA,aAAAgB,CAAA,EACAC,GACAb,EAAA,KAAA,IAAAY,MAAAC,KAAA,EAGA,OAAAb,EAAA,KAAA,EAAA,CACA,CCxHA,IAAAoB,GAAA,iBAEAC,GAAA,CAAA,QAAA,OAAA,OAAA,QAAA,MAAA,SAAA,OAAA,EAOAC,EAGA,CAAA,EAeA,SAAAC,GAAAC,EAAA,CACA,GAAA,EAAA,YAAAC,GACA,OAAAD,EAAA,EAGA,IAAAE,EAAAD,EAAA,QACAE,EAAA,CAAA,EAEAC,EAAA,OAAA,KAAAN,CAAA,EAGAM,EAAA,QAAAC,GAAA,CACA,IAAAC,EAAAR,EAAAO,CAAA,EACAF,EAAAE,CAAA,EAAAH,EAAAG,CAAA,EACAH,EAAAG,CAAA,EAAAC,CACA,CAAA,EAEA,GAAA,CACA,OAAAN,EAAA,CACA,QAAA,CAEAI,EAAA,QAAAC,GAAA,CACAH,EAAAG,CAAA,EAAAF,EAAAE,CAAA,CACA,CAAA,CACA,CACA,CAEA,SAAAE,IAAA,CACA,IAAAC,EAAA,GACAC,EAAA,CACA,OAAA,IAAA,CACAD,EAAA,EACA,EACA,QAAA,IAAA,CACAA,EAAA,EACA,EACA,UAAA,IAAAA,CACA,EAEA,OAAA,OAAA,iBAAA,KAAA,iBACAX,GAAA,QAAAa,GAAA,CAEAD,EAAAC,CAAA,EAAA,IAAAC,IAAA,CACAH,GACAT,GAAA,IAAA,CACAE,EAAA,QAAAS,CAAA,EAAA,GAAAd,MAAAc,MAAA,GAAAC,CAAA,CACA,CAAA,CAEA,CACA,CAAA,EAEAd,GAAA,QAAAa,GAAA,CACAD,EAAAC,CAAA,EAAA,IAAA,EACA,CAAA,EAGAD,CACA,CAEA,IAAAA,EAAAF,GAAA,ECpFA,IAAAK,GAAA,kEAEA,SAAAC,GAAAC,EAAA,CACA,OAAAA,IAAA,QAAAA,IAAA,OACA,CAWA,SAAAC,EAAAC,EAAAC,EAAA,GAAA,CACA,GAAA,CAAA,KAAAC,EAAA,KAAAC,EAAA,KAAAC,EAAA,KAAAC,EAAA,UAAAC,EAAA,SAAAR,EAAA,UAAAS,CAAA,EAAAP,EACA,MACA,GAAAF,OAAAS,IAAAN,GAAAG,EAAA,IAAAA,IAAA,MACAF,IAAAG,EAAA,IAAAA,IAAA,MAAAF,GAAA,GAAAA,OAAAG,GAEA,CAQA,SAAAE,GAAAC,EAAA,CACA,IAAAC,EAAAd,GAAA,KAAAa,CAAA,EAEA,GAAA,CAAAC,EAAA,CAGA,QAAA,MAAA,uBAAAD,GAAA,EACA,OAGA,GAAA,CAAAX,EAAAS,EAAAH,EAAA,GAAAF,EAAAG,EAAA,GAAAM,CAAA,EAAAD,EAAA,MAAA,CAAA,EACAP,EAAA,GACAG,EAAAK,EAEAC,EAAAN,EAAA,MAAA,GAAA,EAMA,GALAM,EAAA,OAAA,IACAT,EAAAS,EAAA,MAAA,EAAA,EAAA,EAAA,KAAA,GAAA,EACAN,EAAAM,EAAA,IAAA,GAGAN,EAAA,CACA,IAAAO,EAAAP,EAAA,MAAA,MAAA,EACAO,IACAP,EAAAO,EAAA,CAAA,GAIA,OAAAC,GAAA,CAAA,KAAAZ,EAAA,KAAAE,EAAA,KAAAD,EAAA,UAAAG,EAAA,KAAAD,EAAA,SAAAP,EAAA,UAAAS,CAAA,CAAA,CACA,CAEA,SAAAO,GAAAC,EAAA,CACA,MAAA,CACA,SAAAA,EAAA,SACA,UAAAA,EAAA,WAAA,GACA,KAAAA,EAAA,MAAA,GACA,KAAAA,EAAA,KACA,KAAAA,EAAA,MAAA,GACA,KAAAA,EAAA,MAAA,GACA,UAAAA,EAAA,SACA,CACA,CAEA,SAAAC,GAAAhB,EAAA,CACA,GAAA,EAAA,OAAA,iBAAA,KAAA,kBACA,MAAA,GAGA,GAAA,CAAA,KAAAK,EAAA,UAAAC,EAAA,SAAAR,CAAA,EAAAE,EAWA,MATA,CAAA,WAAA,YAAA,OAAA,WAAA,EACA,KAAAiB,GACAjB,EAAAiB,CAAA,EAIA,IAHAC,EAAA,MAAA,uBAAAD,WAAA,EACA,GAGA,EAGA,GAGAX,EAAA,MAAA,OAAA,EAKAT,GAAAC,CAAA,EAKAO,GAAA,MAAA,SAAAA,EAAA,EAAA,CAAA,GACAa,EAAA,MAAA,oCAAAb,GAAA,EACA,IAGA,IATAa,EAAA,MAAA,wCAAApB,GAAA,EACA,KANAoB,EAAA,MAAA,yCAAAZ,GAAA,EACA,GAcA,CAMA,SAAAa,GAAAC,EAAA,CACA,IAAAL,EAAA,OAAAK,GAAA,SAAAZ,GAAAY,CAAA,EAAAN,GAAAM,CAAA,EACA,GAAA,GAAAL,GAAA,CAAAC,GAAAD,CAAA,GAGA,OAAAA,CACA,CCzHA,IAAAM,EAAA,cAAA,KAAA,CAMA,YAAAC,EAAAC,EAAA,OAAA,CACA,MAAAD,CAAA,EAAA,KAAA,QAAAA,EAEA,KAAA,KAAA,WAAA,UAAA,YAAA,KAIA,OAAA,eAAA,KAAA,WAAA,SAAA,EACA,KAAA,SAAAC,CACA,CACA,ECCA,SAAAC,EAAAC,EAAAC,EAAAC,EAAA,CACA,GAAA,EAAAD,KAAAD,GACA,OAGA,IAAAG,EAAAH,EAAAC,CAAA,EACAG,EAAAF,EAAAC,CAAA,EAIA,OAAAC,GAAA,YACAC,GAAAD,EAAAD,CAAA,EAGAH,EAAAC,CAAA,EAAAG,CACA,CASA,SAAAE,EAAAC,EAAAN,EAAAO,EAAA,CACA,GAAA,CACA,OAAA,eAAAD,EAAAN,EAAA,CAEA,MAAAO,EACA,SAAA,GACA,aAAA,EACA,CAAA,CACA,MAAA,EACA,OAAA,iBAAA,KAAA,mBAAAC,EAAA,IAAA,0CAAAR,eAAAM,CAAA,CACA,CACA,CASA,SAAAF,GAAAD,EAAAD,EAAA,CACA,GAAA,CACA,IAAAO,EAAAP,EAAA,WAAA,CAAA,EACAC,EAAA,UAAAD,EAAA,UAAAO,EACAJ,EAAAF,EAAA,sBAAAD,CAAA,CACA,MAAA,CAAA,CACA,CAmBA,SAAAQ,GAAAC,EAAA,CACA,OAAA,OAAA,KAAAA,CAAA,EACA,IAAAC,GAAA,GAAA,mBAAAA,CAAA,KAAA,mBAAAD,EAAAC,CAAA,CAAA,GAAA,EACA,KAAA,GAAA,CACA,CAUA,SAAAC,GAAAC,EAcA,CACA,GAAAC,EAAAD,CAAA,EACA,MAAA,CACA,QAAAA,EAAA,QACA,KAAAA,EAAA,KACA,MAAAA,EAAA,MACA,GAAAE,GAAAF,CAAA,CACA,EACA,GAAAG,GAAAH,CAAA,EAAA,CACA,IAAAI,EAMA,CACA,KAAAJ,EAAA,KACA,OAAAK,GAAAL,EAAA,MAAA,EACA,cAAAK,GAAAL,EAAA,aAAA,EACA,GAAAE,GAAAF,CAAA,CACA,EAEA,OAAA,OAAA,YAAA,KAAAM,EAAAN,EAAA,WAAA,IACAI,EAAA,OAAAJ,EAAA,QAGAI,MAEA,QAAAJ,CAEA,CAGA,SAAAK,GAAAE,EAAA,CACA,GAAA,CACA,OAAAC,GAAAD,CAAA,EAAAE,GAAAF,CAAA,EAAA,OAAA,UAAA,SAAA,KAAAA,CAAA,CACA,MAAA,CACA,MAAA,WACA,CACA,CAGA,SAAAL,GAAAQ,EAAA,CACA,GAAA,OAAAA,GAAA,UAAAA,IAAA,KAAA,CACA,IAAAC,EAAA,CAAA,EACA,QAAAC,KAAAF,EACA,OAAA,UAAA,eAAA,KAAAA,EAAAE,CAAA,IACAD,EAAAC,CAAA,EAAAF,EAAAE,CAAA,GAGA,OAAAD,MAEA,OAAA,CAAA,CAEA,CAOA,SAAAE,GAAAC,EAAAC,EAAA,GAAA,CACA,IAAAC,EAAA,OAAA,KAAAjB,GAAAe,CAAA,CAAA,EAGA,GAFAE,EAAA,KAAA,EAEA,CAAAA,EAAA,OACA,MAAA,uBAGA,GAAAA,EAAA,CAAA,EAAA,QAAAD,EACA,OAAAE,EAAAD,EAAA,CAAA,EAAAD,CAAA,EAGA,QAAAG,EAAAF,EAAA,OAAAE,EAAA,EAAAA,IAAA,CACA,IAAAC,EAAAH,EAAA,MAAA,EAAAE,CAAA,EAAA,KAAA,IAAA,EACA,GAAA,EAAAC,EAAA,OAAAJ,GAGA,OAAAG,IAAAF,EAAA,OACAG,EAEAF,EAAAE,EAAAJ,CAAA,EAGA,MAAA,EACA,CAQA,SAAAK,EAAAC,EAAA,CAOA,OAAAC,GAAAD,EAHA,IAAA,GAGA,CACA,CAEA,SAAAC,GAAAD,EAAAE,EAAA,CACA,GAAAC,EAAAH,CAAA,EAAA,CAEA,IAAAI,EAAAF,EAAA,IAAAF,CAAA,EACA,GAAAI,IAAA,OACA,OAAAA,EAGA,IAAAC,EAAA,CAAA,EAEAH,EAAA,IAAAF,EAAAK,CAAA,EAEA,QAAA5B,KAAA,OAAA,KAAAuB,CAAA,EACA,OAAAA,EAAAvB,CAAA,EAAA,MACA4B,EAAA5B,CAAA,EAAAwB,GAAAD,EAAAvB,CAAA,EAAAyB,CAAA,GAIA,OAAAG,EAGA,GAAA,MAAA,QAAAL,CAAA,EAAA,CAEA,IAAAI,EAAAF,EAAA,IAAAF,CAAA,EACA,GAAAI,IAAA,OACA,OAAAA,EAGA,IAAAC,EAAA,CAAA,EAEA,OAAAH,EAAA,IAAAF,EAAAK,CAAA,EAEAL,EAAA,QAAAM,GAAA,CACAD,EAAA,KAAAJ,GAAAK,EAAAJ,CAAA,CAAA,CACA,CAAA,EAEAG,EAGA,OAAAL,CACA,CCtOA,SAAAO,GAAAC,EAAAC,EAAA,GAAA,CAiBA,MAAA,EAfAA,GACAD,GAEA,CAAAA,EAAA,WAAA,GAAA,GAEA,CAAAA,EAAA,SAAA,KAAA,GAEA,CAAAA,EAAA,WAAA,GAAA,GAEA,CAAAA,EAAA,MAAA,kCAAA,IAMAA,IAAA,QAAA,CAAAA,EAAA,SAAA,eAAA,CACA,CAIA,SAAAE,GAAAC,EAAA,CACA,IAAAC,EAAA,eACAC,EAAA,gEAGA,OAAAC,GAAA,CACA,IAAAC,EAAAD,EAAA,MAAAD,CAAA,EAEA,GAAAE,EAAA,CACA,IAAAC,EACAC,EACAC,EACAC,EACAC,EAEA,GAAAL,EAAA,CAAA,EAAA,CACAG,EAAAH,EAAA,CAAA,EAEA,IAAAM,EAAAH,EAAA,YAAA,GAAA,EAKA,GAJAA,EAAAG,EAAA,CAAA,IAAA,KACAA,IAGAA,EAAA,EAAA,CACAL,EAAAE,EAAA,MAAA,EAAAG,CAAA,EACAJ,EAAAC,EAAA,MAAAG,EAAA,CAAA,EACA,IAAAC,EAAAN,EAAA,QAAA,SAAA,EACAM,EAAA,IACAJ,EAAAA,EAAA,MAAAI,EAAA,CAAA,EACAN,EAAAA,EAAA,MAAA,EAAAM,CAAA,GAGAH,EAAA,OAGAF,IACAE,EAAAH,EACAI,EAAAH,GAGAA,IAAA,gBACAG,EAAA,OACAF,EAAA,QAGAA,IAAA,SACAE,EAAAA,GAAA,cACAF,EAAAC,EAAA,GAAAA,KAAAC,IAAAA,GAGA,IAAAZ,EAAAO,EAAA,CAAA,GAAAA,EAAA,CAAA,EAAA,WAAA,SAAA,EAAAA,EAAA,CAAA,EAAA,MAAA,CAAA,EAAAA,EAAA,CAAA,EACAN,EAAAM,EAAA,CAAA,IAAA,SAEA,MAAA,CAAAP,GAAAO,EAAA,CAAA,GAAA,CAAAN,IACAD,EAAAO,EAAA,CAAA,GAGA,CACA,SAAAP,EACA,OAAAG,EAAAA,EAAAH,CAAA,EAAA,OACA,SAAAU,EACA,OAAA,SAAAH,EAAA,CAAA,EAAA,EAAA,GAAA,OACA,MAAA,SAAAA,EAAA,CAAA,EAAA,EAAA,GAAA,OACA,OAAAR,GAAAC,EAAAC,CAAA,CACA,EAGA,GAAAK,EAAA,MAAAF,CAAA,EACA,MAAA,CACA,SAAAE,CACA,CAIA,CACA,CCxHA,IAAAS,GAAA,GAEAC,GAAA,kBACAC,GAAA,kCASA,SAAAC,MAAAC,EAAA,CACA,IAAAC,EAAAD,EAAA,KAAA,CAAAE,EAAAC,IAAAD,EAAA,CAAA,EAAAC,EAAA,CAAA,CAAA,EAAA,IAAAC,GAAAA,EAAA,CAAA,CAAA,EAEA,MAAA,CAAAC,EAAAC,EAAA,IAAA,CACA,IAAAC,EAAA,CAAA,EACAC,EAAAH,EAAA,MAAA;CAAA,EAEA,QAAAI,EAAAH,EAAAG,EAAAD,EAAA,OAAAC,IAAA,CACA,IAAAC,EAAAF,EAAAC,CAAA,EAKA,GAAAC,EAAA,OAAA,KACA,SAKA,IAAAC,EAAAd,GAAA,KAAAa,CAAA,EAAAA,EAAA,QAAAb,GAAA,IAAA,EAAAa,EAIA,GAAA,CAAAC,EAAA,MAAA,YAAA,EAIA,SAAAC,KAAAX,EAAA,CACA,IAAAY,EAAAD,EAAAD,CAAA,EAEA,GAAAE,EAAA,CACAN,EAAA,KAAAM,CAAA,EACA,OAIA,GAAAN,EAAA,QAAAX,GACA,OAIA,OAAAkB,GAAAP,CAAA,CACA,CACA,CAQA,SAAAQ,GAAAC,EAAA,CACA,OAAA,MAAA,QAAAA,CAAA,EACAjB,GAAA,GAAAiB,CAAA,EAEAA,CACA,CAQA,SAAAF,GAAAT,EAAA,CACA,GAAA,CAAAA,EAAA,OACA,MAAA,CAAA,EAGA,IAAAY,EAAA,MAAA,KAAAZ,CAAA,EAGA,MAAA,gBAAA,KAAAY,EAAAA,EAAA,OAAA,CAAA,EAAA,UAAA,EAAA,GACAA,EAAA,IAAA,EAIAA,EAAA,QAAA,EAGAnB,GAAA,KAAAmB,EAAAA,EAAA,OAAA,CAAA,EAAA,UAAA,EAAA,IACAA,EAAA,IAAA,EAUAnB,GAAA,KAAAmB,EAAAA,EAAA,OAAA,CAAA,EAAA,UAAA,EAAA,GACAA,EAAA,IAAA,GAIAA,EAAA,MAAA,EAAArB,EAAA,EAAA,IAAAiB,IAAA,CACA,GAAAA,EACA,SAAAA,EAAA,UAAAI,EAAAA,EAAA,OAAA,CAAA,EAAA,SACA,SAAAJ,EAAA,UAAA,GACA,EAAA,CACA,CAEA,IAAAK,GAAA,cAKA,SAAAC,GAAAC,EAAA,CACA,GAAA,CACA,MAAA,CAAAA,GAAA,OAAAA,GAAA,WACAF,GAEAE,EAAA,MAAAF,EACA,MAAA,CAGA,OAAAA,EACA,CACA,CAQA,SAAAG,GAAAC,EAAA,CACA,MAAA,CAAA,GAAAC,GAAAD,CAAA,CAAA,CACA,CChJA,IAAAE,GAAAC,EAAA,EA0DA,SAAAC,IAAA,CACA,GAAA,EAAA,UAAAC,IACA,MAAA,GAGA,GAAA,CACA,WAAA,QACA,IAAA,QAAA,wBAAA,EACA,IAAA,SACA,EACA,MAAA,CACA,MAAA,EACA,CACA,CAKA,SAAAC,GAAAC,EAAA,CACA,OAAAA,GAAA,mDAAA,KAAAA,EAAA,SAAA,CAAA,CACA,CAQA,SAAAC,IAAA,CACA,GAAA,CAAAJ,GAAA,EACA,MAAA,GAKA,GAAAE,GAAAD,GAAA,KAAA,EACA,MAAA,GAKA,IAAAI,EAAA,GACAC,EAAAL,GAAA,SAEA,GAAAK,GAAA,OAAAA,EAAA,eAAA,WACA,GAAA,CACA,IAAAC,EAAAD,EAAA,cAAA,QAAA,EACAC,EAAA,OAAA,GACAD,EAAA,KAAA,YAAAC,CAAA,EACAA,EAAA,eAAAA,EAAA,cAAA,QAEAF,EAAAH,GAAAK,EAAA,cAAA,KAAA,GAEAD,EAAA,KAAA,YAAAC,CAAA,CACA,OAAAC,EAAA,EACA,OAAA,iBAAA,KAAA,mBACAC,EAAA,KAAA,kFAAAD,CAAA,CACA,CAGA,OAAAH,CACA,CCjGA,IAAAK,GAAAC,EAAA,EAQA,SAAAC,IAAA,CAMA,IAAAC,EAAAH,GAAA,OACAI,EAAAD,GAAAA,EAAA,KAAAA,EAAA,IAAA,QAEAE,EAAA,YAAAL,IAAA,CAAA,CAAAA,GAAA,QAAA,WAAA,CAAA,CAAAA,GAAA,QAAA,aAEA,MAAA,CAAAI,GAAAC,CACA,CC1BA,IAAAC,EAAAC,EAAA,EAEAC,GAAA,oBAwBAC,GAAA,CAAA,EACAC,GAAA,CAAA,EAGA,SAAAC,GAAAC,EAAA,CACA,GAAA,CAAAF,GAAAE,CAAA,EAMA,OAFAF,GAAAE,CAAA,EAAA,GAEAA,EAAA,CACA,IAAA,UACAC,GAAA,EACA,MACA,IAAA,MACAC,GAAA,EACA,MACA,IAAA,MACAC,GAAA,EACA,MACA,IAAA,QACAC,GAAA,EACA,MACA,IAAA,UACAC,GAAA,EACA,MACA,IAAA,QACAC,GAAA,EACA,MACA,IAAA,qBACAC,GAAA,EACA,MACA,SACA,OAAA,iBAAA,KAAA,mBAAAC,EAAA,KAAA,gCAAAR,CAAA,EACA,MACA,CACA,CAOA,SAAAS,GAAAT,EAAAU,EAAA,CACAb,GAAAG,CAAA,EAAAH,GAAAG,CAAA,GAAA,CAAA,EACAH,GAAAG,CAAA,EAAA,KAAAU,CAAA,EACAX,GAAAC,CAAA,CACA,CAaA,SAAAW,EAAAC,EAAAC,EAAA,CACA,GAAA,GAAAD,GAAA,CAAAE,GAAAF,CAAA,GAIA,QAAAG,KAAAD,GAAAF,CAAA,GAAA,CAAA,EACA,GAAA,CACAG,EAAAF,CAAA,CACA,OAAAG,EAAA,EACA,OAAA,iBAAA,KAAA,mBACAC,EAAA,MACA;QAAAL;QAAAM,GAAAH,CAAA;QACAC,CACA,CACA,CAEA,CAGA,SAAAG,IAAA,CACA,YAAAC,GAIAC,GAAA,QAAA,SAAAC,EAAA,CACAA,KAAAF,EAAA,SAIAG,EAAAH,EAAA,QAAAE,EAAA,SAAAE,EAAA,CACA,OAAAC,EAAAH,CAAA,EAAAE,EAEA,YAAAE,EAAA,CACAf,EAAA,UAAA,CAAA,KAAAe,EAAA,MAAAJ,CAAA,CAAA,EAEA,IAAAK,EAAAF,EAAAH,CAAA,EACAK,GAAAA,EAAA,MAAAP,EAAA,QAAAM,CAAA,CACA,CACA,CAAA,CACA,CAAA,CACA,CAGA,SAAAE,IAAA,CACAC,GAAA,GAIAN,EAAAH,EAAA,QAAA,SAAAU,EAAA,CACA,OAAA,YAAAJ,EAAA,CACA,GAAA,CAAA,OAAAK,EAAA,IAAAC,CAAA,EAAAC,GAAAP,CAAA,EAEAQ,EAAA,CACA,KAAAR,EACA,UAAA,CACA,OAAAK,EACA,IAAAC,CACA,EACA,eAAA,KAAA,IAAA,CACA,EAEA,OAAArB,EAAA,QAAA,CACA,GAAAuB,CACA,CAAA,EAGAJ,EAAA,MAAAV,EAAAM,CAAA,EAAA,KACAS,IACAxB,EAAA,QAAA,CACA,GAAAuB,EACA,aAAA,KAAA,IAAA,EACA,SAAAC,CACA,CAAA,EACAA,GAEAC,GAAA,CACA,MAAAzB,EAAA,QAAA,CACA,GAAAuB,EACA,aAAA,KAAA,IAAA,EACA,MAAAE,CACA,CAAA,EAIAA,CACA,CACA,CACA,CACA,CAAA,CACA,CAEA,SAAAC,GAAAC,EAAAC,EAAA,CACA,MAAA,CAAA,CAAAD,GAAA,OAAAA,GAAA,UAAA,CAAA,CAAAA,EAAAC,CAAA,CACA,CAIA,SAAAC,GAAAC,EAAA,CACA,OAAA,OAAAA,GAAA,SACAA,EAGAA,EAIAJ,GAAAI,EAAA,KAAA,EACAA,EAAA,IAGAA,EAAA,SACAA,EAAA,SAAA,EAGA,GAXA,EAYA,CAKA,SAAAR,GAAAS,EAAA,CACA,GAAAA,EAAA,SAAA,EACA,MAAA,CAAA,OAAA,MAAA,IAAA,EAAA,EAGA,GAAAA,EAAA,SAAA,EAAA,CACA,GAAA,CAAAV,EAAAW,CAAA,EAAAD,EAEA,MAAA,CACA,IAAAF,GAAAR,CAAA,EACA,OAAAK,GAAAM,EAAA,QAAA,EAAA,OAAAA,EAAA,MAAA,EAAA,YAAA,EAAA,KACA,EAGA,IAAAC,EAAAF,EAAA,CAAA,EACA,MAAA,CACA,IAAAF,GAAAI,CAAA,EACA,OAAAP,GAAAO,EAAA,QAAA,EAAA,OAAAA,EAAA,MAAA,EAAA,YAAA,EAAA,KACA,CACA,CAGA,SAAAC,IAAA,CAEA,GAAA,CAAAC,EAAA,eACA,OAGA,IAAAC,EAAA,eAAA,UAEAxB,EAAAwB,EAAA,OAAA,SAAAC,EAAA,CACA,OAAA,YAAAtB,EAAA,CACA,IAAAuB,EAAA,KAAA,IAAA,EAEAjB,EAAAN,EAAA,CAAA,EACAwB,EAAA,KAAAC,EAAA,EAAA,CAEA,OAAAC,EAAA1B,EAAA,CAAA,CAAA,EAAAA,EAAA,CAAA,EAAA,YAAA,EAAAA,EAAA,CAAA,EACA,IAAAA,EAAA,CAAA,EACA,gBAAA,CAAA,CACA,EAIA0B,EAAApB,CAAA,GAAAkB,EAAA,SAAA,QAAAlB,EAAA,MAAA,YAAA,IACA,KAAA,uBAAA,IAGA,IAAAqB,EAAA,IAAA,CAEA,IAAAH,EAAA,KAAAC,EAAA,EAEA,GAAAD,GAIA,KAAA,aAAA,EAAA,CACA,GAAA,CAGAA,EAAA,YAAA,KAAA,MACA,MAAA,CAEA,CAEAvC,EAAA,MAAA,CACA,KAAAe,EACA,aAAA,KAAA,IAAA,EACA,eAAAuB,EACA,IAAA,IACA,CAAA,EAEA,EAEA,MAAA,uBAAA,MAAA,OAAA,KAAA,oBAAA,WACA1B,EAAA,KAAA,qBAAA,SAAA+B,EAAA,CACA,OAAA,YAAAC,EAAA,CACA,OAAAF,EAAA,EACAC,EAAA,MAAA,KAAAC,CAAA,CACA,CACA,CAAA,EAEA,KAAA,iBAAA,mBAAAF,CAAA,EAMA9B,EAAA,KAAA,mBAAA,SAAA+B,EAAA,CACA,OAAA,YAAAE,EAAA,CACA,GAAA,CAAAC,EAAAC,CAAA,EAAAF,EAEAN,EAAA,KAAAC,EAAA,EAEA,OAAAD,IACAA,EAAA,gBAAAO,EAAA,YAAA,CAAA,EAAAC,GAGAJ,EAAA,MAAA,KAAAE,CAAA,CACA,CACA,CAAA,EAEAR,EAAA,MAAA,KAAAtB,CAAA,CACA,CACA,CAAA,EAEAH,EAAAwB,EAAA,OAAA,SAAAY,EAAA,CACA,OAAA,YAAAjC,EAAA,CACA,IAAAkC,EAAA,KAAAT,EAAA,EACA,OAAAS,GAAAlC,EAAA,CAAA,IAAA,SACAkC,EAAA,KAAAlC,EAAA,CAAA,GAGAf,EAAA,MAAA,CACA,KAAAe,EACA,eAAA,KAAA,IAAA,EACA,IAAA,IACA,CAAA,EAEAiC,EAAA,MAAA,KAAAjC,CAAA,CACA,CACA,CAAA,CACA,CAEA,IAAAmC,GAGA,SAAAC,IAAA,CACA,GAAA,CAAAC,GAAA,EACA,OAGA,IAAAC,EAAAlB,EAAA,WACAA,EAAA,WAAA,YAAApB,EAAA,CACA,IAAAuC,EAAAnB,EAAA,SAAA,KAEAoB,EAAAL,GAMA,GALAA,GAAAI,EACAtD,EAAA,UAAA,CACA,KAAAuD,EACA,GAAAD,CACA,CAAA,EACAD,EAIA,GAAA,CACA,OAAAA,EAAA,MAAA,KAAAtC,CAAA,CACA,MAAA,CAEA,CAEA,EAGA,SAAAyC,EAAAC,EAAA,CACA,OAAA,YAAA1C,EAAA,CACA,IAAAM,EAAAN,EAAA,OAAA,EAAAA,EAAA,CAAA,EAAA,OACA,GAAAM,EAAA,CAEA,IAAAkC,EAAAL,GACAI,EAAA,OAAAjC,CAAA,EAEA6B,GAAAI,EACAtD,EAAA,UAAA,CACA,KAAAuD,EACA,GAAAD,CACA,CAAA,EAEA,OAAAG,EAAA,MAAA,KAAA1C,CAAA,CACA,CACA,CAEAH,EAAAuB,EAAA,QAAA,YAAAqB,CAAA,EACA5C,EAAAuB,EAAA,QAAA,eAAAqB,CAAA,CACA,CAEA,IAAAE,GAAA,IACAC,GACAC,GAKA,SAAAC,GAAAC,EAAAC,EAAA,CAEA,GAAAD,EAAA,OAAAC,EAAA,KACA,MAAA,GAGA,GAAA,CAGA,GAAAD,EAAA,SAAAC,EAAA,OACA,MAAA,EAEA,MAAA,CAGA,CAKA,MAAA,EACA,CAMA,SAAAC,GAAAC,EAAA,CAEA,GAAAA,EAAA,OAAA,WACA,MAAA,GAGA,GAAA,CACA,IAAAC,EAAAD,EAAA,OAEA,GAAA,CAAAC,GAAA,CAAAA,EAAA,QACA,MAAA,GAKA,GAAAA,EAAA,UAAA,SAAAA,EAAA,UAAA,YAAAA,EAAA,kBACA,MAAA,EAEA,MAAA,CAGA,CAEA,MAAA,EACA,CASA,SAAAC,GAAA/D,EAAAgE,EAAA,GAAA,CACA,OAAAH,GAAA,CASA,GALA,CAAAA,GAAAA,EAAA,iBAKAD,GAAAC,CAAA,EACA,OAIAI,EAAAJ,EAAA,kBAAA,EAAA,EAEA,IAAAK,EAAAL,EAAA,OAAA,WAAA,QAAAA,EAAA,MAKAL,KAAA,QAAA,CAAAC,GAAAD,GAAAK,CAAA,KACA7D,EAAA,CACA,MAAA6D,EACA,KAAAK,EACA,OAAAF,CACA,CAAA,EACAR,GAAAK,GAIA,aAAAN,EAAA,EACAA,GAAAxB,EAAA,WAAA,IAAA,CACAyB,GAAA,MACA,EAAAF,EAAA,CACA,CACA,CAwBA,SAAAa,IAAA,CACA,GAAA,CAAApC,EAAA,SACA,OAMA,IAAAqC,EAAAxE,EAAA,KAAA,KAAA,KAAA,EACAyE,EAAAN,GAAAK,EAAA,EAAA,EACArC,EAAA,SAAA,iBAAA,QAAAsC,EAAA,EAAA,EACAtC,EAAA,SAAA,iBAAA,WAAAsC,EAAA,EAAA,EAOA,CAAA,cAAA,MAAA,EAAA,QAAAP,GAAA,CAEA,IAAAQ,EAAAvC,EAAA+B,CAAA,GAAA/B,EAAA+B,CAAA,EAAA,UAEA,CAAAQ,GAAA,CAAAA,EAAA,gBAAA,CAAAA,EAAA,eAAA,kBAAA,IAIA9D,EAAA8D,EAAA,mBAAA,SAAAC,EAAA,CACA,OAAA,SAEA1E,EACA2E,EACA5C,EACA,CACA,GAAA/B,IAAA,SAAAA,GAAA,WACA,GAAA,CACA,IAAA4E,EAAA,KACA1E,EAAA0E,EAAA,oCAAAA,EAAA,qCAAA,CAAA,EACAC,EAAA3E,EAAAF,CAAA,EAAAE,EAAAF,CAAA,GAAA,CAAA,SAAA,CAAA,EAEA,GAAA,CAAA6E,EAAA,QAAA,CACA,IAAA1E,EAAA+D,GAAAK,CAAA,EACAM,EAAA,QAAA1E,EACAuE,EAAA,KAAA,KAAA1E,EAAAG,EAAA4B,CAAA,EAGA8C,EAAA,UACA,MAAA,CAGA,CAGA,OAAAH,EAAA,KAAA,KAAA1E,EAAA2E,EAAA5C,CAAA,CACA,CACA,CAAA,EAEApB,EACA8D,EACA,sBACA,SAAAK,EAAA,CACA,OAAA,SAEA9E,EACA2E,EACA5C,EACA,CACA,GAAA/B,IAAA,SAAAA,GAAA,WACA,GAAA,CACA,IAAA4E,EAAA,KACA1E,EAAA0E,EAAA,qCAAA,CAAA,EACAC,EAAA3E,EAAAF,CAAA,EAEA6E,IACAA,EAAA,WAEAA,EAAA,UAAA,IACAC,EAAA,KAAA,KAAA9E,EAAA6E,EAAA,QAAA9C,CAAA,EACA8C,EAAA,QAAA,OACA,OAAA3E,EAAAF,CAAA,GAIA,OAAA,KAAAE,CAAA,EAAA,SAAA,GACA,OAAA0E,EAAA,oCAGA,MAAA,CAGA,CAGA,OAAAE,EAAA,KAAA,KAAA9E,EAAA2E,EAAA5C,CAAA,CACA,CACA,CACA,EACA,CAAA,CACA,CAEA,IAAAgD,GAAA,KAEA,SAAAC,IAAA,CACAD,GAAA7C,EAAA,QAEAA,EAAA,QAAA,SAAA+C,EAAA7D,EAAA8D,EAAAC,EAAA3D,EAAA,CASA,OARAzB,EAAA,QAAA,CACA,OAAAoF,EACA,MAAA3D,EACA,KAAA0D,EACA,IAAAD,EACA,IAAA7D,CACA,CAAA,EAEA2D,IAAA,CAAAA,GAAA,kBAEAA,GAAA,MAAA,KAAA,SAAA,EAGA,EACA,EAEA7C,EAAA,QAAA,wBAAA,EACA,CAEA,IAAAkD,GAAA,KAEA,SAAAC,IAAA,CACAD,GAAAlD,EAAA,qBAEAA,EAAA,qBAAA,SAAA9B,EAAA,CAGA,OAFAL,EAAA,qBAAAK,CAAA,EAEAgF,IAAA,CAAAA,GAAA,kBAEAA,GAAA,MAAA,KAAA,SAAA,EAGA,EACA,EAEAlD,EAAA,qBAAA,wBAAA,EACA,CCzoBA,SAAAoD,IAAA,CACA,OAAA,OAAA,0BAAA,KAAA,CAAA,CAAA,yBACA,CCdA,SAAAC,IAAA,CAGA,MACA,CAAAC,GAAA,GACA,OAAA,UAAA,SAAA,KAAA,OAAA,QAAA,IAAA,QAAA,CAAA,IAAA,kBAEA,CAQA,SAAAC,GAAAC,EAAAC,EAAA,CAEA,OAAAD,EAAA,QAAAC,CAAA,CACA,CCjBA,SAAAC,IAAA,CACA,IAAAC,EAAA,OAAA,SAAA,WACAC,EAAAD,EAAA,IAAA,QAAA,CAAA,EACA,SAAAE,EAAAC,EAAA,CACA,GAAAH,EACA,OAAAC,EAAA,IAAAE,CAAA,EACA,IAEAF,EAAA,IAAAE,CAAA,EACA,IAGA,QAAA,EAAA,EAAA,EAAAF,EAAA,OAAA,IAEA,GADAA,EAAA,CAAA,IACAE,EACA,MAAA,GAGA,OAAAF,EAAA,KAAAE,CAAA,EACA,EACA,CAEA,SAAAC,EAAAD,EAAA,CACA,GAAAH,EACAC,EAAA,OAAAE,CAAA,MAEA,SAAA,EAAA,EAAA,EAAAF,EAAA,OAAA,IACA,GAAAA,EAAA,CAAA,IAAAE,EAAA,CACAF,EAAA,OAAA,EAAA,CAAA,EACA,MAIA,CACA,MAAA,CAAAC,EAAAE,CAAA,CACA,CCzBA,SAAAC,GAAA,CACA,IAAAC,EAAAC,EACAC,EAAAF,EAAA,QAAAA,EAAA,SAEAG,EAAA,IAAA,KAAA,OAAA,EAAA,GACA,GAAA,CACA,GAAAD,GAAAA,EAAA,WACA,OAAAA,EAAA,WAAA,EAAA,QAAA,KAAA,EAAA,EAEAA,GAAAA,EAAA,kBACAC,EAAA,IAAAD,EAAA,gBAAA,IAAA,WAAA,CAAA,CAAA,EAAA,CAAA,EAEA,MAAA,CAGA,CAIA,OAAA,CAAA,GAAA,EAAA,IAAA,IAAA,IAAA,MAAA,QAAA,SAAAE,IAEAA,GAAAD,EAAA,EAAA,KAAAC,EAAA,GAAA,SAAA,EAAA,CACA,CACA,CAEA,SAAAC,GAAAC,EAAA,CACA,OAAAA,EAAA,WAAAA,EAAA,UAAA,OAAAA,EAAA,UAAA,OAAA,CAAA,EAAA,MACA,CA6BA,SAAAC,GAAAC,EAAAC,EAAAC,EAAA,CACA,IAAAC,EAAAH,EAAA,UAAAA,EAAA,WAAA,CAAA,EACAI,EAAAD,EAAA,OAAAA,EAAA,QAAA,CAAA,EACAE,EAAAD,EAAA,CAAA,EAAAA,EAAA,CAAA,GAAA,CAAA,EACAC,EAAA,QACAA,EAAA,MAAAJ,GAAA,IAEAI,EAAA,OACAA,EAAA,KAAAH,GAAA,QAEA,CASA,SAAAI,GAAAN,EAAAO,EAAA,CACA,IAAAF,EAAAG,GAAAR,CAAA,EACA,GAAA,CAAAK,EACA,OAGA,IAAAI,EAAA,CAAA,KAAA,UAAA,QAAA,EAAA,EACAC,EAAAL,EAAA,UAGA,GAFAA,EAAA,UAAA,CAAA,GAAAI,EAAA,GAAAC,EAAA,GAAAH,CAAA,EAEAA,GAAA,SAAAA,EAAA,CACA,IAAAI,EAAA,CAAA,GAAAD,GAAAA,EAAA,KAAA,GAAAH,EAAA,IAAA,EACAF,EAAA,UAAA,KAAAM,EAEA,CAmFA,SAAAC,GAAAC,EAAA,CAEA,GAAAA,GAAAA,EAAA,oBACA,MAAA,GAGA,GAAA,CAGAC,EAAAD,EAAA,sBAAA,EAAA,CACA,MAAA,CAEA,CAEA,MAAA,EACA,CAQA,SAAAE,GAAAC,EAAA,CACA,OAAA,MAAA,QAAAA,CAAA,EAAAA,EAAA,CAAAA,CAAA,CACA,CCzLA,SAAAC,EAAAC,EAAAC,EAAA,IAAAC,EAAA,IAAA,CACA,GAAA,CAEA,OAAAC,GAAA,GAAAH,EAAAC,EAAAC,CAAA,CACA,OAAAE,EAAA,CACA,MAAA,CAAA,MAAA,yBAAAA,IAAA,CACA,CACA,CAGA,SAAAC,EAEAC,EAEAL,EAAA,EAEAM,EAAA,IAAA,KACA,CACA,IAAAC,EAAAT,EAAAO,EAAAL,CAAA,EAEA,OAAAQ,GAAAD,CAAA,EAAAD,EACAF,EAAAC,EAAAL,EAAA,EAAAM,CAAA,EAGAC,CACA,CAWA,SAAAL,GACAO,EACAC,EACAV,EAAA,IACAC,EAAA,IACAU,EAAAC,GAAA,EACA,CACA,GAAA,CAAAC,EAAAC,CAAA,EAAAH,EAGA,GACAD,GAAA,MACA,CAAA,SAAA,UAAA,QAAA,EAAA,SAAA,OAAAA,CAAA,GAAA,CAAAK,GAAAL,CAAA,EAEA,OAAAA,EAGA,IAAAM,EAAAC,GAAAR,EAAAC,CAAA,EAIA,GAAA,CAAAM,EAAA,WAAA,UAAA,EACA,OAAAA,EAQA,GAAAN,EAAA,8BACA,OAAAA,EAMA,IAAAQ,EACA,OAAAR,EAAA,yCAAA,SACAA,EAAA,wCACAV,EAGA,GAAAkB,IAAA,EAEA,OAAAF,EAAA,QAAA,UAAA,EAAA,EAIA,GAAAH,EAAAH,CAAA,EACA,MAAA,eAIA,IAAAS,EAAAT,EACA,GAAAS,GAAA,OAAAA,EAAA,QAAA,WACA,GAAA,CACA,IAAAC,EAAAD,EAAA,OAAA,EAEA,OAAAjB,GAAA,GAAAkB,EAAAF,EAAA,EAAAjB,EAAAU,CAAA,CACA,MAAA,CAEA,CAMA,IAAAJ,EAAA,MAAA,QAAAG,CAAA,EAAA,CAAA,EAAA,CAAA,EACAW,EAAA,EAIAC,EAAAC,GAAAb,CAAA,EAEA,QAAAc,KAAAF,EAAA,CAEA,GAAA,CAAA,OAAA,UAAA,eAAA,KAAAA,EAAAE,CAAA,EACA,SAGA,GAAAH,GAAApB,EAAA,CACAM,EAAAiB,CAAA,EAAA,oBACA,MAIA,IAAAC,EAAAH,EAAAE,CAAA,EACAjB,EAAAiB,CAAA,EAAAtB,GAAAsB,EAAAC,EAAAP,EAAA,EAAAjB,EAAAU,CAAA,EAEAU,IAIA,OAAAP,EAAAJ,CAAA,EAGAH,CACA,CAiBA,SAAAU,GACAR,EAGAC,EACA,CACA,GAAA,CACA,GAAAD,IAAA,UAAAC,GAAA,OAAAA,GAAA,UAAAA,EAAA,QACA,MAAA,WAGA,GAAAD,IAAA,gBACA,MAAA,kBAMA,GAAA,OAAA,OAAA,KAAAC,IAAA,OACA,MAAA,WAIA,GAAA,OAAA,OAAA,KAAAA,IAAA,OACA,MAAA,WAIA,GAAA,OAAA,SAAA,KAAAA,IAAA,SACA,MAAA,aAGA,GAAAgB,GAAAhB,CAAA,EACA,MAAA,iBAIA,GAAAiB,GAAAjB,CAAA,EACA,MAAA,mBAGA,GAAA,OAAAA,GAAA,UAAAA,IAAAA,EACA,MAAA,QAGA,GAAA,OAAAA,GAAA,WACA,MAAA,cAAAkB,GAAAlB,CAAA,KAGA,GAAA,OAAAA,GAAA,SACA,MAAA,IAAA,OAAAA,CAAA,KAIA,GAAA,OAAAA,GAAA,SACA,MAAA,YAAA,OAAAA,CAAA,KAOA,IAAAmB,EAAAC,GAAApB,CAAA,EAGA,MAAA,qBAAA,KAAAmB,CAAA,EACA,iBAAAA,KAGA,WAAAA,IACA,OAAA1B,EAAA,CACA,MAAA,yBAAAA,IACA,CACA,CAGA,SAAA2B,GAAApB,EAAA,CACA,IAAAqB,EAAA,OAAA,eAAArB,CAAA,EAEA,OAAAqB,EAAAA,EAAA,YAAA,KAAA,gBACA,CAGA,SAAAC,GAAAtB,EAAA,CAEA,MAAA,CAAA,CAAA,UAAAA,CAAA,EAAA,MAAA,OAAA,EAAA,MACA,CAIA,SAAAF,GAAAE,EAAA,CACA,OAAAsB,GAAA,KAAA,UAAAtB,CAAA,CAAA,CACA,CCjOA,IAAAuB,GAAA,yEAEA,SAAAC,GAAAC,EAAA,CAGA,IAAAC,EAAAD,EAAA,OAAA,KAAA,cAAAA,EAAA,MAAA,KAAA,IAAAA,EACAE,EAAAJ,GAAA,KAAAG,CAAA,EACA,OAAAC,EAAAA,EAAA,MAAA,CAAA,EAAA,CAAA,CACA,CA6IA,SAAAC,GAAAC,EAAAC,EAAA,CACA,IAAAC,EAAAC,GAAAH,CAAA,EAAA,CAAA,EACA,OAAAC,GAAAC,EAAA,MAAAD,EAAA,OAAA,EAAA,IAAAA,IACAC,EAAAA,EAAA,MAAA,EAAAA,EAAA,OAAAD,EAAA,MAAA,GAEAC,CACA,CCzMA,IAAAE,GAAA,SAAAA,EAAA,CAEAA,EAAAA,EAAA,QAAA,CAAA,EAAA,UAEA,IAAAC,EAAA,EAAAD,EAAAA,EAAA,SAAAC,CAAA,EAAA,WAEA,IAAAC,EAAA,EAAAF,EAAAA,EAAA,SAAAE,CAAA,EAAA,UACA,GAAAF,IAAAA,EAAA,CAAA,EAAA,EAYA,SAAAG,EAAAC,EAAA,CACA,OAAA,IAAAC,EAAAC,GAAA,CACAA,EAAAF,CAAA,CACA,CAAA,CACA,CAQA,SAAAG,EAAAC,EAAA,CACA,OAAA,IAAAH,EAAA,CAAAI,EAAAC,IAAA,CACAA,EAAAF,CAAA,CACA,CAAA,CACA,CAMA,IAAAH,EAAA,KAAA,CAKA,YACAM,EACA,CAAAN,EAAA,UAAA,OAAA,KAAA,IAAA,EAAAA,EAAA,UAAA,QAAA,KAAA,IAAA,EAAAA,EAAA,UAAA,QAAA,KAAA,IAAA,EAAAA,EAAA,UAAA,QAAA,KAAA,IAAA,EACA,KAAA,OAAAL,EAAA,QACA,KAAA,UAAA,CAAA,EAEA,GAAA,CACAW,EAAA,KAAA,SAAA,KAAA,OAAA,CACA,OAAAC,EAAA,CACA,KAAA,QAAAA,CAAA,CACA,CACA,CAGA,KACAC,EACAC,EACA,CACA,OAAA,IAAAT,EAAA,CAAAC,EAAAI,IAAA,CACA,KAAA,UAAA,KAAA,CACA,GACAK,GAAA,CACA,GAAA,CAAAF,EAGAP,EAAAS,CAAA,MAEA,IAAA,CACAT,EAAAO,EAAAE,CAAA,CAAA,CACA,OAAAH,EAAA,CACAF,EAAAE,CAAA,CACA,CAEA,EACAJ,GAAA,CACA,GAAA,CAAAM,EACAJ,EAAAF,CAAA,MAEA,IAAA,CACAF,EAAAQ,EAAAN,CAAA,CAAA,CACA,OAAAI,EAAA,CACAF,EAAAE,CAAA,CACA,CAEA,CACA,CAAA,EACA,KAAA,iBAAA,CACA,CAAA,CACA,CAGA,MACAE,EACA,CACA,OAAA,KAAA,KAAAE,GAAAA,EAAAF,CAAA,CACA,CAGA,QAAAG,EAAA,CACA,OAAA,IAAAZ,EAAA,CAAAC,EAAAI,IAAA,CACA,IAAAM,EACAE,EAEA,OAAA,KAAA,KACAd,GAAA,CACAc,EAAA,GACAF,EAAAZ,EACAa,GACAA,EAAA,CAEA,EACAT,GAAA,CACAU,EAAA,GACAF,EAAAR,EACAS,GACAA,EAAA,CAEA,CACA,EAAA,KAAA,IAAA,CACA,GAAAC,EAAA,CACAR,EAAAM,CAAA,EACA,OAGAV,EAAAU,CAAA,CACA,CAAA,CACA,CAAA,CACA,CAGA,QAAA,CAAA,KAAA,SAAAZ,GAAA,CACA,KAAA,WAAAJ,EAAA,SAAAI,CAAA,CACA,CAAA,CAGA,SAAA,CAAA,KAAA,QAAAI,GAAA,CACA,KAAA,WAAAR,EAAA,SAAAQ,CAAA,CACA,CAAA,CAGA,SAAA,CAAA,KAAA,WAAA,CAAAW,EAAAf,IAAA,CACA,GAAA,KAAA,SAAAJ,EAAA,QAIA,IAAAoB,EAAAhB,CAAA,EAAA,CACAA,EAAA,KAAA,KAAA,SAAA,KAAA,OAAA,EACA,OAGA,KAAA,OAAAe,EACA,KAAA,OAAAf,EAEA,KAAA,iBAAA,EACA,CAAA,CAGA,SAAA,CAAA,KAAA,iBAAA,IAAA,CACA,GAAA,KAAA,SAAAJ,EAAA,QACA,OAGA,IAAAqB,EAAA,KAAA,UAAA,MAAA,EACA,KAAA,UAAA,CAAA,EAEAA,EAAA,QAAAC,GAAA,CACAA,EAAA,CAAA,IAIA,KAAA,SAAAtB,EAAA,UAEAsB,EAAA,CAAA,EAAA,KAAA,MAAA,EAGA,KAAA,SAAAtB,EAAA,UACAsB,EAAA,CAAA,EAAA,KAAA,MAAA,EAGAA,EAAA,CAAA,EAAA,GACA,CAAA,CACA,CAAA,CACA,ECpLA,SAAAC,GAAAC,EAAA,CACA,IAAAC,EAAA,CAAA,EAEA,SAAAC,GAAA,CACA,OAAAF,IAAA,QAAAC,EAAA,OAAAD,CACA,CAQA,SAAAG,EAAAC,EAAA,CACA,OAAAH,EAAA,OAAAA,EAAA,QAAAG,CAAA,EAAA,CAAA,EAAA,CAAA,CACA,CAYA,SAAAC,EAAAC,EAAA,CACA,GAAA,CAAAJ,EAAA,EACA,OAAAK,EAAA,IAAAC,EAAA,sDAAA,CAAA,EAIA,IAAAJ,EAAAE,EAAA,EACA,OAAAL,EAAA,QAAAG,CAAA,IAAA,IACAH,EAAA,KAAAG,CAAA,EAEAA,EACA,KAAA,IAAAD,EAAAC,CAAA,CAAA,EAIA,KAAA,KAAA,IACAD,EAAAC,CAAA,EAAA,KAAA,KAAA,IAAA,CAEA,CAAA,CACA,EACAA,CACA,CAWA,SAAAK,EAAAC,EAAA,CACA,OAAA,IAAAC,EAAA,CAAAC,EAAAC,IAAA,CACA,IAAAC,EAAAb,EAAA,OAEA,GAAA,CAAAa,EACA,OAAAF,EAAA,EAAA,EAIA,IAAAG,EAAA,WAAA,IAAA,CACAL,GAAAA,EAAA,GACAE,EAAA,EAAA,CAEA,EAAAF,CAAA,EAGAT,EAAA,QAAAe,GAAA,CACAC,EAAAD,CAAA,EAAA,KAAA,IAAA,CACA,EAAAF,IACA,aAAAC,CAAA,EACAH,EAAA,EAAA,EAEA,EAAAC,CAAA,CACA,CAAA,CACA,CAAA,CACA,CAEA,MAAA,CACA,EAAAZ,EACA,IAAAI,EACA,MAAAI,CACA,CACA,CCtGA,IAAAS,GAAAC,EAAA,EAgBAC,GAAA,CACA,WAAA,IAAA,KAAA,IAAA,EAAA,GACA,EAuBA,SAAAC,IAAA,CACA,GAAA,CAAA,YAAAC,CAAA,EAAAJ,GACA,GAAA,CAAAI,GAAA,CAAAA,EAAA,IACA,OAwBA,IAAAC,EAAA,KAAA,IAAA,EAAAD,EAAA,IAAA,EAEA,MAAA,CACA,IAAA,IAAAA,EAAA,IAAA,EACA,WAAAC,CACA,CACA,CAMA,SAAAC,IAAA,CACA,GAAA,CAEA,OADAC,GAAA,OAAA,YAAA,EACA,WACA,MAAA,CACA,MACA,CACA,CAKA,IAAAC,GAAAC,GAAA,EAAAH,GAAA,EAAAH,GAAA,EAEAO,GACAF,KAAA,OACAN,GACA,CACA,WAAA,KAAAM,GAAA,WAAAA,GAAA,IAAA,GAAA,GACA,EAKAG,EAAAT,GAAA,WAAA,KAAAA,EAAA,EAaAU,EAAAF,GAAA,WAAA,KAAAA,EAAA,EAkBA,IAAAG,GAMAC,IAAA,IAAA,CAKA,GAAA,CAAA,YAAAC,CAAA,EAAAC,GACA,GAAA,CAAAD,GAAA,CAAAA,EAAA,IAAA,CACAF,GAAA,OACA,OAGA,IAAAI,EAAA,KAAA,IACAC,EAAAH,EAAA,IAAA,EACAI,EAAA,KAAA,IAAA,EAGAC,EAAAL,EAAA,WACA,KAAA,IAAAA,EAAA,WAAAG,EAAAC,CAAA,EACAF,EACAI,EAAAD,EAAAH,EAQAK,EAAAP,EAAA,QAAAA,EAAA,OAAA,gBAGAQ,EAFA,OAAAD,GAAA,SAEA,KAAA,IAAAA,EAAAJ,EAAAC,CAAA,EAAAF,EACAO,EAAAD,EAAAN,EAEA,OAAAI,GAAAG,EAEAJ,GAAAG,GACAV,GAAA,aACAE,EAAA,aAEAF,GAAA,kBACAS,IAKAT,GAAA,UACAM,EACA,GAAA,EC3LA,IAAAM,GAAA,IAAA,OACA,2DAKA,EAyEA,SAAAC,GACAC,EAAAC,EAAA,EACAC,EAAAD,EAAA,EAAA,UAAA,EAAA,EACAE,EACA,CACA,IAAAC,EAAA,GACA,OAAAD,IAAA,SACAC,EAAAD,EAAA,KAAA,MAEA,GAAAH,KAAAE,IAAAE,GACA,CCrEA,SAAAC,EAAAC,EAAAC,EAAA,CAAA,EAAA,CACA,MAAA,CAAAD,EAAAC,CAAA,CACA,CAOA,SAAAC,GAAAC,EAAAC,EAAA,CACA,GAAA,CAAAJ,EAAAC,CAAA,EAAAE,EACA,MAAA,CAAAH,EAAA,CAAA,GAAAC,EAAAG,CAAA,CAAA,CACA,CAQA,SAAAC,GACAF,EACAG,EACA,CACA,IAAAC,EAAAJ,EAAA,CAAA,EAEA,QAAAK,KAAAD,EAAA,CACA,IAAAE,EAAAD,EAAA,CAAA,EAAA,KAGA,GAFAF,EAAAE,EAAAC,CAAA,EAGA,MAAA,GAIA,MAAA,EACA,CAYA,SAAAC,GAAAC,EAAAC,EAAA,CAEA,OADAA,GAAA,IAAA,aACA,OAAAD,CAAA,CACA,CAKA,SAAAE,GAAAC,EAAAF,EAAA,CACA,GAAA,CAAAG,EAAAC,CAAA,EAAAF,EAGAG,EAAA,KAAA,UAAAF,CAAA,EAEA,SAAAG,EAAAC,EAAA,CACA,OAAAF,GAAA,SACAA,EAAA,OAAAE,GAAA,SAAAF,EAAAE,EAAA,CAAAT,GAAAO,EAAAL,CAAA,EAAAO,CAAA,EAEAF,EAAA,KAAA,OAAAE,GAAA,SAAAT,GAAAS,EAAAP,CAAA,EAAAO,CAAA,CAEA,CAEA,QAAAC,KAAAJ,EAAA,CACA,GAAA,CAAAK,EAAAC,CAAA,EAAAF,EAIA,GAFAF,EAAA;EAAA,KAAA,UAAAG,CAAA;CAAA,EAEA,OAAAC,GAAA,UAAAA,aAAA,WACAJ,EAAAI,CAAA,MACA,CACA,IAAAC,EACA,GAAA,CACAA,EAAA,KAAA,UAAAD,CAAA,CACA,MAAA,CAIAC,EAAA,KAAA,UAAAC,EAAAF,CAAA,CAAA,CACA,CACAJ,EAAAK,CAAA,GAIA,OAAA,OAAAN,GAAA,SAAAA,EAAAQ,GAAAR,CAAA,CACA,CAEA,SAAAQ,GAAAC,EAAA,CACA,IAAAC,EAAAD,EAAA,OAAA,CAAAE,EAAAC,IAAAD,EAAAC,EAAA,OAAA,CAAA,EAEAC,EAAA,IAAA,WAAAH,CAAA,EACAI,EAAA,EACA,QAAAC,KAAAN,EACAI,EAAA,IAAAE,EAAAD,CAAA,EACAA,GAAAC,EAAA,OAGA,OAAAF,CACA,CAkDA,SAAAG,GACAC,EACAC,EACA,CACA,IAAAC,EAAA,OAAAF,EAAA,MAAA,SAAAG,GAAAH,EAAA,KAAAC,CAAA,EAAAD,EAAA,KAEA,MAAA,CACAI,EAAA,CACA,KAAA,aACA,OAAAF,EAAA,OACA,SAAAF,EAAA,SACA,aAAAA,EAAA,YACA,gBAAAA,EAAA,cACA,CAAA,EACAE,CACA,CACA,CAEA,IAAAG,GAAA,CACA,QAAA,UACA,SAAA,UACA,WAAA,aACA,YAAA,cACA,MAAA,QACA,cAAA,WACA,YAAA,UACA,QAAA,UACA,aAAA,SACA,iBAAA,SACA,SAAA,UAEA,OAAA,SACA,EAKA,SAAAC,GAAAC,EAAA,CACA,OAAAF,GAAAE,CAAA,CACA,CAGA,SAAAC,GAAAC,EAAA,CACA,GAAA,CAAAA,GAAA,CAAAA,EAAA,IACA,OAEA,GAAA,CAAA,KAAAC,EAAA,QAAAC,CAAA,EAAAF,EAAA,IACA,MAAA,CAAA,KAAAC,EAAA,QAAAC,CAAA,CACA,CAMA,SAAAC,GACAC,EACAC,EACAC,EACAC,EACA,CACA,IAAAC,EAAAJ,EAAA,uBAAAA,EAAA,sBAAA,uBACA,MAAA,CACA,SAAAA,EAAA,SACA,QAAA,IAAA,KAAA,EAAA,YAAA,EACA,GAAAC,GAAA,CAAA,IAAAA,CAAA,EACA,GAAA,CAAA,CAAAC,GAAAC,GAAA,CAAA,IAAAE,EAAAF,CAAA,CAAA,EACA,GAAAC,GAAA,CACA,MAAAb,EAAA,CAAA,GAAAa,CAAA,CAAA,CACA,CACA,CACA,CC7OA,SAAAE,GAAAC,EAAAC,EAAA,KAAA,IAAA,EAAA,CACA,IAAAC,EAAA,SAAA,GAAAF,IAAA,EAAA,EACA,GAAA,CAAA,MAAAE,CAAA,EACA,OAAAA,EAAA,IAGA,IAAAC,EAAA,KAAA,MAAA,GAAAH,GAAA,EACA,OAAA,MAAAG,CAAA,EAIA,IAHAA,EAAAF,CAIA,CASA,SAAAG,GAAAC,EAAAC,EAAA,CACA,OAAAD,EAAAC,CAAA,GAAAD,EAAA,KAAA,CACA,CAKA,SAAAE,GAAAF,EAAAC,EAAAL,EAAA,KAAA,IAAA,EAAA,CACA,OAAAG,GAAAC,EAAAC,CAAA,EAAAL,CACA,CAOA,SAAAO,GACAH,EACA,CAAA,WAAAI,EAAA,QAAAC,CAAA,EACAT,EAAA,KAAA,IAAA,EACA,CACA,IAAAU,EAAA,CACA,GAAAN,CACA,EAIAO,EAAAF,GAAAA,EAAA,sBAAA,EACAG,EAAAH,GAAAA,EAAA,aAAA,EAEA,GAAAE,EAaA,QAAAE,KAAAF,EAAA,KAAA,EAAA,MAAA,GAAA,EAAA,CACA,GAAA,CAAAG,EAAAC,CAAA,EAAAF,EAAA,MAAA,IAAA,CAAA,EACAZ,EAAA,SAAAa,EAAA,EAAA,EACAE,GAAA,MAAAf,CAAA,EAAA,GAAAA,GAAA,IACA,GAAA,CAAAc,EACAL,EAAA,IAAAV,EAAAgB,MAEA,SAAAX,KAAAU,EAAA,MAAA,GAAA,EACAL,EAAAL,CAAA,EAAAL,EAAAgB,OAIAJ,EACAF,EAAA,IAAAV,EAAAF,GAAAc,EAAAZ,CAAA,EACAQ,IAAA,MACAE,EAAA,IAAAV,EAAA,GAAA,KAGA,OAAAU,CACA,CC5EA,SAAAO,GAAAC,EAAAC,EAAA,CACA,OAAAD,EAAAC,EAAA,OAAA,GAAA,CAAA,CACA,CAKA,SAAAC,GAAAF,EAAAC,EAAA,CACA,IAAAE,EAAA,CACA,KAAAF,EAAA,MAAAA,EAAA,YAAA,KACA,MAAAA,EAAA,OACA,EAEAG,EAAAL,GAAAC,EAAAC,CAAA,EACA,OAAAG,EAAA,SACAD,EAAA,WAAA,CAAA,OAAAC,CAAA,GAGAD,CACA,CAEA,SAAAE,GAAAF,EAAA,CACA,GAAA,SAAAA,GAAA,OAAAA,EAAA,MAAA,SAAA,CACA,IAAAG,EAAA,IAAAH,EAAA,8BAEA,MAAA,YAAAA,GAAA,OAAAA,EAAA,SAAA,WACAG,GAAA,kBAAAH,EAAA,YAGAG,MACA,OAAA,YAAAH,GAAA,OAAAA,EAAA,SAAA,SACAA,EAAA,QAIA,2CAAAI,GACAJ,CACA,GAEA,CAMA,SAAAK,GACAC,EACAT,EACAG,EACAO,EACA,CACA,IAAAC,EAAAR,EAGAS,EADAF,GAAAA,EAAA,MAAAA,EAAA,KAAA,WACA,CACA,QAAA,GACA,KAAA,SACA,EAEA,GAAA,CAAAG,EAAAV,CAAA,EAAA,CACA,GAAAW,EAAAX,CAAA,EAAA,CACA,IAAAY,EAAAN,EAAA,EACAO,EAAAD,EAAA,UAAA,EACAE,EAAAD,GAAAA,EAAA,WAAA,EAAA,eACAD,EAAA,eAAAG,GAAA,CACAA,EAAA,SAAA,iBAAAC,EAAAhB,EAAAc,CAAA,CAAA,CACA,CAAA,EAEA,IAAAX,EAAAD,GAAAF,CAAA,EACAQ,EAAAD,GAAAA,EAAA,oBAAA,IAAA,MAAAJ,CAAA,EACAK,EAAA,QAAAL,OAIAK,EAAAD,GAAAA,EAAA,oBAAA,IAAA,MAAAP,CAAA,EACAQ,EAAA,QAAAR,EAEAS,EAAA,UAAA,GAGA,IAAAQ,EAAA,CACA,UAAA,CACA,OAAA,CAAAlB,GAAAF,EAAAW,CAAA,CAAA,CACA,CACA,EAEA,OAAAU,GAAAD,EAAA,OAAA,MAAA,EACAE,GAAAF,EAAAR,CAAA,EAEA,CACA,GAAAQ,EACA,SAAAV,GAAAA,EAAA,QACA,CACA,CAMA,SAAAa,GACAvB,EACAM,EAEAkB,EAAA,OACAd,EACAe,EACA,CACA,IAAAL,EAAA,CACA,SAAAV,GAAAA,EAAA,SACA,MAAAc,EACA,QAAAlB,CACA,EAEA,GAAAmB,GAAAf,GAAAA,EAAA,mBAAA,CACA,IAAAN,EAAAL,GAAAC,EAAAU,EAAA,kBAAA,EACAN,EAAA,SACAgB,EAAA,UAAA,CACA,OAAA,CACA,CACA,MAAAd,EACA,WAAA,CAAA,OAAAF,CAAA,CACA,CACA,CACA,GAIA,OAAAgB,CACA,CCpJA,IAAAM,EAAA,aCMA,SAAAC,IAAA,CACA,OAAAC,GAAA,wBAAA,IAAA,CAAA,CAAA,CACA,CAMA,SAAAC,GAAAC,EAAA,CACAH,GAAA,EAAA,KAAAG,CAAA,CACA,CAKA,SAAAC,EACAC,EACAC,EACAC,EACAC,EAAA,EACA,CACA,OAAA,IAAAC,EAAA,CAAAC,EAAAC,IAAA,CACA,IAAAC,EAAAP,EAAAG,CAAA,EACA,GAAAF,IAAA,MAAA,OAAAM,GAAA,WACAF,EAAAJ,CAAA,MACA,CACA,IAAAO,EAAAD,EAAA,CAAA,GAAAN,CAAA,EAAAC,CAAA,GAEA,OAAA,iBAAA,KAAA,mBACAK,EAAA,IACAC,IAAA,MACAC,EAAA,IAAA,oBAAAF,EAAA,mBAAA,EAEAG,EAAAF,CAAA,EACAA,EACA,KAAAG,GAAAZ,EAAAC,EAAAW,EAAAT,EAAAC,EAAA,CAAA,EAAA,KAAAE,CAAA,CAAA,EACA,KAAA,KAAAC,CAAA,EAEAP,EAAAC,EAAAQ,EAAAN,EAAAC,EAAA,CAAA,EACA,KAAAE,CAAA,EACA,KAAA,KAAAC,CAAA,EAGA,CAAA,CACA,CCvCA,SAAAM,GAAAC,EAAA,CAEA,IAAAC,EAAAC,EAAA,EAEAC,EAAA,CACA,IAAAC,EAAA,EACA,KAAA,GACA,UAAAH,EACA,QAAAA,EACA,SAAA,EACA,OAAA,KACA,OAAA,EACA,eAAA,GACA,OAAA,IAAAI,GAAAF,CAAA,CACA,EAEA,OAAAH,GACAM,EAAAH,EAAAH,CAAA,EAGAG,CACA,CAcA,SAAAG,EAAAH,EAAAH,EAAA,CAAA,EAAA,CAiCA,GAhCAA,EAAA,OACA,CAAAG,EAAA,WAAAH,EAAA,KAAA,aACAG,EAAA,UAAAH,EAAA,KAAA,YAGA,CAAAG,EAAA,KAAA,CAAAH,EAAA,MACAG,EAAA,IAAAH,EAAA,KAAA,IAAAA,EAAA,KAAA,OAAAA,EAAA,KAAA,WAIAG,EAAA,UAAAH,EAAA,WAAAE,EAAA,EAEAF,EAAA,qBACAG,EAAA,mBAAAH,EAAA,oBAGAA,EAAA,iBACAG,EAAA,eAAAH,EAAA,gBAEAA,EAAA,MAEAG,EAAA,IAAAH,EAAA,IAAA,SAAA,GAAAA,EAAA,IAAAI,EAAA,GAEAJ,EAAA,OAAA,SACAG,EAAA,KAAAH,EAAA,MAEA,CAAAG,EAAA,KAAAH,EAAA,MACAG,EAAA,IAAA,GAAAH,EAAA,OAEA,OAAAA,EAAA,SAAA,WACAG,EAAA,QAAAH,EAAA,SAEAG,EAAA,eACAA,EAAA,SAAA,eACA,OAAAH,EAAA,UAAA,SACAG,EAAA,SAAAH,EAAA,aACA,CACA,IAAAO,EAAAJ,EAAA,UAAAA,EAAA,QACAA,EAAA,SAAAI,GAAA,EAAAA,EAAA,EAEAP,EAAA,UACAG,EAAA,QAAAH,EAAA,SAEAA,EAAA,cACAG,EAAA,YAAAH,EAAA,aAEA,CAAAG,EAAA,WAAAH,EAAA,YACAG,EAAA,UAAAH,EAAA,WAEA,CAAAG,EAAA,WAAAH,EAAA,YACAG,EAAA,UAAAH,EAAA,WAEA,OAAAA,EAAA,QAAA,WACAG,EAAA,OAAAH,EAAA,QAEAA,EAAA,SACAG,EAAA,OAAAH,EAAA,OAEA,CAaA,SAAAQ,GAAAL,EAAAM,EAAA,CACA,IAAAT,EAAA,CAAA,EACAS,EACAT,EAAA,CAAA,OAAAS,CAAA,EACAN,EAAA,SAAA,OACAH,EAAA,CAAA,OAAA,QAAA,GAGAM,EAAAH,EAAAH,CAAA,CACA,CAWA,SAAAK,GAAAF,EAAA,CACA,OAAAO,EAAA,CACA,IAAA,GAAAP,EAAA,MACA,KAAAA,EAAA,KAEA,QAAA,IAAA,KAAAA,EAAA,QAAA,GAAA,EAAA,YAAA,EACA,UAAA,IAAA,KAAAA,EAAA,UAAA,GAAA,EAAA,YAAA,EACA,OAAAA,EAAA,OACA,OAAAA,EAAA,OACA,IAAA,OAAAA,EAAA,KAAA,UAAA,OAAAA,EAAA,KAAA,SAAA,GAAAA,EAAA,MAAA,OACA,SAAAA,EAAA,SACA,mBAAAA,EAAA,mBACA,MAAA,CACA,QAAAA,EAAA,QACA,YAAAA,EAAA,YACA,WAAAA,EAAA,UACA,WAAAA,EAAA,SACA,CACA,CAAA,CACA,CC7HA,IAAAQ,GAAA,IAMAC,EAAA,KAAA,CA0DA,aAAA,CACA,KAAA,oBAAA,GACA,KAAA,gBAAA,CAAA,EACA,KAAA,iBAAA,CAAA,EACA,KAAA,aAAA,CAAA,EACA,KAAA,aAAA,CAAA,EACA,KAAA,MAAA,CAAA,EACA,KAAA,MAAA,CAAA,EACA,KAAA,OAAA,CAAA,EACA,KAAA,UAAA,CAAA,EACA,KAAA,uBAAA,CAAA,EACA,KAAA,oBAAAC,GAAA,CACA,CAMA,OAAA,MAAAC,EAAA,CACA,IAAAC,EAAA,IAAAH,EACA,OAAAE,IACAC,EAAA,aAAA,CAAA,GAAAD,EAAA,YAAA,EACAC,EAAA,MAAA,CAAA,GAAAD,EAAA,KAAA,EACAC,EAAA,OAAA,CAAA,GAAAD,EAAA,MAAA,EACAC,EAAA,UAAA,CAAA,GAAAD,EAAA,SAAA,EACAC,EAAA,MAAAD,EAAA,MACAC,EAAA,OAAAD,EAAA,OACAC,EAAA,MAAAD,EAAA,MACAC,EAAA,SAAAD,EAAA,SACAC,EAAA,iBAAAD,EAAA,iBACAC,EAAA,aAAAD,EAAA,aACAC,EAAA,iBAAA,CAAA,GAAAD,EAAA,gBAAA,EACAC,EAAA,gBAAAD,EAAA,gBACAC,EAAA,aAAA,CAAA,GAAAD,EAAA,YAAA,EACAC,EAAA,uBAAA,CAAA,GAAAD,EAAA,sBAAA,EACAC,EAAA,oBAAA,CAAA,GAAAD,EAAA,mBAAA,GAEAC,CACA,CAMA,iBAAAC,EAAA,CACA,KAAA,gBAAA,KAAAA,CAAA,CACA,CAKA,kBAAAA,EAAA,CACA,YAAA,iBAAA,KAAAA,CAAA,EACA,IACA,CAKA,QAAAC,EAAA,CACA,YAAA,MAAAA,GAAA,CAAA,EACA,KAAA,UACAC,EAAA,KAAA,SAAA,CAAA,KAAAD,CAAA,CAAA,EAEA,KAAA,sBAAA,EACA,IACA,CAKA,SAAA,CACA,OAAA,KAAA,KACA,CAKA,mBAAA,CACA,OAAA,KAAA,eACA,CAKA,kBAAAE,EAAA,CACA,YAAA,gBAAAA,EACA,IACA,CAKA,QAAAC,EAAA,CACA,YAAA,MAAA,CACA,GAAA,KAAA,MACA,GAAAA,CACA,EACA,KAAA,sBAAA,EACA,IACA,CAKA,OAAAC,EAAAC,EAAA,CACA,YAAA,MAAA,CAAA,GAAA,KAAA,MAAA,CAAAD,CAAA,EAAAC,CAAA,EACA,KAAA,sBAAA,EACA,IACA,CAKA,UAAAC,EAAA,CACA,YAAA,OAAA,CACA,GAAA,KAAA,OACA,GAAAA,CACA,EACA,KAAA,sBAAA,EACA,IACA,CAKA,SAAAF,EAAAG,EAAA,CACA,YAAA,OAAA,CAAA,GAAA,KAAA,OAAA,CAAAH,CAAA,EAAAG,CAAA,EACA,KAAA,sBAAA,EACA,IACA,CAKA,eAAAC,EAAA,CACA,YAAA,aAAAA,EACA,KAAA,sBAAA,EACA,IACA,CAKA,SAEAC,EACA,CACA,YAAA,OAAAA,EACA,KAAA,sBAAA,EACA,IACA,CAKA,mBAAAC,EAAA,CACA,YAAA,iBAAAA,EACA,KAAA,sBAAA,EACA,IACA,CAKA,WAAAN,EAAAO,EAAA,CACA,OAAAA,IAAA,KAEA,OAAA,KAAA,UAAAP,CAAA,EAEA,KAAA,UAAAA,CAAA,EAAAO,EAGA,KAAA,sBAAA,EACA,IACA,CAKA,QAAAC,EAAA,CACA,YAAA,MAAAA,EACA,KAAA,sBAAA,EACA,IACA,CAKA,SAAA,CACA,OAAA,KAAA,KACA,CAKA,gBAAA,CAGA,IAAAA,EAAA,KAAA,QAAA,EACA,OAAAA,GAAAA,EAAA,WACA,CAKA,WAAAC,EAAA,CACA,OAAAA,EAGA,KAAA,SAAAA,EAFA,OAAA,KAAA,SAIA,KAAA,sBAAA,EACA,IACA,CAKA,YAAA,CACA,OAAA,KAAA,QACA,CAKA,OAAAC,EAAA,CACA,GAAA,CAAAA,EACA,OAAA,KAGA,GAAA,OAAAA,GAAA,WAAA,CACA,IAAAC,EAAAD,EAAA,IAAA,EACA,OAAAC,aAAApB,EAAAoB,EAAA,KAGA,OAAAD,aAAAnB,GACA,KAAA,MAAA,CAAA,GAAA,KAAA,MAAA,GAAAmB,EAAA,KAAA,EACA,KAAA,OAAA,CAAA,GAAA,KAAA,OAAA,GAAAA,EAAA,MAAA,EACA,KAAA,UAAA,CAAA,GAAA,KAAA,UAAA,GAAAA,EAAA,SAAA,EACAA,EAAA,OAAA,OAAA,KAAAA,EAAA,KAAA,EAAA,SACA,KAAA,MAAAA,EAAA,OAEAA,EAAA,SACA,KAAA,OAAAA,EAAA,QAEAA,EAAA,eACA,KAAA,aAAAA,EAAA,cAEAA,EAAA,kBACA,KAAA,gBAAAA,EAAA,iBAEAA,EAAA,sBACA,KAAA,oBAAAA,EAAA,sBAEAE,EAAAF,CAAA,IAEAA,EAAAA,EACA,KAAA,MAAA,CAAA,GAAA,KAAA,MAAA,GAAAA,EAAA,IAAA,EACA,KAAA,OAAA,CAAA,GAAA,KAAA,OAAA,GAAAA,EAAA,KAAA,EACA,KAAA,UAAA,CAAA,GAAA,KAAA,UAAA,GAAAA,EAAA,QAAA,EACAA,EAAA,OACA,KAAA,MAAAA,EAAA,MAEAA,EAAA,QACA,KAAA,OAAAA,EAAA,OAEAA,EAAA,cACA,KAAA,aAAAA,EAAA,aAEAA,EAAA,iBACA,KAAA,gBAAAA,EAAA,gBAEAA,EAAA,qBACA,KAAA,oBAAAA,EAAA,qBAIA,IACA,CAKA,OAAA,CACA,YAAA,aAAA,CAAA,EACA,KAAA,MAAA,CAAA,EACA,KAAA,OAAA,CAAA,EACA,KAAA,MAAA,CAAA,EACA,KAAA,UAAA,CAAA,EACA,KAAA,OAAA,OACA,KAAA,iBAAA,OACA,KAAA,aAAA,OACA,KAAA,gBAAA,OACA,KAAA,MAAA,OACA,KAAA,SAAA,OACA,KAAA,sBAAA,EACA,KAAA,aAAA,CAAA,EACA,KAAA,oBAAAlB,GAAA,EACA,IACA,CAKA,cAAAqB,EAAAC,EAAA,CACA,IAAAC,EAAA,OAAAD,GAAA,SAAAA,EAAAxB,GAGA,GAAAyB,GAAA,EACA,OAAA,KAGA,IAAAC,EAAA,CACA,UAAAC,EAAA,EACA,GAAAJ,CACA,EAEAK,EAAA,KAAA,aACA,OAAAA,EAAA,KAAAF,CAAA,EACA,KAAA,aAAAE,EAAA,OAAAH,EAAAG,EAAA,MAAA,CAAAH,CAAA,EAAAG,EAEA,KAAA,sBAAA,EAEA,IACA,CAKA,mBAAA,CACA,OAAA,KAAA,aAAA,KAAA,aAAA,OAAA,CAAA,CACA,CAKA,kBAAA,CACA,YAAA,aAAA,CAAA,EACA,KAAA,sBAAA,EACA,IACA,CAKA,cAAAC,EAAA,CACA,YAAA,aAAA,KAAAA,CAAA,EACA,IACA,CAKA,gBAAA,CACA,OAAA,KAAA,YACA,CAKA,kBAAA,CACA,YAAA,aAAA,CAAA,EACA,IACA,CASA,aACAC,EACAC,EAAA,CAAA,EACAC,EACA,CAuBA,GAtBA,KAAA,QAAA,OAAA,KAAA,KAAA,MAAA,EAAA,SACAF,EAAA,MAAA,CAAA,GAAA,KAAA,OAAA,GAAAA,EAAA,KAAA,GAEA,KAAA,OAAA,OAAA,KAAA,KAAA,KAAA,EAAA,SACAA,EAAA,KAAA,CAAA,GAAA,KAAA,MAAA,GAAAA,EAAA,IAAA,GAEA,KAAA,OAAA,OAAA,KAAA,KAAA,KAAA,EAAA,SACAA,EAAA,KAAA,CAAA,GAAA,KAAA,MAAA,GAAAA,EAAA,IAAA,GAEA,KAAA,WAAA,OAAA,KAAA,KAAA,SAAA,EAAA,SACAA,EAAA,SAAA,CAAA,GAAA,KAAA,UAAA,GAAAA,EAAA,QAAA,GAEA,KAAA,SACAA,EAAA,MAAA,KAAA,QAEA,KAAA,mBACAA,EAAA,YAAA,KAAA,kBAMA,KAAA,MAAA,CACAA,EAAA,SAAA,CAAA,MAAA,KAAA,MAAA,gBAAA,EAAA,GAAAA,EAAA,QAAA,EACA,IAAAG,EAAA,KAAA,MAAA,YACA,GAAAA,EAAA,CACAH,EAAA,sBAAA,CACA,uBAAAG,EAAA,0BAAA,EACA,GAAAH,EAAA,qBACA,EACA,IAAAI,EAAAD,EAAA,KACAC,IACAJ,EAAA,KAAA,CAAA,YAAAI,EAAA,GAAAJ,EAAA,IAAA,IAKA,KAAA,kBAAAA,CAAA,EAEA,IAAAK,EAAA,KAAA,gBAAA,EACAP,EAAA,CAAA,GAAAE,EAAA,aAAA,CAAA,EAAA,GAAAK,CAAA,EACA,OAAAL,EAAA,YAAAF,EAAA,OAAA,EAAAA,EAAA,OAEAE,EAAA,sBAAA,CACA,GAAAA,EAAA,sBACA,GAAA,KAAA,uBACA,mBAAA,KAAA,mBACA,EAGAM,EACA,CAAA,GAAAJ,GAAA,CAAA,EAAA,GAAAK,GAAA,EAAA,GAAA,KAAA,gBAAA,EACAP,EACAC,CACA,CACA,CAKA,yBAAAO,EAAA,CACA,YAAA,uBAAA,CAAA,GAAA,KAAA,uBAAA,GAAAA,CAAA,EAEA,IACA,CAKA,sBAAArB,EAAA,CACA,YAAA,oBAAAA,EACA,IACA,CAKA,uBAAA,CACA,OAAA,KAAA,mBACA,CAKA,iBAAA,CACA,OAAA,KAAA,YACA,CAKA,uBAAA,CAIA,KAAA,sBACA,KAAA,oBAAA,GACA,KAAA,gBAAA,QAAAZ,GAAA,CACAA,EAAA,IAAA,CACA,CAAA,EACA,KAAA,oBAAA,GAEA,CAMA,kBAAAyB,EAAA,CAEAA,EAAA,YAAAA,EAAA,YAAAS,GAAAT,EAAA,WAAA,EAAA,CAAA,EAGA,KAAA,eACAA,EAAA,YAAAA,EAAA,YAAA,OAAA,KAAA,YAAA,GAIAA,EAAA,aAAA,CAAAA,EAAA,YAAA,QACA,OAAAA,EAAA,WAEA,CACA,EAEA,SAAA5B,IAAA,CACA,MAAA,CACA,QAAAsC,EAAA,EACA,OAAAA,EAAA,EAAA,UAAA,EAAA,CACA,CACA,CCvjBA,IAAAC,GAAA,EAMAC,GAAA,IAuDAC,EAAA,KAAA,CAeA,YAAAC,EAAAC,EAAA,IAAAC,EAAAC,EAAAN,GAAA,CAAA,KAAA,SAAAM,EACA,KAAA,OAAA,CAAA,CAAA,MAAAF,CAAA,CAAA,EACAD,GACA,KAAA,WAAAA,CAAA,CAEA,CAKA,YAAAI,EAAA,CACA,OAAA,KAAA,SAAAA,CACA,CAKA,WAAAJ,EAAA,CACA,IAAAK,EAAA,KAAA,YAAA,EACAA,EAAA,OAAAL,EACAA,GAAAA,EAAA,mBACAA,EAAA,kBAAA,CAEA,CAKA,WAAA,CAEA,IAAAC,EAAAC,EAAA,MAAA,KAAA,SAAA,CAAA,EACA,YAAA,SAAA,EAAA,KAAA,CACA,OAAA,KAAA,UAAA,EACA,MAAAD,CACA,CAAA,EACAA,CACA,CAKA,UAAA,CACA,OAAA,KAAA,SAAA,EAAA,QAAA,EAAA,GACA,CAAA,CAAA,KAAA,SAAA,EAAA,IAAA,CACA,CAKA,UAAAK,EAAA,CACA,IAAAL,EAAA,KAAA,UAAA,EACA,GAAA,CACAK,EAAAL,CAAA,CACA,QAAA,CACA,KAAA,SAAA,CACA,CACA,CAKA,WAAA,CACA,OAAA,KAAA,YAAA,EAAA,MACA,CAGA,UAAA,CACA,OAAA,KAAA,YAAA,EAAA,KACA,CAGA,UAAA,CACA,OAAA,KAAA,MACA,CAGA,aAAA,CACA,OAAA,KAAA,OAAA,KAAA,OAAA,OAAA,CAAA,CACA,CAKA,iBAAAM,EAAAC,EAAA,CACA,IAAAC,EAAA,KAAA,aAAAD,GAAAA,EAAA,SAAAA,EAAA,SAAAE,EAAA,EACAC,EAAA,IAAA,MAAA,2BAAA,EACA,YAAA,YAAA,CAAAX,EAAAC,IAAA,CACAD,EAAA,iBACAO,EACA,CACA,kBAAAA,EACA,mBAAAI,EACA,GAAAH,EACA,SAAAC,CACA,EACAR,CACA,CACA,CAAA,EACAQ,CACA,CAKA,eACAG,EAEAC,EACAL,EACA,CACA,IAAAC,EAAA,KAAA,aAAAD,GAAAA,EAAA,SAAAA,EAAA,SAAAE,EAAA,EACAC,EAAA,IAAA,MAAAC,CAAA,EACA,YAAA,YAAA,CAAAZ,EAAAC,IAAA,CACAD,EAAA,eACAY,EACAC,EACA,CACA,kBAAAD,EACA,mBAAAD,EACA,GAAAH,EACA,SAAAC,CACA,EACAR,CACA,CACA,CAAA,EACAQ,CACA,CAKA,aAAAK,EAAAN,EAAA,CACA,IAAAC,EAAAD,GAAAA,EAAA,SAAAA,EAAA,SAAAE,EAAA,EACA,OAAAI,EAAA,OACA,KAAA,aAAAL,GAGA,KAAA,YAAA,CAAAT,EAAAC,IAAA,CACAD,EAAA,aAAAc,EAAA,CAAA,GAAAN,EAAA,SAAAC,CAAA,EAAAR,CAAA,CACA,CAAA,EACAQ,CACA,CAKA,aAAA,CACA,OAAA,KAAA,YACA,CAKA,cAAAM,EAAAP,EAAA,CACA,GAAA,CAAA,MAAAP,EAAA,OAAAD,CAAA,EAAA,KAAA,YAAA,EAEA,GAAA,CAAAA,EAAA,OAEA,GAAA,CAAA,iBAAAgB,EAAA,KAAA,eAAAC,EAAAnB,EAAA,EACAE,EAAA,YAAAA,EAAA,WAAA,GAAA,CAAA,EAEA,GAAAiB,GAAA,EAAA,OAGA,IAAAC,EAAA,CAAA,UADAC,EAAA,EACA,GAAAJ,CAAA,EACAK,EAAAJ,EACAK,GAAA,IAAAL,EAAAE,EAAAV,CAAA,CAAA,EACAU,EAEAE,IAAA,OAEApB,EAAA,MACAA,EAAA,KAAA,sBAAAoB,EAAAZ,CAAA,EAGAP,EAAA,cAAAmB,EAAAH,CAAA,EACA,CAKA,QAAAK,EAAA,CACA,KAAA,SAAA,EAAA,QAAAA,CAAA,CACA,CAKA,QAAAC,EAAA,CACA,KAAA,SAAA,EAAA,QAAAA,CAAA,CACA,CAKA,UAAAC,EAAA,CACA,KAAA,SAAA,EAAA,UAAAA,CAAA,CACA,CAKA,OAAAC,EAAAC,EAAA,CACA,KAAA,SAAA,EAAA,OAAAD,EAAAC,CAAA,CACA,CAKA,SAAAD,EAAAE,EAAA,CACA,KAAA,SAAA,EAAA,SAAAF,EAAAE,CAAA,CACA,CAMA,WAAAC,EAAAC,EAAA,CACA,KAAA,SAAA,EAAA,WAAAD,EAAAC,CAAA,CACA,CAKA,eAAAvB,EAAA,CACA,GAAA,CAAA,MAAAL,EAAA,OAAAD,CAAA,EAAA,KAAA,YAAA,EACAA,GACAM,EAAAL,CAAA,CAEA,CAKA,IAAAK,EAAA,CACA,IAAAwB,EAAAC,GAAA,IAAA,EACA,GAAA,CACAzB,EAAA,IAAA,CACA,QAAA,CACAyB,GAAAD,CAAA,CACA,CACA,CAKA,eAAAE,EAAA,CACA,IAAAhC,EAAA,KAAA,UAAA,EACA,GAAA,CAAAA,EAAA,OAAA,KACA,GAAA,CACA,OAAAA,EAAA,eAAAgC,CAAA,CACA,MAAA,CACA,OAAA,OAAA,iBAAA,KAAA,mBAAAC,EAAA,KAAA,+BAAAD,EAAA,yBAAA,EACA,IACA,CACA,CAKA,iBAAAH,EAAAK,EAAA,CACA,IAAAC,EAAA,KAAA,qBAAA,mBAAAN,EAAAK,CAAA,EAEA,IAAA,OAAA,iBAAA,KAAA,mBAAA,CAAAC,EAAA,CACA,IAAAnC,EAAA,KAAA,UAAA,EAQA,QAAA,KAPAA,EAOA;;;EAJA,8GAOA,EAIA,OAAAmC,CACA,CAKA,cAAA,CACA,OAAA,KAAA,qBAAA,cAAA,CACA,CAKA,eAAAC,EAAA,GAAA,CAEA,GAAAA,EACA,OAAA,KAAA,WAAA,EAIA,KAAA,mBAAA,CACA,CAKA,YAAA,CAEA,IAAAnC,EADA,KAAA,YAAA,EACA,MACAoC,EAAApC,EAAA,WAAA,EACAoC,GACAC,GAAAD,CAAA,EAEA,KAAA,mBAAA,EAGApC,EAAA,WAAA,CACA,CAKA,aAAA4B,EAAA,CACA,GAAA,CAAA,MAAA5B,EAAA,OAAAD,CAAA,EAAA,KAAA,YAAA,EACA,CAAA,QAAAuC,EAAA,YAAAC,EAAAC,CAAA,EAAAzC,GAAAA,EAAA,WAAA,GAAA,CAAA,EAGA,CAAA,UAAA0C,CAAA,EAAAC,EAAA,WAAA,CAAA,EAEAN,EAAAO,GAAA,CACA,QAAAL,EACA,YAAAC,EACA,KAAAvC,EAAA,QAAA,EACA,GAAAyC,GAAA,CAAA,UAAAA,CAAA,EACA,GAAAb,CACA,CAAA,EAGAgB,EAAA5C,EAAA,YAAAA,EAAA,WAAA,EACA,OAAA4C,GAAAA,EAAA,SAAA,MACAC,EAAAD,EAAA,CAAA,OAAA,QAAA,CAAA,EAEA,KAAA,WAAA,EAGA5C,EAAA,WAAAoC,CAAA,EAEAA,CACA,CAMA,sBAAA,CACA,IAAArC,EAAA,KAAA,UAAA,EACA+C,EAAA/C,GAAAA,EAAA,WAAA,EACA,MAAA,GAAA+C,GAAAA,EAAA,eACA,CAKA,oBAAA,CACA,GAAA,CAAA,MAAA9C,EAAA,OAAAD,CAAA,EAAA,KAAA,YAAA,EAEAqC,EAAApC,EAAA,WAAA,EACAoC,GAAArC,GAAAA,EAAA,gBACAA,EAAA,eAAAqC,CAAA,CAEA,CAQA,YAAA/B,EAAA,CACA,GAAA,CAAA,MAAAL,EAAA,OAAAD,CAAA,EAAA,KAAA,YAAA,EACAA,GACAM,EAAAN,EAAAC,CAAA,CAEA,CAOA,qBAAA+C,KAAAC,EAAA,CAEA,IAAAC,EADAC,EAAA,EACA,WACA,GAAAD,GAAAA,EAAA,YAAA,OAAAA,EAAA,WAAAF,CAAA,GAAA,WACA,OAAAE,EAAA,WAAAF,CAAA,EAAA,MAAA,KAAAC,CAAA,GAEA,OAAA,iBAAA,KAAA,mBAAAhB,EAAA,KAAA,oBAAAe,qCAAA,CACA,CACA,EASA,SAAAG,GAAA,CACA,OAAAR,EAAA,WAAAA,EAAA,YAAA,CACA,WAAA,CAAA,EACA,IAAA,MACA,EACAA,CACA,CAOA,SAAAZ,GAAAqB,EAAA,CACA,IAAAC,EAAAF,EAAA,EACArB,EAAAwB,GAAAD,CAAA,EACA,OAAAE,GAAAF,EAAAD,CAAA,EACAtB,CACA,CASA,SAAA0B,GAAA,CAEA,IAAAH,EAAAF,EAAA,EAEA,GAAAE,EAAA,YAAAA,EAAA,WAAA,IAAA,CACA,IAAAD,EAAAC,EAAA,WAAA,IAAA,cAAA,EAEA,GAAAD,EACA,OAAAA,EAKA,OAAAK,GAAAJ,CAAA,CACA,CAEA,SAAAI,GAAAJ,EAAAF,EAAA,EAAA,CAEA,OAAA,CAAAO,GAAAL,CAAA,GAAAC,GAAAD,CAAA,EAAA,YAAAxD,EAAA,IACA0D,GAAAF,EAAA,IAAAtD,CAAA,EAIAuD,GAAAD,CAAA,CACA,CAiDA,SAAAM,GAAAC,EAAA,CACA,MAAA,CAAA,EAAAA,GAAAA,EAAA,YAAAA,EAAA,WAAA,IACA,CAQA,SAAAC,GAAAD,EAAA,CACA,OAAAE,GAAA,MAAA,IAAA,IAAAC,EAAAH,CAAA,CACA,CAQA,SAAAI,GAAAJ,EAAAK,EAAA,CACA,GAAA,CAAAL,EAAA,MAAA,GACA,IAAAM,EAAAN,EAAA,WAAAA,EAAA,YAAA,CAAA,EACA,OAAAM,EAAA,IAAAD,EACA,EACA,CC/mBA,SAAAE,GAAAC,EAAA,CAGA,OAFAA,GAAAC,EAAA,GACA,SAAA,EACA,eAAA,CACA,CCnBA,IAAAC,GAAA,GAKA,SAAAC,IAAA,CACAD,KAIAA,GAAA,GACAE,GAAA,QAAAC,EAAA,EACAD,GAAA,qBAAAC,EAAA,EACA,CAKA,SAAAA,IAAA,CACA,IAAAC,EAAAC,GAAA,EACA,GAAAD,EAAA,CACA,IAAAE,EAAA,kBACA,OAAA,iBAAA,KAAA,mBAAAC,EAAA,IAAA,0BAAAD,2BAAA,EACAF,EAAA,UAAAE,CAAA,EAEA,CAIAH,GAAA,IAAA,8BChBA,IAAAK,GAAA,KAAA,CAKA,YAAAC,EAAA,IAAA,CACA,KAAA,QAAAA,EACA,KAAA,MAAA,CAAA,CACA,CAQA,IAAAC,EAAA,CACA,KAAA,MAAA,OAAA,KAAA,QACAA,EAAA,aAAA,OAEA,KAAA,MAAA,KAAAA,CAAA,CAEA,CACA,EAKAC,EAAA,KAAA,CAoFA,YAAAC,EAAA,CAAA,EAAA,CACA,KAAA,QAAAA,EAAA,SAAAC,EAAA,EACA,KAAA,OAAAD,EAAA,QAAAC,EAAA,EAAA,UAAA,EAAA,EACA,KAAA,eAAAD,EAAA,gBAAAE,EAAA,EACA,KAAA,KAAAF,EAAA,MAAA,CAAA,EACA,KAAA,KAAAA,EAAA,MAAA,CAAA,EACA,KAAA,aAAAA,EAAA,cAAA,SACA,KAAA,OAAAA,EAAA,QAAA,SAEAA,EAAA,eACA,KAAA,aAAAA,EAAA,cAGA,YAAAA,IACA,KAAA,QAAAA,EAAA,SAEAA,EAAA,KACA,KAAA,GAAAA,EAAA,IAEAA,EAAA,cACA,KAAA,YAAAA,EAAA,aAEAA,EAAA,OACA,KAAA,YAAAA,EAAA,MAEAA,EAAA,SACA,KAAA,OAAAA,EAAA,QAEAA,EAAA,eACA,KAAA,aAAAA,EAAA,aAEA,CAGA,IAAA,MAAA,CACA,OAAA,KAAA,aAAA,EACA,CAEA,IAAA,KAAAG,EAAA,CACA,KAAA,QAAAA,CAAA,CACA,CAKA,WACAH,EACA,CACA,IAAAI,EAAA,IAAAL,EAAA,CACA,GAAAC,EACA,aAAA,KAAA,OACA,QAAA,KAAA,QACA,QAAA,KAAA,OACA,CAAA,EASA,GAPAI,EAAA,aAAA,KAAA,aACAA,EAAA,cACAA,EAAA,aAAA,IAAAA,CAAA,EAGAA,EAAA,YAAA,KAAA,aAEA,OAAA,iBAAA,KAAA,mBAAAA,EAAA,YAAA,CACA,IAAAC,EAAAL,GAAAA,EAAA,IAAA,iBACAM,EAAAF,EAAA,YAAA,MAAA,mBACAG,EAAAH,EAAA,YAAA,OAEAI,EAAA,uBAAAH,2BAAAC,OAAAC,MACAH,EAAA,YAAA,SAAA,aAAAA,EAAA,MAAA,EAAA,CAAA,WAAAI,CAAA,EACAC,EAAA,IAAAD,CAAA,EAGA,OAAAJ,CACA,CAKA,OAAAM,EAAAC,EAAA,CACA,YAAA,KAAA,CAAA,GAAA,KAAA,KAAA,CAAAD,CAAA,EAAAC,CAAA,EACA,IACA,CAMA,QAAAD,EAAAC,EAAA,CACA,YAAA,KAAA,CAAA,GAAA,KAAA,KAAA,CAAAD,CAAA,EAAAC,CAAA,EACA,IACA,CAKA,UAAAA,EAAA,CACA,YAAA,OAAAA,EACA,IACA,CAKA,cAAAC,EAAA,CACA,KAAA,OAAA,mBAAA,OAAAA,CAAA,CAAA,EACA,KAAA,QAAA,4BAAAA,CAAA,EACA,IAAAC,EAAAC,GAAAF,CAAA,EACA,OAAAC,IAAA,iBACA,KAAA,UAAAA,CAAA,EAEA,IACA,CAKA,QAAAV,EAAA,CACA,KAAA,YAAAA,CACA,CAKA,WAAA,CACA,OAAA,KAAA,SAAA,IACA,CAKA,OAAAY,EAAA,CACA,IACA,OAAA,iBAAA,KAAA,mBAEA,KAAA,aACA,KAAA,YAAA,SAAA,KAAA,OACA,CACA,GAAA,CAAA,WAAAP,CAAA,EAAA,KAAA,YAAA,SAAA,aAAA,KAAA,MAAA,EACAA,GACAC,EAAA,IAAAD,EAAA,QAAA,WAAA,WAAA,CAAA,EAIA,KAAA,aAAA,OAAAO,GAAA,SAAAA,EAAAb,EAAA,CACA,CAKA,eAAA,CACA,OAAAc,GAAA,KAAA,QAAA,KAAA,OAAA,KAAA,OAAA,CACA,CAKA,WAAA,CACA,OAAAC,EAAA,CACA,KAAA,KAAA,KACA,YAAA,KAAA,YACA,aAAA,KAAA,aACA,GAAA,KAAA,GACA,aAAA,KAAA,aACA,QAAA,KAAA,QACA,OAAA,KAAA,OACA,eAAA,KAAA,eACA,OAAA,KAAA,OACA,KAAA,KAAA,KACA,QAAA,KAAA,OACA,CAAA,CACA,CAKA,kBAAAjB,EAAA,CACA,YAAA,KAAAA,EAAA,MAAA,CAAA,EACA,KAAA,YAAAA,EAAA,YACA,KAAA,aAAAA,EAAA,aACA,KAAA,GAAAA,EAAA,GACA,KAAA,aAAAA,EAAA,aACA,KAAA,QAAAA,EAAA,QACA,KAAA,OAAAA,EAAA,QAAA,KAAA,OACA,KAAA,eAAAA,EAAA,gBAAA,KAAA,eACA,KAAA,OAAAA,EAAA,OACA,KAAA,KAAAA,EAAA,MAAA,CAAA,EACA,KAAA,QAAAA,EAAA,SAAA,KAAA,QAEA,IACA,CAKA,iBAAA,CACA,OAAAiB,EAAA,CACA,KAAA,OAAA,KAAA,KAAA,IAAA,EAAA,OAAA,EAAA,KAAA,KAAA,OACA,YAAA,KAAA,YACA,GAAA,KAAA,GACA,eAAA,KAAA,aACA,QAAA,KAAA,OACA,OAAA,KAAA,OACA,KAAA,OAAA,KAAA,KAAA,IAAA,EAAA,OAAA,EAAA,KAAA,KAAA,OACA,SAAA,KAAA,OACA,CAAA,CACA,CAKA,QAaA,CACA,OAAAA,EAAA,CACA,KAAA,OAAA,KAAA,KAAA,IAAA,EAAA,OAAA,EAAA,KAAA,KAAA,OACA,YAAA,KAAA,YACA,GAAA,KAAA,GACA,eAAA,KAAA,aACA,QAAA,KAAA,OACA,gBAAA,KAAA,eACA,OAAA,KAAA,OACA,KAAA,OAAA,KAAA,KAAA,IAAA,EAAA,OAAA,EAAA,KAAA,KAAA,OACA,UAAA,KAAA,aACA,SAAA,KAAA,QACA,OAAA,KAAA,MACA,CAAA,CACA,CACA,EA4CA,SAAAH,GAAAF,EAAA,CACA,GAAAA,EAAA,KAAAA,GAAA,IACA,MAAA,KAGA,GAAAA,GAAA,KAAAA,EAAA,IACA,OAAAA,EAAA,CACA,IAAA,KACA,MAAA,kBACA,IAAA,KACA,MAAA,oBACA,IAAA,KACA,MAAA,YACA,IAAA,KACA,MAAA,iBACA,IAAA,KACA,MAAA,sBACA,IAAA,KACA,MAAA,qBACA,QACA,MAAA,kBACA,CAGA,GAAAA,GAAA,KAAAA,EAAA,IACA,OAAAA,EAAA,CACA,IAAA,KACA,MAAA,gBACA,IAAA,KACA,MAAA,cACA,IAAA,KACA,MAAA,oBACA,QACA,MAAA,gBACA,CAGA,MAAA,eACA,CCxbA,SAAAM,EACAC,EACAC,EACAC,EACA,CACA,IAAAC,EAAAF,EAAA,WAAA,EAEA,CAAA,UAAAG,CAAA,EAAAH,EAAA,OAAA,GAAA,CAAA,EACA,CAAA,QAAAI,CAAA,EAAAH,GAAAA,EAAA,QAAA,GAAA,CAAA,EAEAI,EAAAC,EAAA,CACA,YAAAJ,EAAA,aAAAK,EACA,QAAAL,EAAA,QACA,aAAAE,EACA,WAAAD,EACA,SAAAJ,CACA,CAAA,EAEA,OAAAC,EAAA,MAAAA,EAAA,KAAA,YAAAK,CAAA,EAEAA,CACA,CCZA,IAAAG,GAAA,cAAAC,CAAA,CAyBA,YAAAC,EAAAC,EAAA,CACA,MAAAD,CAAA,EAGA,OAAA,KAAA,YAEA,KAAA,cAAA,CAAA,EACA,KAAA,UAAA,CAAA,EAEA,KAAA,KAAAC,GAAAC,EAAA,EAEA,KAAA,MAAAF,EAAA,MAAA,GAEA,KAAA,SAAA,CACA,OAAA,SACA,GAAAA,EAAA,SACA,aAAA,CAAA,CACA,EAEA,KAAA,SAAAA,EAAA,QAGA,KAAA,YAAA,KAIA,IAAAG,EAAA,KAAA,SAAA,uBACAA,IAEA,KAAA,8BAAA,CAAA,GAAAA,CAAA,EAEA,CAGA,IAAA,MAAA,CACA,OAAA,KAAA,KACA,CAGA,IAAA,KAAAC,EAAA,CACA,KAAA,QAAAA,CAAA,CACA,CAKA,QAAAC,EAAAC,EAAA,SAAA,CACA,KAAA,MAAAD,EACA,KAAA,SAAA,OAAAC,CACA,CAMA,iBAAAC,EAAA,IAAA,CACA,KAAA,eACA,KAAA,aAAA,IAAAC,GAAAD,CAAA,GAEA,KAAA,aAAA,IAAA,IAAA,CACA,CAKA,WAAAE,EAAAC,EAAA,CACAA,IAAA,KAEA,OAAA,KAAA,UAAAD,CAAA,EAEA,KAAA,UAAAA,CAAA,EAAAC,CAEA,CAKA,eAAAL,EAAAM,EAAAC,EAAA,GAAA,CACA,KAAA,cAAAP,CAAA,EAAA,CAAA,MAAAM,EAAA,KAAAC,CAAA,CACA,CAKA,YAAAC,EAAA,CACA,KAAA,SAAA,CAAA,GAAA,KAAA,SAAA,GAAAA,CAAA,CACA,CAKA,OAAAC,EAAA,CACA,IAAAC,EAAA,KAAA,mBAAAD,CAAA,EACA,GAAAC,EAGA,OAAA,KAAA,KAAA,aAAAA,CAAA,CACA,CAKA,WAAA,CACA,IAAAC,EAAA,MAAA,UAAA,EAEA,OAAAC,EAAA,CACA,GAAAD,EACA,KAAA,KAAA,KACA,QAAA,KAAA,QACA,CAAA,CACA,CAKA,kBAAAhB,EAAA,CACA,aAAA,kBAAAA,CAAA,EAEA,KAAA,KAAAA,EAAA,MAAA,GAEA,KAAA,SAAAA,EAAA,QAEA,IACA,CAOA,2BAAA,CACA,GAAA,KAAA,8BACA,OAAA,KAAA,8BAGA,IAAAC,EAAA,KAAA,MAAAC,EAAA,EACAgB,EAAAjB,EAAA,UAAA,EAEA,GAAA,CAAAiB,EAAA,MAAA,CAAA,EAEA,IAAAC,EAAAlB,EAAA,SAAA,EACAmB,EAAAC,EAAA,KAAA,QAAAH,EAAAC,CAAA,EAEAG,EAAA,KAAA,SAAA,WACAA,IAAA,SACAF,EAAA,YAAA,GAAAE,KAIA,IAAAhB,EAAA,KAAA,SAAA,OACA,OAAAA,GAAAA,IAAA,QACAc,EAAA,YAAA,KAAA,MAGA,KAAA,UAAA,SACAA,EAAA,QAAA,OAAA,KAAA,OAAA,GAMAA,CACA,CAQA,OAAAnB,EAAA,CACA,KAAA,KAAAA,CACA,CAKA,mBAAAa,EAAA,CAEA,GAAA,KAAA,eAAA,OACA,OAGA,KAAA,QACA,OAAA,iBAAA,KAAA,mBAAAS,EAAA,KAAA,qEAAA,EACA,KAAA,KAAA,2BAIA,MAAA,OAAAT,CAAA,EAEA,IAAAI,EAAA,KAAA,KAAA,UAAA,EAKA,GAJAA,GAAAA,EAAA,MACAA,EAAA,KAAA,oBAAA,IAAA,EAGA,KAAA,UAAA,GAAA,EAEA,OAAA,iBAAA,KAAA,mBAAAK,EAAA,IAAA,kFAAA,EAEAL,GACAA,EAAA,mBAAA,cAAA,aAAA,EAGA,OAGA,IAAAM,EAAA,KAAA,aAAA,KAAA,aAAA,MAAA,OAAAC,GAAAA,IAAA,MAAAA,EAAA,YAAA,EAAA,CAAA,EAEA,KAAA,UAAAD,EAAA,OAAA,IACA,KAAA,aAAAA,EAAA,OAAA,CAAAE,EAAAC,IACAD,EAAA,cAAAC,EAAA,aACAD,EAAA,aAAAC,EAAA,aAAAD,EAAAC,EAEAD,CACA,EAAA,cAGA,IAAAE,EAAA,KAAA,SAEAb,EAAA,CACA,SAAA,CACA,GAAA,KAAA,UAEA,MAAA,KAAA,gBAAA,CACA,EACA,MAAAS,EACA,gBAAA,KAAA,eACA,KAAA,KAAA,KACA,UAAA,KAAA,aACA,YAAA,KAAA,KACA,KAAA,cACA,sBAAA,CACA,GAAAI,EACA,uBAAA,KAAA,0BAAA,CACA,EACA,GAAAA,EAAA,QAAA,CACA,iBAAA,CACA,OAAAA,EAAA,MACA,CACA,CACA,EAIA,OAFA,OAAA,KAAA,KAAA,aAAA,EAAA,OAAA,KAGA,OAAA,iBAAA,KAAA,mBACAL,EAAA,IACA,oDACA,KAAA,UAAA,KAAA,cAAA,OAAA,CAAA,CACA,EACAR,EAAA,aAAA,KAAA,gBAGA,OAAA,iBAAA,KAAA,mBAAAQ,EAAA,IAAA,uBAAA,KAAA,mBAAA,KAAA,OAAA,EAEAR,CACA,CACA,EClSA,SAAAc,GACAC,EACA,CACA,GAAA,OAAA,oBAAA,WAAA,CAAA,mBACA,MAAA,GAGA,IAAAC,EAAAC,EAAA,EAAA,UAAA,EACAC,EAAAH,GAAAC,GAAAA,EAAA,WAAA,EACA,MAAA,CAAA,CAAAE,IAAAA,EAAA,eAAA,qBAAAA,GAAA,kBAAAA,EACA,CCPA,SAAAC,GACAC,EACAC,EACAC,EACA,CAEA,GAAA,CAAAC,GAAAF,CAAA,EACA,OAAAD,EAAA,QAAA,GACAA,EAIA,GAAAA,EAAA,UAAA,OACA,OAAAA,EAAA,YAAA,CACA,WAAA,OAAAA,EAAA,OAAA,CACA,CAAA,EACAA,EAKA,IAAAI,EAuBA,OAtBA,OAAAH,EAAA,eAAA,YACAG,EAAAH,EAAA,cAAAC,CAAA,EACAF,EAAA,YAAA,CACA,WAAA,OAAAI,CAAA,CACA,CAAA,GACAF,EAAA,gBAAA,OACAE,EAAAF,EAAA,cACA,OAAAD,EAAA,iBAAA,KACAG,EAAAH,EAAA,iBACAD,EAAA,YAAA,CACA,WAAA,OAAAI,CAAA,CACA,CAAA,IAGAA,EAAA,EACAJ,EAAA,YAAA,CACA,WAAAI,CACA,CAAA,GAKAC,GAAAD,CAAA,EAOAA,GAeAJ,EAAA,QAAA,KAAA,OAAA,EAAAI,EAGAJ,EAAA,UAUA,OAAA,iBAAA,KAAA,mBAAAM,EAAA,IAAA,sBAAAN,EAAA,oBAAAA,EAAA,MAAA,EACAA,KAVA,OAAA,iBAAA,KAAA,mBACAM,EAAA,IACA,oGAAA,OACAF,CACA,IACA,EACAJ,MAxBA,OAAA,iBAAA,KAAA,mBACAM,EAAA,IACA,4CACA,OAAAL,EAAA,eAAA,WACA,oCACA,8EAEA,EACAD,EAAA,QAAA,GACAA,KAhBA,OAAA,iBAAA,KAAA,mBAAAM,EAAA,KAAA,kEAAA,EACAN,EAAA,QAAA,GACAA,EAkCA,CAKA,SAAAK,GAAAE,EAAA,CAGA,OAAAC,GAAAD,CAAA,GAAA,EAAA,OAAAA,GAAA,UAAA,OAAAA,GAAA,aACA,OAAA,iBAAA,KAAA,mBACAD,EAAA,KACA,0GAAA,KAAA,UACAC,CACA,aAAA,KAAA,UAAA,OAAAA,CAAA,IACA,EACA,IAIAA,EAAA,GAAAA,EAAA,IACA,OAAA,iBAAA,KAAA,mBACAD,EAAA,KAAA,oFAAAC,IAAA,EACA,IAEA,EACA,CC9GA,SAAAE,IAAA,CAEA,IAAAC,EADA,KAAA,SAAA,EACA,QAAA,EAEA,OAAAA,EACA,CACA,eAAAA,EAAA,cAAA,CACA,EACA,CAAA,CACA,CAiBA,SAAAC,GAEAC,EACAC,EACA,CACA,IAAAC,EAAA,KAAA,UAAA,EACAC,EAAAD,GAAAA,EAAA,WAAA,GAAA,CAAA,EAEAE,EAAAD,EAAA,cAAA,SACAE,EAAAL,EAAA,cAAA,SAEAI,IAAAC,KACA,OAAA,iBAAA,KAAA,mBACAC,EAAA,MACA,iDAAAD,6CAAAD;sDACAA,0CACA,EAEAJ,EAAA,QAAA,IAGA,IAAAO,EAAA,IAAAC,GAAAR,EAAA,IAAA,EACA,OAAAO,EAAAE,GAAAF,EAAAJ,EAAA,CACA,cAAAH,EAAA,cACA,mBAAAA,EACA,GAAAC,CACA,CAAA,EACAM,EAAA,SACAA,EAAA,iBAAAJ,EAAA,cAAAA,EAAA,aAAA,QAAA,EAEAD,GAAAA,EAAA,MACAA,EAAA,KAAA,mBAAAK,CAAA,EAEAA,CACA,CAmCA,SAAAG,IAAA,CACA,IAAAC,EAAAC,EAAA,EACAD,EAAA,aAGAA,EAAA,WAAA,WAAAA,EAAA,WAAA,YAAA,CAAA,EACAA,EAAA,WAAA,WAAA,mBACAA,EAAA,WAAA,WAAA,iBAAAE,IAEAF,EAAA,WAAA,WAAA,eACAA,EAAA,WAAA,WAAA,aAAAG,IAGAC,GAAA,EACA,CCrGA,IAAAC,GAAA,KAAA,CAQA,YAAAC,EAAAC,EAAA,CACA,KAAA,QAAAD,EACA,KAAA,aAAA,GACA,KAAA,mBAAA,CAAA,EACA,KAAA,WAAA,GAGA,KAAA,YAAA,YAAA,IAAA,KAAA,MAAA,EAAA,KAAA,aAAA,GAAA,EACA,KAAA,cAAAC,CACA,CAGA,OAAA,CACA,IAAAC,EAAA,KAAA,qBAAA,EACAA,EAAA,WAAA,SAAA,IAGA,KAAA,mBAAA,CAAA,EACA,KAAA,QAAA,YAAAA,CAAA,EACA,CAGA,sBAAA,CACA,IAAAC,EAAA,OAAA,KAAA,KAAA,kBAAA,EAAA,IAAAC,GACA,KAAA,mBAAA,SAAAA,CAAA,CAAA,CACA,EAEAF,EAAA,CACA,MAAA,KAAA,cACA,WAAAC,CACA,EACA,OAAAE,EAAAH,CAAA,CACA,CAGA,OAAA,CACA,cAAA,KAAA,WAAA,EACA,KAAA,WAAA,GACA,KAAA,MAAA,CACA,CAOA,6BAAA,CACA,GAAA,CAAA,KAAA,WACA,OAEA,IAAAI,EAAAC,EAAA,EAAA,SAAA,EACAC,EAAAF,EAAA,kBAAA,EAEAE,GAAAA,EAAA,SACA,KAAA,6BAAAA,EAAA,OAAA,IAAA,IAAA,EAGAF,EAAA,kBAAA,MAAA,EAGA,CAMA,6BAAAG,EAAAC,EAAA,CAEA,IAAAC,EAAA,IAAA,KAAAD,CAAA,EAAA,WAAA,EAAA,CAAA,EACA,KAAA,mBAAAC,CAAA,EAAA,KAAA,mBAAAA,CAAA,GAAA,CAAA,EAIA,IAAAC,EAAA,KAAA,mBAAAD,CAAA,EAKA,OAJAC,EAAA,UACAA,EAAA,QAAA,IAAA,KAAAD,CAAA,EAAA,YAAA,GAGAF,EAAA,CACA,IAAA,UACA,OAAAG,EAAA,SAAAA,EAAA,SAAA,GAAA,EACAA,EAAA,QACA,IAAA,KACA,OAAAA,EAAA,QAAAA,EAAA,QAAA,GAAA,EACAA,EAAA,OACA,QACA,OAAAA,EAAA,SAAAA,EAAA,SAAA,GAAA,EACAA,EAAA,OACA,CACA,CACA,EClHA,IAAAC,GAAA,IAGA,SAAAC,GAAAC,EAAA,CACA,IAAAC,EAAAD,EAAA,SAAA,GAAAA,EAAA,YAAA,GACAE,EAAAF,EAAA,KAAA,IAAAA,EAAA,OAAA,GACA,MAAA,GAAAC,MAAAD,EAAA,OAAAE,IAAAF,EAAA,KAAA,IAAAA,EAAA,OAAA,SACA,CAGA,SAAAG,GAAAH,EAAA,CACA,MAAA,GAAAD,GAAAC,CAAA,IAAAA,EAAA,qBACA,CAGA,SAAAI,GAAAJ,EAAAK,EAAA,CACA,OAAAC,GAAA,CAGA,WAAAN,EAAA,UACA,eAAAF,GACA,GAAAO,GAAA,CAAA,cAAA,GAAAA,EAAA,QAAAA,EAAA,SAAA,CACA,CAAA,CACA,CAOA,SAAAE,GACAP,EAGAQ,EAAA,CAAA,EACA,CAKA,IAAAC,EAAA,OAAAD,GAAA,SAAAA,EAAAA,EAAA,OACAH,EACA,OAAAG,GAAA,UAAA,CAAAA,EAAA,UAAA,OAAAA,EAAA,UAAA,IAEA,OAAAC,GAAA,GAAAN,GAAAH,CAAA,KAAAI,GAAAJ,EAAAK,CAAA,GACA,CCzBA,SAAAK,GAAAC,EAAAC,EAAA,CACA,OAAAA,IAGAD,EAAA,IAAAA,EAAA,KAAA,CAAA,EACAA,EAAA,IAAA,KAAAA,EAAA,IAAA,MAAAC,EAAA,KACAD,EAAA,IAAA,QAAAA,EAAA,IAAA,SAAAC,EAAA,QACAD,EAAA,IAAA,aAAA,CAAA,GAAAA,EAAA,IAAA,cAAA,CAAA,EAAA,GAAAC,EAAA,cAAA,CAAA,CAAA,EACAD,EAAA,IAAA,SAAA,CAAA,GAAAA,EAAA,IAAA,UAAA,CAAA,EAAA,GAAAC,EAAA,UAAA,CAAA,CAAA,GACAD,CACA,CAGA,SAAAE,GACAC,EACAC,EACAC,EACAC,EACA,CACA,IAAAL,EAAAM,GAAAF,CAAA,EACAG,EAAA,CACA,QAAA,IAAA,KAAA,EAAA,YAAA,EACA,GAAAP,GAAA,CAAA,IAAAA,CAAA,EACA,GAAA,CAAA,CAAAK,GAAAF,GAAA,CAAA,IAAAK,EAAAL,CAAA,CAAA,CACA,EAEAM,EACA,eAAAP,EAAA,CAAA,CAAA,KAAA,UAAA,EAAAA,CAAA,EAAA,CAAA,CAAA,KAAA,SAAA,EAAAA,EAAA,OAAA,CAAA,EAEA,OAAAQ,EAAAH,EAAA,CAAAE,CAAA,CAAA,CACA,CAKA,SAAAE,GACAZ,EACAI,EACAC,EACAC,EACA,CACA,IAAAL,EAAAM,GAAAF,CAAA,EASAQ,EAAAb,EAAA,MAAAA,EAAA,OAAA,eAAAA,EAAA,KAAA,QAEAD,GAAAC,EAAAK,GAAAA,EAAA,GAAA,EAEA,IAAAG,EAAAM,GAAAd,EAAAC,EAAAK,EAAAF,CAAA,EAMA,cAAAJ,EAAA,sBAGAW,EAAAH,EAAA,CADA,CAAA,CAAA,KAAAK,CAAA,EAAAb,CAAA,CACA,CAAA,CACA,CC3EA,IAAAe,GAAA,CAAA,EAaA,SAAAC,GAAAC,EAAA,CACA,IAAAC,EAAA,CAAA,EAEA,OAAAD,EAAA,QAAAE,GAAA,CACA,GAAA,CAAA,KAAAC,CAAA,EAAAD,EAEAE,EAAAH,EAAAE,CAAA,EAIAC,GAAA,CAAAA,EAAA,mBAAAF,EAAA,oBAIAD,EAAAE,CAAA,EAAAD,EACA,CAAA,EAEA,OAAA,KAAAD,CAAA,EAAA,IAAAI,GAAAJ,EAAAI,CAAA,CAAA,CACA,CAGA,SAAAC,GAAAC,EAAA,CACA,IAAAC,EAAAD,EAAA,qBAAA,CAAA,EACAE,EAAAF,EAAA,aAGAC,EAAA,QAAAE,GAAA,CACAA,EAAA,kBAAA,EACA,CAAA,EAEA,IAAAV,EAEA,MAAA,QAAAS,CAAA,EACAT,EAAA,CAAA,GAAAQ,EAAA,GAAAC,CAAA,EACA,OAAAA,GAAA,WACAT,EAAAW,GAAAF,EAAAD,CAAA,CAAA,EAEAR,EAAAQ,EAGA,IAAAI,EAAAb,GAAAC,CAAA,EAMAa,EAAAC,GAAAF,EAAAF,GAAAA,EAAA,OAAA,OAAA,EACA,GAAAG,IAAA,GAAA,CACA,GAAA,CAAAE,CAAA,EAAAH,EAAA,OAAAC,EAAA,CAAA,EACAD,EAAA,KAAAG,CAAA,EAGA,OAAAH,CACA,CAQA,SAAAI,GAAAC,EAAAjB,EAAA,CACA,IAAAkB,EAAA,CAAA,EAEA,OAAAlB,EAAA,QAAAU,GAAA,CAEAA,GACAS,GAAAF,EAAAP,EAAAQ,CAAA,CAEA,CAAA,EAEAA,CACA,CAGA,SAAAC,GAAAF,EAAAP,EAAAQ,EAAA,CAQA,GAPAA,EAAAR,EAAA,IAAA,EAAAA,EAEAZ,GAAA,QAAAY,EAAA,IAAA,IAAA,KACAA,EAAA,UAAAU,GAAAC,CAAA,EACAvB,GAAA,KAAAY,EAAA,IAAA,GAGAO,EAAA,IAAA,OAAAP,EAAA,iBAAA,WAAA,CACA,IAAAY,EAAAZ,EAAA,gBAAA,KAAAA,CAAA,EACAO,EAAA,GAAA,kBAAA,CAAAM,EAAAC,IAAAF,EAAAC,EAAAC,EAAAP,CAAA,CAAA,EAGA,GAAAA,EAAA,mBAAA,OAAAP,EAAA,cAAA,WAAA,CACA,IAAAY,EAAAZ,EAAA,aAAA,KAAAA,CAAA,EAEAe,EAAA,OAAA,OAAA,CAAAF,EAAAC,IAAAF,EAAAC,EAAAC,EAAAP,CAAA,EAAA,CACA,GAAAP,EAAA,IACA,CAAA,EAEAO,EAAA,kBAAAQ,CAAA,GAGA,OAAA,iBAAA,KAAA,mBAAAC,EAAA,IAAA,0BAAAhB,EAAA,MAAA,CACA,CAeA,SAAAiB,GAAAC,EAAAC,EAAA,CACA,QAAAC,EAAA,EAAAA,EAAAF,EAAA,OAAAE,IACA,GAAAD,EAAAD,EAAAE,CAAA,CAAA,IAAA,GACA,OAAAA,EAIA,MAAA,EACA,CC3HA,SAAAC,GACAC,EACAC,EACAC,EACAC,EACAC,EACA,CACA,GAAA,CAAA,eAAAC,EAAA,EAAA,oBAAAC,EAAA,GAAA,EAAAN,EACAO,EAAA,CACA,GAAAN,EACA,SAAAA,EAAA,UAAAC,EAAA,UAAAM,EAAA,EACA,UAAAP,EAAA,WAAAQ,EAAA,CACA,EACAC,EAAAR,EAAA,cAAAF,EAAA,aAAA,IAAAW,GAAAA,EAAA,IAAA,EAEAC,GAAAL,EAAAP,CAAA,EACAa,GAAAN,EAAAG,CAAA,EAGAT,EAAA,OAAA,QACAa,GAAAP,EAAAP,EAAA,WAAA,EAKA,IAAAe,EAAAZ,EACAD,EAAA,iBACAa,EAAAC,EAAA,MAAAD,CAAA,EAAA,OAAAb,EAAA,cAAA,GAIA,IAAAe,EAAAC,EAAAX,CAAA,EAEAY,EAAAf,GAAAA,EAAA,mBAAAA,EAAA,mBAAA,EAAA,CAAA,EASA,GAAAW,EAAA,CAEA,GAAAA,EAAA,eAAA,CACA,IAAAK,EAAA,CAAA,GAAAlB,EAAA,aAAA,CAAA,EAAA,GAAAa,EAAA,eAAA,CAAA,EAEAK,EAAA,SACAlB,EAAA,YAAAkB,GAKAH,EAAAF,EAAA,aAAAR,EAAAL,EAAAiB,CAAA,OAIAF,EAAAI,EAAA,CAAA,GAAAF,EAAA,GAAAG,GAAA,CAAA,EAAAf,EAAAL,CAAA,EAGA,OAAAe,EAAA,KAAAM,IACAA,GAKAC,GAAAD,CAAA,EAGA,OAAAlB,GAAA,UAAAA,EAAA,EACAoB,GAAAF,EAAAlB,EAAAC,CAAA,EAEAiB,EACA,CACA,CAQA,SAAAX,GAAAX,EAAAD,EAAA,CACA,GAAA,CAAA,YAAA0B,EAAA,QAAAC,EAAA,KAAAC,EAAA,eAAAC,EAAA,GAAA,EAAA7B,EAEA,gBAAAC,IACAA,EAAA,YAAA,gBAAAD,EAAA0B,EAAAI,GAGA7B,EAAA,UAAA,QAAA0B,IAAA,SACA1B,EAAA,QAAA0B,GAGA1B,EAAA,OAAA,QAAA2B,IAAA,SACA3B,EAAA,KAAA2B,GAGA3B,EAAA,UACAA,EAAA,QAAA8B,EAAA9B,EAAA,QAAA4B,CAAA,GAGA,IAAAG,EAAA/B,EAAA,WAAAA,EAAA,UAAA,QAAAA,EAAA,UAAA,OAAA,CAAA,EACA+B,GAAAA,EAAA,QACAA,EAAA,MAAAD,EAAAC,EAAA,MAAAH,CAAA,GAGA,IAAAI,EAAAhC,EAAA,QACAgC,GAAAA,EAAA,MACAA,EAAA,IAAAF,EAAAE,EAAA,IAAAJ,CAAA,EAEA,CAEA,IAAAK,GAAA,IAAA,QAKA,SAAApB,GAAAb,EAAAkC,EAAA,CACA,IAAAC,EAAAC,EAAA,gBAEA,GAAA,CAAAD,EACA,OAGA,IAAAE,EACAC,EAAAL,GAAA,IAAAC,CAAA,EACAI,EACAD,EAAAC,GAEAD,EAAA,IAAA,IACAJ,GAAA,IAAAC,EAAAG,CAAA,GAIA,IAAAE,EAAA,OAAA,KAAAJ,CAAA,EAAA,OAAA,CAAAK,EAAAC,IAAA,CACA,IAAAC,EACAC,EAAAN,EAAA,IAAAI,CAAA,EACAE,EACAD,EAAAC,GAEAD,EAAAR,EAAAO,CAAA,EACAJ,EAAA,IAAAI,EAAAC,CAAA,GAGA,QAAAhC,EAAAgC,EAAA,OAAA,EAAAhC,GAAA,EAAAA,IAAA,CACA,IAAAkC,EAAAF,EAAAhC,CAAA,EACA,GAAAkC,EAAA,SAAA,CACAJ,EAAAI,EAAA,QAAA,EAAAT,EAAAM,CAAA,EACA,OAGA,OAAAD,CACA,EAAA,CAAA,CAAA,EAEA,GAAA,CAEAxC,EAAA,UAAA,OAAA,QAAA+B,GAAA,CAEAA,EAAA,WAAA,OAAA,QAAAc,GAAA,CACAA,EAAA,WACAA,EAAA,SAAAN,EAAAM,EAAA,QAAA,EAEA,CAAA,CACA,CAAA,CACA,MAAA,CAEA,CACA,CAKA,SAAAtB,GAAAvB,EAAA,CAEA,IAAAuC,EAAA,CAAA,EACA,GAAA,CAEAvC,EAAA,UAAA,OAAA,QAAA+B,GAAA,CAEAA,EAAA,WAAA,OAAA,QAAAc,GAAA,CACAA,EAAA,WACAA,EAAA,SACAN,EAAAM,EAAA,QAAA,EAAAA,EAAA,SACAA,EAAA,WACAN,EAAAM,EAAA,QAAA,EAAAA,EAAA,UAEA,OAAAA,EAAA,SAEA,CAAA,CACA,CAAA,CACA,MAAA,CAEA,CAEA,GAAA,OAAA,KAAAN,CAAA,EAAA,SAAA,EACA,OAIAvC,EAAA,WAAAA,EAAA,YAAA,CAAA,EACAA,EAAA,WAAA,OAAAA,EAAA,WAAA,QAAA,CAAA,EACA,IAAA8C,EAAA9C,EAAA,WAAA,OACA,OAAA,KAAAuC,CAAA,EAAA,QAAAQ,GAAA,CACAD,EAAA,KAAA,CACA,KAAA,YACA,UAAAC,EACA,SAAAR,EAAAQ,CAAA,CACA,CAAA,CACA,CAAA,CACA,CAMA,SAAAnC,GAAAZ,EAAAgD,EAAA,CACAA,EAAA,OAAA,IACAhD,EAAA,IAAAA,EAAA,KAAA,CAAA,EACAA,EAAA,IAAA,aAAA,CAAA,GAAAA,EAAA,IAAA,cAAA,CAAA,EAAA,GAAAgD,CAAA,EAEA,CAYA,SAAAxB,GAAAxB,EAAAiD,EAAAC,EAAA,CACA,GAAA,CAAAlD,EACA,OAAA,KAGA,IAAAmD,EAAA,CACA,GAAAnD,EACA,GAAAA,EAAA,aAAA,CACA,YAAAA,EAAA,YAAA,IAAAoD,IAAA,CACA,GAAAA,EACA,GAAAA,EAAA,MAAA,CACA,KAAAC,EAAAD,EAAA,KAAAH,EAAAC,CAAA,CACA,CACA,EAAA,CACA,EACA,GAAAlD,EAAA,MAAA,CACA,KAAAqD,EAAArD,EAAA,KAAAiD,EAAAC,CAAA,CACA,EACA,GAAAlD,EAAA,UAAA,CACA,SAAAqD,EAAArD,EAAA,SAAAiD,EAAAC,CAAA,CACA,EACA,GAAAlD,EAAA,OAAA,CACA,MAAAqD,EAAArD,EAAA,MAAAiD,EAAAC,CAAA,CACA,CACA,EASA,OAAAlD,EAAA,UAAAA,EAAA,SAAA,OAAAmD,EAAA,WACAA,EAAA,SAAA,MAAAnD,EAAA,SAAA,MAGAA,EAAA,SAAA,MAAA,OACAmD,EAAA,SAAA,MAAA,KAAAE,EAAArD,EAAA,SAAA,MAAA,KAAAiD,EAAAC,CAAA,IAKAlD,EAAA,QACAmD,EAAA,MAAAnD,EAAA,MAAA,IAAAsD,IAEAA,EAAA,OACAA,EAAA,KAAAD,EAAAC,EAAA,KAAAL,EAAAC,CAAA,GAEAI,EACA,GAGAH,CACA,CCjQA,IAAAI,GAAA,8DAiCAC,GAAA,KAAA,CA+BA,YAAAC,EAAA,CAeA,GAdA,KAAA,SAAAA,EACA,KAAA,cAAA,CAAA,EACA,KAAA,yBAAA,GACA,KAAA,eAAA,EACA,KAAA,UAAA,CAAA,EACA,KAAA,OAAA,CAAA,EACA,KAAA,iBAAA,CAAA,EAEAA,EAAA,IACA,KAAA,KAAAC,GAAAD,EAAA,GAAA,GAEA,OAAA,iBAAA,KAAA,mBAAAE,EAAA,KAAA,+CAAA,EAGA,KAAA,KAAA,CACA,IAAAC,EAAAC,GAAA,KAAA,KAAAJ,CAAA,EACA,KAAA,WAAAA,EAAA,UAAA,CACA,mBAAA,KAAA,mBAAA,KAAA,IAAA,EACA,GAAAA,EAAA,iBACA,IAAAG,CACA,CAAA,EAEA,CAMA,iBAAAE,EAAAC,EAAAC,EAAA,CAEA,GAAAC,GAAAH,CAAA,EAAA,EACA,OAAA,iBAAA,KAAA,mBAAAH,EAAA,IAAAJ,EAAA,EACA,OAGA,IAAAW,EAAAH,GAAAA,EAAA,SAEA,YAAA,SACA,KAAA,mBAAAD,EAAAC,CAAA,EACA,KAAAI,GAAA,KAAA,cAAAA,EAAAJ,EAAAC,CAAA,CAAA,EACA,KAAAI,GAAA,CACAF,EAAAE,CACA,CAAA,CACA,EAEAF,CACA,CAKA,eACAG,EAEAC,EACAP,EACAC,EACA,CACA,IAAAE,EAAAH,GAAAA,EAAA,SAEAQ,EAAAC,GAAAH,CAAA,EACA,KAAA,iBAAA,OAAAA,CAAA,EAAAC,EAAAP,CAAA,EACA,KAAA,mBAAAM,EAAAN,CAAA,EAEA,YAAA,SACAQ,EACA,KAAAJ,GAAA,KAAA,cAAAA,EAAAJ,EAAAC,CAAA,CAAA,EACA,KAAAI,GAAA,CACAF,EAAAE,CACA,CAAA,CACA,EAEAF,CACA,CAKA,aAAAC,EAAAJ,EAAAC,EAAA,CAEA,GAAAD,GAAAA,EAAA,mBAAAE,GAAAF,EAAA,iBAAA,EAAA,EACA,OAAA,iBAAA,KAAA,mBAAAJ,EAAA,IAAAJ,EAAA,EACA,OAGA,IAAAW,EAAAH,GAAAA,EAAA,SAEA,YAAA,SACA,KAAA,cAAAI,EAAAJ,EAAAC,CAAA,EAAA,KAAAI,GAAA,CACAF,EAAAE,CACA,CAAA,CACA,EAEAF,CACA,CAKA,eAAAO,EAAA,CACA,OAAAA,EAAA,SAAA,UACA,OAAA,iBAAA,KAAA,mBAAAd,EAAA,KAAA,4DAAA,GAEA,KAAA,YAAAc,CAAA,EAEAC,EAAAD,EAAA,CAAA,KAAA,EAAA,CAAA,EAEA,CAKA,QAAA,CACA,OAAA,KAAA,IACA,CAKA,YAAA,CACA,OAAA,KAAA,QACA,CAOA,gBAAA,CACA,OAAA,KAAA,SAAA,SACA,CAKA,cAAA,CACA,OAAA,KAAA,UACA,CAKA,MAAAE,EAAA,CACA,IAAAC,EAAA,KAAA,WACA,OAAAA,EACA,KAAA,wBAAAD,CAAA,EAAA,KAAAE,GACAD,EAAA,MAAAD,CAAA,EAAA,KAAAG,GAAAD,GAAAC,CAAA,CACA,EAEAC,EAAA,EAAA,CAEA,CAKA,MAAAJ,EAAA,CACA,OAAA,KAAA,MAAAA,CAAA,EAAA,KAAAP,IACA,KAAA,WAAA,EAAA,QAAA,GACAA,EACA,CACA,CAGA,oBAAA,CACA,OAAA,KAAA,gBACA,CAGA,kBAAAY,EAAA,CACA,KAAA,iBAAA,KAAAA,CAAA,CACA,CAKA,kBAAAC,EAAA,EACAA,GAAA,CAAA,KAAA,0BAAA,KAAA,WAAA,GAAA,CAAA,KAAA,4BACA,KAAA,cAAAC,GAAA,KAAA,KAAA,SAAA,YAAA,EACA,KAAA,yBAAA,GAEA,CAOA,mBAAAC,EAAA,CACA,OAAA,KAAA,cAAAA,CAAA,CACA,CAKA,eAAAC,EAAA,CACA,GAAA,CACA,OAAA,KAAA,cAAAA,EAAA,EAAA,GAAA,IACA,MAAA,CACA,OAAA,OAAA,iBAAA,KAAA,mBAAAzB,EAAA,KAAA,+BAAAyB,EAAA,4BAAA,EACA,IACA,CACA,CAKA,eAAAA,EAAA,CACAC,GAAA,KAAAD,EAAA,KAAA,aAAA,CACA,CAKA,UAAAjB,EAAAJ,EAAA,CAAA,EAAA,CACA,KAAA,KAAA,kBAAAI,EAAAJ,CAAA,EAEA,IAAAuB,EAAAC,GAAApB,EAAA,KAAA,KAAA,KAAA,SAAA,UAAA,KAAA,SAAA,MAAA,EAEA,QAAAqB,KAAAzB,EAAA,aAAA,CAAA,EACAuB,EAAAG,GACAH,EACAI,GACAF,EACA,KAAA,SAAA,kBAAA,KAAA,SAAA,iBAAA,WACA,CACA,EAGA,IAAAG,EAAA,KAAA,cAAAL,CAAA,EACAK,GACAA,EAAA,KAAAC,GAAA,KAAA,KAAA,iBAAAzB,EAAAyB,CAAA,EAAA,IAAA,CAEA,CAKA,YAAAnB,EAAA,CACA,IAAAa,EAAAO,GAAApB,EAAA,KAAA,KAAA,KAAA,SAAA,UAAA,KAAA,SAAA,MAAA,EACA,KAAA,cAAAa,CAAA,CACA,CAKA,mBAAAQ,EAAAC,EAAAC,EAAA,CAGA,GAAA,KAAA,SAAA,kBAAA,CAOA,IAAAC,EAAA,GAAAH,KAAAC,KACA,OAAA,iBAAA,KAAA,mBAAApC,EAAA,IAAA,oBAAAsC,IAAA,EAGA,KAAA,UAAAA,CAAA,EAAA,KAAA,UAAAA,CAAA,EAAA,GAAA,EAEA,CAoCA,GAAAC,EAAAC,EAAA,CACA,KAAA,OAAAD,CAAA,IACA,KAAA,OAAAA,CAAA,EAAA,CAAA,GAIA,KAAA,OAAAA,CAAA,EAAA,KAAAC,CAAA,CACA,CA8BA,KAAAD,KAAAE,EAAA,CACA,KAAA,OAAAF,CAAA,GACA,KAAA,OAAAA,CAAA,EAAA,QAAAC,GAAAA,EAAA,GAAAC,CAAA,CAAA,CAEA,CAKA,wBAAA3B,EAAAN,EAAA,CACA,IAAAkC,EAAA,GACAC,EAAA,GACAC,EAAApC,EAAA,WAAAA,EAAA,UAAA,OAEA,GAAAoC,EAAA,CACAD,EAAA,GAEA,QAAAE,KAAAD,EAAA,CACA,IAAAE,EAAAD,EAAA,UACA,GAAAC,GAAAA,EAAA,UAAA,GAAA,CACAJ,EAAA,GACA,QAQA,IAAAK,EAAAjC,EAAA,SAAA,MACAiC,GAAAjC,EAAA,SAAA,GAAAiC,GAAAL,KAGA3B,EAAAD,EAAA,CACA,GAAA4B,GAAA,CAAA,OAAA,SAAA,EACA,OAAA5B,EAAA,QAAA,OAAA6B,GAAAD,CAAA,CACA,CAAA,EACA,KAAA,eAAA5B,CAAA,EAEA,CAYA,wBAAAE,EAAA,CACA,OAAA,IAAAgC,EAAAC,GAAA,CACA,IAAAC,EAAA,EACAC,EAAA,EAEAC,EAAA,YAAA,IAAA,CACA,KAAA,gBAAA,GACA,cAAAA,CAAA,EACAH,EAAA,EAAA,IAEAC,GAAAC,EACAnC,GAAAkC,GAAAlC,IACA,cAAAoC,CAAA,EACAH,EAAA,EAAA,GAGA,EAAAE,CAAA,CACA,CAAA,CACA,CAGA,YAAA,CACA,OAAA,KAAA,WAAA,EAAA,UAAA,IAAA,KAAA,aAAA,MACA,CAgBA,cAAA3C,EAAAJ,EAAAC,EAAA,CACA,IAAAP,EAAA,KAAA,WAAA,EACAuD,EAAA,OAAA,KAAA,KAAA,aAAA,EACA,MAAA,CAAAjD,EAAA,cAAAiD,EAAA,OAAA,IACAjD,EAAA,aAAAiD,GAGA,KAAA,KAAA,kBAAA7C,EAAAJ,CAAA,EAEAkD,GAAAxD,EAAAU,EAAAJ,EAAAC,EAAA,IAAA,EAAA,KAAAkD,GAAA,CACA,GAAAA,IAAA,KACA,OAAAA,EAMA,GAAA,CAAA,mBAAAC,CAAA,EAAAD,EAAA,uBAAA,CAAA,EAEA,GAAA,EADAA,EAAA,UAAAA,EAAA,SAAA,QACAC,EAAA,CACA,GAAA,CAAA,QAAAC,EAAA,OAAAC,EAAA,aAAAC,EAAA,IAAAC,CAAA,EAAAJ,EACAD,EAAA,SAAA,CACA,MAAA,CACA,SAAAE,EACA,QAAAC,EACA,eAAAC,CACA,EACA,GAAAJ,EAAA,QACA,EAEA,IAAAM,EAAAD,GAAAE,EAAAL,EAAA,KAAApD,CAAA,EAEAkD,EAAA,sBAAA,CACA,uBAAAM,EACA,GAAAN,EAAA,qBACA,EAEA,OAAAA,CACA,CAAA,CACA,CAQA,cAAA/C,EAAAJ,EAAA,CAAA,EAAAC,EAAA,CACA,OAAA,KAAA,cAAAG,EAAAJ,EAAAC,CAAA,EAAA,KACA0D,GACAA,EAAA,SAEA5B,GAAA,CACA,GAAA,OAAA,iBAAA,KAAA,iBAAA,CAGA,IAAA6B,EAAA7B,EACA6B,EAAA,WAAA,MACAhE,EAAA,IAAAgE,EAAA,OAAA,EAEAhE,EAAA,KAAAgE,CAAA,EAIA,CACA,CACA,CAeA,cAAAxD,EAAAJ,EAAAC,EAAA,CACA,IAAAP,EAAA,KAAA,WAAA,EACA,CAAA,WAAAmE,CAAA,EAAAnE,EAEAoE,EAAAC,GAAA3D,CAAA,EACA4D,EAAAC,GAAA7D,CAAA,EACA8D,EAAA9D,EAAA,MAAA,QACA+D,EAAA,0BAAAD,MAKA,GAAAF,GAAA,OAAAH,GAAA,UAAA,KAAA,OAAA,EAAAA,EACA,YAAA,mBAAA,cAAA,QAAAzD,CAAA,EACAgE,EACA,IAAAC,EACA,oFAAAR,KACA,KACA,CACA,EAGA,IAAAS,EAAAJ,IAAA,eAAA,SAAAA,EAEA,OAAA,KAAA,cAAA9D,EAAAJ,EAAAC,CAAA,EACA,KAAAsE,GAAA,CACA,GAAAA,IAAA,KACA,WAAA,mBAAA,kBAAAD,EAAAlE,CAAA,EACA,IAAAiE,EAAA,2DAAA,KAAA,EAIA,GADArE,EAAA,MAAAA,EAAA,KAAA,aAAA,GAEA,OAAAuE,EAGA,IAAAlE,EAAAmE,GAAA9E,EAAA6E,EAAAvE,CAAA,EACA,OAAAyE,GAAApE,EAAA8D,CAAA,CACA,CAAA,EACA,KAAAO,GAAA,CACA,GAAAA,IAAA,KACA,WAAA,mBAAA,cAAAJ,EAAAlE,CAAA,EACA,IAAAiE,EAAA,GAAAF,4CAAA,KAAA,EAGA,IAAAzD,EAAAT,GAAAA,EAAA,WAAA,EACA,CAAA6D,GAAApD,GACA,KAAA,wBAAAA,EAAAgE,CAAA,EAMA,IAAAC,EAAAD,EAAA,iBACA,GAAAZ,GAAAa,GAAAD,EAAA,cAAAtE,EAAA,YAAA,CACA,IAAAwE,EAAA,SACAF,EAAA,iBAAA,CACA,GAAAC,EACA,OAAAC,CACA,EAGA,YAAA,UAAAF,EAAA1E,CAAA,EACA0E,CACA,CAAA,EACA,KAAA,KAAA3C,GAAA,CACA,MAAAA,aAAAsC,EACAtC,GAGA,KAAA,iBAAAA,EAAA,CACA,KAAA,CACA,WAAA,EACA,EACA,kBAAAA,CACA,CAAA,EACA,IAAAsC,EACA;UAAAtC,GACA,EACA,CAAA,CACA,CAKA,SAAAH,EAAA,CACA,KAAA,iBACAA,EAAA,KACAiD,IACA,KAAA,iBACAA,GAEA9C,IACA,KAAA,iBACAA,EAEA,CACA,CAKA,cAAA+C,EAAA,CAGA,GAFA,KAAA,KAAA,iBAAAA,CAAA,EAEA,KAAA,WAAA,GAAA,KAAA,WACA,OAAA,KAAA,WAAA,KAAAA,CAAA,EAAA,KAAA,KAAA/C,GAAA,EACA,OAAA,iBAAA,KAAA,mBAAAnC,EAAA,MAAA,6BAAAmC,CAAA,CACA,CAAA,GAEA,OAAA,iBAAA,KAAA,mBAAAnC,EAAA,MAAA,oBAAA,CAEA,CAKA,gBAAA,CACA,IAAAmF,EAAA,KAAA,UACA,YAAA,UAAA,CAAA,EACA,OAAA,KAAAA,CAAA,EAAA,IAAA7C,GAAA,CACA,GAAA,CAAAH,EAAAC,CAAA,EAAAE,EAAA,MAAA,GAAA,EACA,MAAA,CACA,OAAAH,EACA,SAAAC,EACA,SAAA+C,EAAA7C,CAAA,CACA,CACA,CAAA,CACA,CAiBA,EAKA,SAAAuC,GACAO,EACAb,EACA,CACA,IAAAc,EAAA,GAAAd,2CACA,GAAAe,EAAAF,CAAA,EACA,OAAAA,EAAA,KACA5E,GAAA,CACA,GAAA,CAAA+E,EAAA/E,CAAA,GAAAA,IAAA,KACA,MAAA,IAAAiE,EAAAY,CAAA,EAEA,OAAA7E,CACA,EACAgF,GAAA,CACA,MAAA,IAAAf,EAAA,GAAAF,mBAAAiB,GAAA,CACA,CACA,EACA,GAAA,CAAAD,EAAAH,CAAA,GAAAA,IAAA,KACA,MAAA,IAAAX,EAAAY,CAAA,EAEA,OAAAD,CACA,CAKA,SAAAR,GACA9E,EACAU,EACAJ,EACA,CACA,GAAA,CAAA,WAAAqF,EAAA,sBAAAC,CAAA,EAAA5F,EAEA,OAAAuE,GAAA7D,CAAA,GAAAiF,EACAA,EAAAjF,EAAAJ,CAAA,EAGA+D,GAAA3D,CAAA,GAAAkF,EACAA,EAAAlF,EAAAJ,CAAA,EAGAI,CACA,CAEA,SAAA6D,GAAA7D,EAAA,CACA,OAAAA,EAAA,OAAA,MACA,CAEA,SAAA2D,GAAA3D,EAAA,CACA,OAAAA,EAAA,OAAA,aACA,CC3yBA,SAAAmF,GACAC,EACAC,EACAC,EACAC,EACAC,EACA,CACA,IAAAC,EAAA,CACA,QAAA,IAAA,KAAA,EAAA,YAAA,CACA,EAEAH,GAAAA,EAAA,MACAG,EAAA,IAAA,CACA,KAAAH,EAAA,IAAA,KACA,QAAAA,EAAA,IAAA,OACA,GAGAC,GAAAC,IACAC,EAAA,IAAAC,EAAAF,CAAA,GAGAH,IACAI,EAAA,MAAAE,EAAAN,CAAA,GAGA,IAAAO,EAAAC,GAAAT,CAAA,EACA,OAAAU,EAAAL,EAAA,CAAAG,CAAA,CAAA,CACA,CAEA,SAAAC,GAAAT,EAAA,CAIA,MAAA,CAHA,CACA,KAAA,UACA,EACAA,CAAA,CACA,CCjBA,IAAAW,GAAA,cAEAC,EAAA,CAOA,YAAAC,EAAA,CAEAC,GAAA,EAEA,MAAAD,CAAA,CACA,CAKA,mBAAAE,EAAAC,EAAA,CACA,OAAAC,EAAAC,GAAAC,EAAA,KAAA,SAAA,YAAAJ,EAAAC,CAAA,CAAA,CACA,CAKA,iBACAI,EAEAC,EAAA,OACAL,EACA,CACA,OAAAC,EACAK,GAAA,KAAA,SAAA,YAAAF,EAAAC,EAAAL,EAAA,KAAA,SAAA,gBAAA,CACA,CACA,CAMA,iBAAAD,EAAAC,EAAAO,EAAA,CAIA,GAAA,KAAA,SAAA,qBAAA,KAAA,iBAAAA,EAAA,CACA,IAAAC,EAAAD,EAAA,kBAAA,EAIAC,GAAAA,EAAA,SAAA,OACAA,EAAA,OAAA,WAIA,OAAA,MAAA,iBAAAT,EAAAC,EAAAO,CAAA,CACA,CAKA,aAAAE,EAAAT,EAAAO,EAAA,CAIA,GAAA,KAAA,SAAA,qBAAA,KAAA,iBAAAA,IACAE,EAAA,MAAA,eAEA,aAAAA,EAAA,WAAAA,EAAA,UAAA,QAAAA,EAAA,UAAA,OAAA,OAAA,EAGA,CACA,IAAAD,EAAAD,EAAA,kBAAA,EAIAC,GAAAA,EAAA,SAAA,OACAA,EAAA,OAAA,WAKA,OAAA,MAAA,aAAAC,EAAAT,EAAAO,CAAA,CACA,CAMA,MAAAG,EAAA,CACA,OAAA,KAAA,iBACA,KAAA,gBAAA,MAAA,EAEA,MAAA,MAAAA,CAAA,CACA,CAGA,oBAAA,CACA,GAAA,CAAA,QAAAC,EAAA,YAAAC,CAAA,EAAA,KAAA,SACAD,EAGA,KAAA,gBAAA,IAAAE,GAAA,KAAA,CACA,QAAAF,EACA,YAAAC,CACA,CAAA,GALA,OAAA,iBAAA,KAAA,mBAAAE,EAAA,KAAA,4EAAA,CAOA,CASA,eAAAC,EAAAC,EAAAT,EAAA,CACA,IAAAU,EAAAF,EAAA,SAAA,eAAAA,EAAA,UAAAA,EAAA,UAAAG,EAAA,EACA,GAAA,CAAA,KAAA,WAAA,EACA,OAAA,OAAA,iBAAA,KAAA,mBAAAJ,EAAA,KAAA,4CAAA,EACAG,EAGA,IAAApB,EAAA,KAAA,WAAA,EACA,CAAA,QAAAc,EAAA,YAAAC,EAAA,OAAAO,CAAA,EAAAtB,EAEAuB,EAAA,CACA,YAAAH,EACA,aAAAF,EAAA,YACA,OAAAA,EAAA,OACA,QAAAJ,EACA,YAAAC,CACA,EAEAG,EAAA,SAAA,gBACAK,EAAA,SAAAL,EAAA,UAGAC,IACAI,EAAA,eAAA,CACA,SAAAJ,EAAA,SACA,eAAAA,EAAA,cACA,YAAAA,EAAA,WACA,SAAAA,EAAA,QACA,GAGA,GAAA,CAAAK,EAAAC,CAAA,EAAA,KAAA,uBAAAf,CAAA,EACAe,IACAF,EAAA,SAAA,CACA,MAAAE,CACA,GAGA,IAAAC,EAAAC,GACAJ,EACAC,EACA,KAAA,eAAA,EACAF,EACA,KAAA,OAAA,CACA,EAEA,OAAA,OAAA,iBAAA,KAAA,mBAAAL,EAAA,KAAA,mBAAAC,EAAA,YAAAA,EAAA,MAAA,EACA,KAAA,cAAAQ,CAAA,EACAN,CACA,CAMA,wBAAA,CACA,KAAA,gBAGA,KAAA,gBAAA,4BAAA,GAFA,OAAA,iBAAA,KAAA,mBAAAH,EAAA,KAAA,gFAAA,CAIA,CAKA,cAAAL,EAAAT,EAAAO,EAAA,CACA,OAAA,KAAA,SAAA,WACAE,EAAA,SAAAA,EAAA,UAAA,KAAA,SAAA,UAGA,KAAA,SAAA,UACAA,EAAA,SAAA,CACA,GAAAA,EAAA,SACA,SAAAA,EAAA,UAAA,CAAA,GAAA,SAAA,KAAA,SAAA,OACA,GAGA,KAAA,SAAA,aACAA,EAAA,YAAAA,EAAA,aAAA,KAAA,SAAA,YAGA,MAAA,cAAAA,EAAAT,EAAAO,CAAA,CACA,CAGA,uBACAA,EACA,CACA,GAAA,CAAAA,EACA,MAAA,CAAA,OAAA,MAAA,EAGA,IAAAkB,EAAAlB,EAAA,QAAA,EACA,GAAAkB,EAEA,MAAA,CADAA,EAAA,YAAAA,EAAA,YAAA,0BAAA,EAAA,OACAA,EAAA,gBAAA,CAAA,EAGA,GAAA,CAAA,QAAAC,EAAA,OAAAC,EAAA,aAAAC,EAAA,IAAAC,CAAA,EAAAtB,EAAA,sBAAA,EACAe,EAAA,CACA,SAAAI,EACA,QAAAC,EACA,eAAAC,CACA,EACA,OAAAC,EACA,CAAAA,EAAAP,CAAA,EAGA,CAAAQ,EAAAJ,EAAA,KAAAnB,CAAA,EAAAe,CAAA,CACA,CACA,ECxOA,IAAAS,GAAA,GAQA,SAAAC,GACAC,EACAC,EACAC,EAAAC,GACAH,EAAA,YAAAF,EACA,EACA,CACA,IAAAM,EAAA,CAAA,EACAC,EAAAC,GAAAJ,EAAA,MAAAI,CAAA,EAEA,SAAAC,EAAAC,EAAA,CACA,IAAAC,EAAA,CAAA,EAcA,GAXAC,GAAAF,EAAA,CAAAG,EAAAC,IAAA,CACA,IAAAC,EAAAC,GAAAF,CAAA,EACA,GAAAG,GAAAX,EAAAS,CAAA,EAAA,CACA,IAAAG,EAAAC,GAAAN,EAAAC,CAAA,EACAZ,EAAA,mBAAA,oBAAAa,EAAAG,CAAA,OAEAP,EAAA,KAAAE,CAAA,CAEA,CAAA,EAGAF,EAAA,SAAA,EACA,OAAAS,EAAA,EAIA,IAAAC,EAAAC,EAAAZ,EAAA,CAAA,EAAAC,CAAA,EAGAY,EAAAC,GAAA,CACAZ,GAAAS,EAAA,CAAAR,EAAAC,IAAA,CACA,IAAAI,EAAAC,GAAAN,EAAAC,CAAA,EACAZ,EAAA,mBAAAsB,EAAAR,GAAAF,CAAA,EAAAI,CAAA,CACA,CAAA,CACA,EAEAO,EAAA,IACAtB,EAAA,CAAA,KAAAuB,GAAAL,EAAAnB,EAAA,WAAA,CAAA,CAAA,EAAA,KACAyB,IAEAA,EAAA,aAAA,SAAAA,EAAA,WAAA,KAAAA,EAAA,YAAA,OACA,OAAA,iBAAA,KAAA,mBAAAC,EAAA,KAAA,qCAAAD,EAAA,2BAAA,EAGArB,EAAAuB,GAAAvB,EAAAqB,CAAA,EACAA,GAEAG,GAAA,CACA,MAAAP,EAAA,eAAA,EACAO,CACA,CACA,EAEA,OAAA1B,EAAA,IAAAqB,CAAA,EAAA,KACAM,GAAAA,EACAD,GAAA,CACA,GAAAA,aAAAE,EACA,OAAA,OAAA,iBAAA,KAAA,mBAAAJ,EAAA,MAAA,+CAAA,EACAL,EAAA,gBAAA,EACAH,EAAA,EAEA,MAAAU,CAEA,CACA,CACA,CAIA,OAAArB,EAAA,0BAAA,GAEA,CACA,KAAAA,EACA,MAAAF,CACA,CACA,CAEA,SAAAY,GAAAN,EAAAC,EAAA,CACA,GAAA,EAAAA,IAAA,SAAAA,IAAA,eAIA,OAAA,MAAA,QAAAD,CAAA,EAAAA,EAAA,CAAA,EAAA,MACA,CCrHA,SAASoB,GAASC,EAAO,CACrB,OAAO,OAAOA,GAAU,UAAYA,IAAU,IAClD,CACA,SAASC,GAAYD,EAAO,CACxB,OAAQD,GAASC,CAAK,GAClB,YAAaA,GACb,OAAOA,EAAM,SAAY,WACzB,SAAUA,GACV,OAAOA,EAAM,MAAS,QAC9B,CACA,SAASE,GAAkBF,EAAO,CAC9B,OAAQD,GAASC,CAAK,GAAK,cAAeA,GAASC,GAAYD,EAAM,SAAY,CACrF,CAIA,SAASG,IAAmB,CAExB,GAAIC,EAAW,gBAAkBA,EAAW,eAAe,GACvD,OAAOA,EAAW,eAAe,EAEzC,CAQA,SAASC,GAAcC,EAAQC,EAAO,CAClC,OAAID,IAAW,QACXA,EAAOC,EAAM,CAAC,CAAC,EAAIA,EAAM,CAAC,EACnBD,GAGA,CAAE,CAACC,EAAM,CAAC,CAAC,EAAGA,EAAM,CAAC,CAAE,CAEtC,CAKA,SAASC,GAAiBC,EAAaC,EAAO,CAC1C,OAAOD,EAAYC,EAAM,OAAS,GAAI,CAAC,CAC3C,CAMA,SAASC,GAAeC,EAAI,CACxB,IAAMC,EAAUD,GAAMA,EAAG,QACzB,OAAKC,EAGDA,EAAQ,OAAS,OAAOA,EAAQ,MAAM,SAAY,SAC3CA,EAAQ,MAAM,QAElBA,EALI,kBAMf,CAIA,SAASC,GAAmBL,EAAaC,EAAO,CAC5C,IAAMK,EAAY,CACd,KAAML,EAAM,MAAQA,EAAM,YAAY,KACtC,MAAOC,GAAeD,CAAK,CAC/B,EACMM,EAASR,GAAiBC,EAAaC,CAAK,EAClD,OAAIM,EAAO,SACPD,EAAU,WAAa,CAAE,OAAAC,CAAO,GAEhCD,EAAU,OAAS,QAAaA,EAAU,QAAU,KACpDA,EAAU,MAAQ,8BAEfA,CACX,CAIA,SAASE,GAAsBC,EAAKT,EAAaM,EAAWI,EAAM,CAC9D,IAAIP,EAIEQ,GAHoBD,GAAQA,EAAK,MAAQjB,GAAkBiB,EAAK,IAAI,EACpEA,EAAK,KAAK,UACV,SACiC,CACnC,QAAS,GACT,KAAM,SACV,EACA,GAAKE,EAAQN,CAAS,EAsBlBH,EAAKG,MAtBgB,CACrB,GAAIO,EAAcP,CAAS,EAAG,CAG1B,IAAMF,EAAU,2CAA2CU,GAA+BR,CAAS,IAC7FS,EAASN,GAAK,UAAU,EACxBO,EAAiBD,GAAUA,EAAO,WAAW,EAAE,eACrDN,GAAK,eAAgBQ,GAAU,CAC3BA,EAAM,SAAS,iBAAkBC,EAAgBZ,EAAWU,CAAc,CAAC,CAC/E,CAAC,EACDb,EAAMO,GAAQA,EAAK,oBAAuB,IAAI,MAAMN,CAAO,EAC3DD,EAAG,QAAUC,OAKbD,EAAMO,GAAQA,EAAK,oBAAuB,IAAI,MAAMJ,CAAS,EAC7DH,EAAG,QAAUG,EAEjBK,EAAU,UAAY,GAK1B,IAAMQ,EAAQ,CACV,UAAW,CACP,OAAQ,CAACd,GAAmBL,EAAaG,CAAE,CAAC,CAChD,CACJ,EACA,OAAAiB,GAAsBD,EAAO,OAAW,MAAS,EACjDE,GAAsBF,EAAOR,CAAS,EAC/B,CACH,GAAGQ,EACH,SAAUT,GAAQA,EAAK,QAC3B,CACJ,CAIA,SAASY,GAAiBtB,EAAaI,EAASmB,EAAQ,OAAQb,EAAMc,EAAkB,CACpF,IAAML,EAAQ,CACV,SAAUT,GAAQA,EAAK,SACvB,MAAAa,EACA,QAAAnB,CACJ,EACA,GAAIoB,GAAoBd,GAAQA,EAAK,mBAAoB,CACrD,IAAMH,EAASR,GAAiBC,EAAaU,EAAK,kBAAkB,EAChEH,EAAO,SACPY,EAAM,UAAY,CACd,OAAQ,CACJ,CACI,MAAOf,EACP,WAAY,CAAE,OAAAG,CAAO,CACzB,CACJ,CACJ,GAGR,OAAOY,CACX,CAEA,IAAMM,GAAgB,EAChBC,GAAN,KAAmB,CAEf,KAAOA,GAAa,GACpB,MACA,YAAYC,EAAU,CAAC,EAAG,CACtB,KAAK,MAAQA,EAAQ,OAASF,EAClC,CACA,UAAUG,EAAyBC,EAAe,CAC9C,IAAMd,EAASc,EAAc,EAAE,UAAU,EACpCd,GAGLa,EAAwB,CAACT,EAAOT,IAAS,CACrC,IAAMoB,EAAOD,EAAc,EAAE,eAAeH,EAAY,EACxD,OAAKI,EAGEC,GAAQhB,EAAO,WAAW,EAAE,YAAae,EAAK,MAAOX,EAAOT,CAAI,EAF5DS,CAGf,CAAC,CACL,CACJ,EApBMa,GAANN,GACIO,GADED,GACK,KAAK,gBAoBhB,SAASD,GAAQG,EAAQC,EAAOhB,EAAOT,EAAM,CACzC,GAAI,CAACS,EAAM,WACP,CAACA,EAAM,UAAU,QACjB,CAACT,GACD,CAAC0B,EAAa1B,EAAK,kBAAmB,KAAK,EAC3C,OAAOS,EAEX,IAAMkB,EAAeC,GAAcJ,EAAQC,EAAOzB,EAAK,iBAAiB,EACxE,OAAAS,EAAM,UAAU,OAAS,CAAC,GAAGkB,EAAc,GAAGlB,EAAM,UAAU,MAAM,EAC7DA,CACX,CACA,SAASmB,GAAcJ,EAAQC,EAAOlC,EAAOsC,EAAQ,CAAC,EAAG,CACrD,GAAI,CAACH,EAAanC,EAAM,MAAO,KAAK,GAAKsC,EAAM,OAAS,GAAKJ,EACzD,OAAOI,EAEX,IAAMjC,EAAYD,GAAmB6B,EAAQjC,EAAM,KAAK,EACxD,OAAOqC,GAAcJ,EAAQC,EAAOlC,EAAM,MAAO,CAC7CK,EACA,GAAGiC,CACP,CAAC,CACL,CAEA,IAAMC,GAA4B,CAC9B,eAAgB,CAAC,SAAU,WAAW,CAC1C,EAxMAC,EAyMMC,GAAN,KAAkB,CAId,YAAYf,EAAU,CAAC,EAAG,CAF1BM,GAAA,YAAOS,GAAY,IACnBC,GAAA,KAAAF,EAAA,QAEIG,GAAA,KAAKH,EAAW,CAAE,GAAGD,GAA2B,GAAGb,CAAQ,EAC/D,CACA,UAAUC,EAAyBC,EAAe,CAC/BA,EAAc,EAAE,UAAU,GAIzCD,EAAyBT,GAAU,CAC/B,GAAM,CAAE,sBAAA0B,CAAsB,EAAI1B,EAElC,MAAI,CADSU,EAAc,EAAE,eAAea,EAAW,GAC1C,CAACG,IAGV,YAAaA,GACbA,EAAsB,mBAAmB,UACzC1B,EAAM,QAAU2B,GAAeD,EAAsB,QAASE,GAAA,KAAKN,EAAQ,EAC3EtB,EAAM,KAAO6B,GAAY7B,EAAM,MAAQ,CAAC,EAAG0B,EAAsB,QAASE,GAAA,KAAKN,EAAQ,GAEvF,gBAAiBI,IACb1B,EAAM,QACNA,EAAM,QAAQ,KAAO0B,EAAsB,YAG3C1B,EAAM,QAAU,CACZ,KAAM0B,EAAsB,WAChC,IAGD1B,CACX,CAAC,CACL,CACJ,EApCM8B,GAANP,GAGID,EAAA,YAFAR,GADEgB,GACK,KAAK,eA4ChB,SAASD,GAAYE,EAAMC,EAASxB,EAAS,CACzC,IAAMyB,EAAaD,EAAQ,QAAQ,IAAI,kBAAkB,EACnD,CAAE,WAAAE,CAAW,EAAI1B,EACjB2B,EAAU,CAAE,GAAGJ,CAAK,EAC1B,MAAI,EAAE,eAAgBA,IAClBE,GACAC,IAAe,QACfE,GAAcH,EAAYC,CAAU,IACpCC,EAAQ,WAAaF,GAElB,OAAO,KAAKE,CAAO,EAAE,OAAS,EAAIA,EAAU,MACvD,CAQA,SAASR,GAAeK,EAASxB,EAAS,CAEtC,IAAM6B,EAAeL,EAAQ,QAAQ,IAAI,QAAQ,EAC7CM,EACJ,GAAID,EACA,GAAI,CACAC,EAAUC,GAAYF,CAAY,CACtC,MACA,CAEA,CAEJ,IAAMG,EAAU,CAAC,EAEjB,OAAW,CAACC,EAAGC,CAAC,IAAKV,EAAQ,QAAQ,QAAQ,EACrCS,IAAM,WACND,EAAQC,CAAC,EAAIC,GAGrB,IAAMC,EAAe,CACjB,OAAQX,EAAQ,OAChB,QAAAM,EACA,QAAAE,CACJ,EACA,GAAI,CACA,IAAMI,EAAM,IAAI,IAAIZ,EAAQ,GAAG,EAC/BW,EAAa,IAAM,GAAGC,EAAI,aAAaA,EAAI,WAAWA,EAAI,WAC1DD,EAAa,aAAeC,EAAI,MACpC,MACA,CAEI,IAAMC,EAAKb,EAAQ,IAAI,QAAQ,GAAG,EAC9Ba,EAAK,EAELF,EAAa,IAAMX,EAAQ,KAG3BW,EAAa,IAAMX,EAAQ,IAAI,OAAO,EAAGa,CAAE,EAC3CF,EAAa,aAAeX,EAAQ,IAAI,OAAOa,EAAK,CAAC,EAE7D,CAEA,GAAM,CAAE,eAAAC,EAAgB,eAAAC,EAAgB,oBAAAC,CAAoB,EAAIxC,EAmBhE,GAlBIsC,IAAmB,QAAaH,EAAa,SAC7CA,EAAa,QAAUM,GAAuBN,EAAa,QAASG,CAAc,EAC9E,OAAO,KAAKH,EAAa,OAAO,EAAE,SAAW,GAC7C,OAAOA,EAAa,SAIxB,OAAOA,EAAa,QAEpBI,IAAmB,QAAaJ,EAAa,SAC7CA,EAAa,QAAUM,GAAuBN,EAAa,QAASI,CAAc,EAC9E,OAAO,KAAKJ,EAAa,OAAO,EAAE,SAAW,GAC7C,OAAOA,EAAa,SAIxB,OAAOA,EAAa,QAEpBK,IAAwB,OAAW,CACnC,IAAME,EAAS,OAAO,YAAY,IAAI,gBAAgBP,EAAa,YAAY,CAAC,EAC1EQ,EAAgB,IAAI,gBAC1B,OAAO,KAAKF,GAAuBC,EAAQF,CAAmB,CAAC,EAAE,QAASI,GAAe,CACrFD,EAAc,IAAIC,EAAYF,EAAOE,CAAU,CAAC,CACpD,CAAC,EACDT,EAAa,aAAeQ,EAAc,SAAS,OAGnD,OAAOR,EAAa,aAExB,OAAOA,CACX,CAQA,SAASP,GAAc1D,EAAQ2E,EAAW,CACtC,OAAI,OAAOA,GAAc,UACdA,EAEFA,aAAqB,OACnBA,EAAU,KAAK3E,CAAM,EAEvB,MAAM,QAAQ2E,CAAS,EACAA,EAAU,IAAKC,GAASA,EAAK,YAAY,CAAC,EAC3C,SAAS5E,CAAM,EAGnC,EAEf,CAQA,SAASuE,GAAuBvE,EAAQ2E,EAAW,CAC/C,IAAIE,EAAY,IAAM,GACtB,GAAI,OAAOF,GAAc,UACrB,OAAOA,EAAY3E,EAAS,CAAC,EAE5B,GAAI2E,aAAqB,OAC1BE,EAAaD,GAASD,EAAU,KAAKC,CAAI,UAEpC,MAAM,QAAQD,CAAS,EAAG,CAC/B,IAAMG,EAAsBH,EAAU,IAAKC,GAASA,EAAK,YAAY,CAAC,EACtEC,EAAaD,GAASE,EAAoB,SAASF,EAAK,YAAY,CAAC,MAGrE,OAAO,CAAC,EAEZ,OAAO,OAAO,KAAK5E,CAAM,EACpB,OAAO6E,CAAS,EAChB,OAAO,CAACE,EAASC,KAClBD,EAAQC,CAAG,EAAIhF,EAAOgF,CAAG,EAClBD,GACR,CAAC,CAAC,CACT,CAOA,SAASlB,GAAYF,EAAc,CAC/B,GAAI,OAAOA,GAAiB,SACxB,MAAO,CAAC,EAEZ,GAAI,CACA,OAAOA,EACF,MAAM,GAAG,EACT,IAAKsB,GAASA,EAAK,MAAM,GAAG,CAAC,EAC7B,OAAO,CAACC,EAAK,CAACC,EAAWC,CAAW,KACrCF,EAAI,mBAAmBC,EAAU,KAAK,CAAC,CAAC,EAAI,mBAAmBC,EAAY,KAAK,CAAC,EAC1EF,GACR,CAAC,CAAC,CACT,MACA,CACI,MAAO,CAAC,CACZ,CACJ,CAOA,SAASG,GAAkBC,EAAc1E,EAAK,CAC1C,IAAM2E,EAAmB,CAAC,EAC1B,OAAAD,EAAa,QAASE,GAAgB,CAClCD,EAAiBC,EAAY,IAAI,EAAIA,EACrCA,EAAY,UAAWC,GAAa,CAChC7E,EAAI,SAAS,GAAG,kBAAkB6E,CAAQ,CAC9C,EAAG,IAAM7E,CAAG,CAChB,CAAC,EACM2E,CACX,CAKA,IAAMG,GAAN,cAA2BC,EAAoB,CAM3CC,GAAO,KAKP,YAAY9D,EAAS,CACjBA,EAAQ,UAAYA,EAAQ,WAAa,CAAC,EAC1CA,EAAQ,UAAU,IAAMA,EAAQ,UAAU,KAAO,CAC7C,KAAM,YACN,SAAU,CACN,CACI,KAAM,gBACN,QAAS,OACb,CACJ,EACA,QAAS,OACb,EACA,MAAMA,CAAO,CACjB,CAIA,mBAAoB,CACZ,KAAK,WAAW,GAAK,CAAC,KAAK,0BAA4B,KAAK8D,KAC5D,KAAK,cAAgBP,GAAkB,KAAK,SAAS,aAAc,KAAKO,EAAI,EAC5E,KAAK,yBAA2B,GAExC,CACA,mBAAmBnF,EAAWI,EAAM,CAChC,OAAOgF,EAAoBlF,GAAsB,KAAKiF,GAAM,KAAK,SAAS,YAAanF,EAAWI,CAAI,CAAC,CAC3G,CACA,iBAAiBN,EAASmB,EAAQ,OAAQb,EAAM,CAC5C,OAAOgF,EAAoBpE,GAAiB,KAAK,SAAS,YAAalB,EAASmB,EAAOb,EAAM,KAAK,SAAS,gBAAgB,CAAC,CAChI,CACA,cAAcS,EAAOT,EAAMO,EAAO,CAC9B,OAAAE,EAAM,SAAWA,EAAM,UAAY,aAC/B,KAAK,WAAW,EAAE,UAElBA,EAAM,sBAAwBvB,GAAcuB,EAAM,sBAAuB,CACrE,UACA,KAAK,WAAW,EAAE,OACtB,CAAC,GAED,KAAK,WAAW,EAAE,cAElBA,EAAM,sBAAwBvB,GAAcuB,EAAM,sBAAuB,CACrE,cACA,KAAK,WAAW,EAAE,WACtB,CAAC,GAEE,MAAM,cAAcA,EAAOT,EAAMO,CAAK,CACjD,CACA,QAAS,CACL,OAAO,KAAKwE,EAChB,CACA,OAAOhF,EAAK,CACR,KAAKgF,GAAOhF,CAChB,CASA,eAAekF,EAAM,CACjB,KAAK,WAAW,EAAE,YAAcA,CACpC,CAMA,WAAWC,EAAS,CAChB,KAAK,WAAW,EAAE,QAAUA,CAChC,CACJ,EAOA,SAASC,GAAuBC,EAAW,CACvC,GAAM,CAACC,EAAMC,CAAI,EAAIC,GAAoBH,CAAS,EAgBlD,MAAO,CAACC,EAfIG,GAAS,CACjB,IAAMC,EAASH,EAAKE,CAAI,EACxB,GAAIC,EAAQ,CACR,IAAMC,EAAWD,EAAO,SAExBA,EAAO,SACHC,IAAa,QAAa,CAACA,EAAS,WAAW,GAAG,EAC5C,IAAIA,IACJA,EAGVD,EAAO,OAASC,IAAa,OAEjC,OAAOD,CACX,CACgB,CACpB,CAOA,SAASL,GAAUM,EAAU,CACzB,GAAKA,EAIL,OAAOC,GAASD,EAAU,KAAK,CACnC,CAEA,IAAME,GAAqBC,GAAkBV,GAAuBC,EAAS,CAAC,EAK9E,SAASU,GAAmB7E,EAAS,CACjC,SAAS8E,EAAY,CAAE,KAAAd,CAAM,EAAG,CAC5B,GAAI,CAEA,IAAMxC,GADUxB,EAAQ,SAAW,OACXA,EAAQ,IAAK,CACjC,OAAQ,OACR,QAASA,EAAQ,QACjB,KAAAgE,CACJ,CAAC,EAAE,KAAMe,IACE,CACH,WAAYA,EAAS,OACrB,QAAS,CACL,cAAeA,EAAS,QAAQ,IAAI,aAAa,EACjD,uBAAwBA,EAAS,QAAQ,IAAI,sBAAsB,CACvE,CACJ,EACH,EAID,OAAI/E,EAAQ,SACRA,EAAQ,QAAQ,UAAUwB,CAAO,EAE9BA,CACX,OACOwD,EAAP,CACI,OAAOC,EAAoBD,CAAC,CAChC,CACJ,CACA,OAAOE,GAAgBlF,EAAS8E,CAAW,CAC/C,CAKA,IAAMK,GAAN,cAAqBC,CAAI,CACrB,YAAYpF,EAAS,CAYjB,GAXAA,EAAQ,oBACJA,EAAQ,sBAAwB,GAC1B,CAAC,EACD,CACE,GAAI,MAAM,QAAQA,EAAQ,mBAAmB,EACvCA,EAAQ,oBACR,CACE,IAAIsB,GAAYtB,EAAQ,kBAAkB,EAC1C,IAAIK,EACR,CACR,EACJL,EAAQ,UAAY,OAAW,CAC/B,IAAMqF,EAAkBtH,GAAiB,EACrCsH,IAAoB,SACpBrF,EAAQ,QAAUqF,GAG1B,IAAMjG,EAAS,IAAIwE,GAAa,CAC5B,GAAG5D,EACH,UAAW6E,GACX,aAAcS,GAAuBtF,CAAO,EAC5C,YAAauF,GAAkCvF,EAAQ,aAAe2E,EAAkB,EACxF,iBAAkB,CACd,GAAG3E,EAAQ,iBACX,QAASA,EAAQ,OACrB,CACJ,CAAC,EACD,MAAMZ,CAAM,EACZA,EAAO,OAAO,IAAI,EAClBA,EAAO,kBAAkB,CAC7B,CASA,eAAe4E,EAAM,CACjB,KAAK,UAAU,GAAG,eAAeA,CAAI,CACzC,CAMA,WAAWC,EAAS,CAChB,KAAK,UAAU,GAAG,WAAWA,CAAO,CACxC,CAQA,eAAeuB,EAASC,EAAenG,EAAO,CAC1C,OAAIkG,EAAQ,SAAW,eACnB,KAAK,WAAW,UAAW,CAAE,KAAMA,EAAQ,WAAY,CAAC,EAE7C,KAAK,UAAU,EAChB,eAAeA,EAASC,EAAenG,CAAK,CAC9D,CACJ,ECppBO,SAASoG,GACfC,EACAC,EACAC,EACAC,EACAC,EACqB,CAErB,GAAI,EAAEF,GAAOC,GAAYC,GACxB,OAED,IAAMC,EAAS,IAAIC,GAAO,CACzB,IAAAJ,EACA,QAAAF,EACA,QAAAC,EACA,WAAY,EACZ,mBAAoB,CACnB,eAAgB,CACf,aACA,eACA,kBACA,kBACA,SACA,iBACA,eACA,MACD,EACA,oBAAqB,MACtB,EAEA,iBAAkB,CACjB,QAAS,CACR,sBAAuBE,EACvB,0BAA2BC,CAC5B,CACD,CACD,CAAC,EACKG,EAAOP,EAAQ,IAAI,MAAQ,UACjCK,EAAO,OAAO,OAAQE,CAAc,EAEpC,IAAMC,EAAYR,EAAQ,QAAQ,IAAI,YAAY,GAAK,aACvD,OAAAK,EAAO,QAAQ,CAAE,UAAWG,EAAW,KAAMD,CAAK,CAAC,EAC5CF,CACR,CCtCO,IAAMI,GAAN,KAAqB,CAG3B,YAAYC,EAAmB,CAC9B,KAAK,KAAOA,CACb,CAEA,MAAM,IAAIC,EAAkB,CAC3B,IAAMC,EAAW,MAAMC,GAASF,CAAQ,EAClCG,EAAQC,GACb,IAAI,WAAW,KAAK,KAAM,EAAW,EACrCH,CACD,EACA,OAAOE,EAAQE,GAAiBF,CAAK,EAAI,IAC1C,CACD,EAEMD,GAAW,MAAOI,GAAiB,CAExC,IAAMP,EADU,IAAI,YAAY,EACX,OAAOO,CAAI,EAC1BC,EAAa,MAAM,OAAO,OAAO,OAAO,UAAWR,EAAK,MAAM,EACpE,OAAO,IAAI,WAAWQ,EAAY,EAAG,EAAc,CACpD,EAEMH,GAAe,CACpBI,EACAC,IACwB,CACxB,GAAID,EAAI,aAAe,EACtB,MAAO,GAER,IAAME,EACLF,EAAI,YAAeA,EAAI,WAAa,IAAe,GAAK,GACnDG,EAAU,IAAI,WAAWH,EAAI,OAAQE,EAAQ,EAAc,EACjE,GAAIC,EAAQ,aAAeF,EAAY,WACtC,MAAM,IAAI,UACT,yDACD,EAED,IAAMG,EAAMC,GAAQJ,EAAaE,CAAO,EACxC,GAAIC,EAAM,EAAG,CACZ,IAAME,EAAaN,EAAI,WACjBO,EAAaL,EAASF,EAAI,WAChC,OAAOJ,GACN,IAAI,WAAWI,EAAI,OAAQM,EAAYC,CAAU,EACjDN,CACD,UACUG,EAAM,EAAG,CACnB,IAAME,EAAaJ,EAAS,GACtBK,EAAaP,EAAI,OAAO,WAAaE,EAAS,GACpD,OAAON,GACN,IAAI,WAAWI,EAAI,OAAQM,EAAYC,CAAU,EACjDN,CACD,MAEA,QAAO,IAAI,WAAWD,EAAI,OAAQE,EAAQ,EAAU,CAEtD,EAEMG,GAAU,CAACG,EAAeC,IAAkB,CACjD,GAAID,EAAE,WAAaC,EAAE,WACpB,MAAO,GAER,GAAID,EAAE,WAAaC,EAAE,WACpB,MAAO,GAGR,OAAW,CAACC,EAAGC,CAAC,IAAKH,EAAE,QAAQ,EAAG,CACjC,GAAIG,EAAIF,EAAEC,CAAC,EACV,MAAO,GAER,GAAIC,EAAIF,EAAEC,CAAC,EACV,MAAO,GAIT,MAAO,EACR,EAEMb,GAAoBe,GAKlB,CAAC,GAJYA,EAAO,MAC1B,GACA,GAAiB,EAClB,CACsB,EAAE,IAAKH,GAAMA,EAAE,SAAS,EAAE,EAAE,SAAS,EAAG,GAAG,CAAC,EAAE,KAAK,EAAE,ECzFrE,IAAMI,GACZC,IAEO,CACN,cAAeA,GAAe,eAAiB,sBAC/C,mBAAoBA,GAAe,oBAAsB,MAC1D,GCRM,IAAMC,GAAN,cAAyB,QAAS,CACxC,YAAYC,EAAuBC,EAAqB,CACvD,MAAMD,EAAM,CACX,GAAGC,EACH,OAAQ,GACT,CAAC,CACF,CACD,EAEaC,GAAN,cAA+B,QAAS,CAC9C,eAAe,CAACF,EAAMC,CAAI,EAA2C,CACpE,MAAMD,EAAM,CACX,GAAGC,EACH,OAAQ,IACR,WAAY,WACb,CAAC,CACF,CACD,EAEaE,EAAN,cAAuC,QAAS,CACtD,eAAe,CAACH,EAAMC,CAAI,EAA2C,CACpE,MAAMD,EAAM,CACX,GAAGC,EACH,OAAQ,IACR,WAAY,oBACb,CAAC,CACF,CACD,EAEaG,GAAN,cAA0C,QAAS,CACzD,YAAYC,EAAYJ,EAAqB,CAC5C,MAAM,KAAM,CACX,GAAGA,EACH,OAAQ,GACT,CAAC,CACF,CACD,EAEaK,GAAN,cAAkC,QAAS,CACjD,eAAe,CAACC,EAAON,CAAI,EAA2C,CACrE,MAAM,KAAM,CACX,GAAGA,EACH,OAAQ,IACR,WAAY,cACb,CAAC,CACF,CACD,EAEaO,GAAN,cAAwC,QAAS,CACvD,YAAYC,EAAkBR,EAAqB,CAClD,MAAM,KAAM,CACX,GAAGA,EACH,OAAQ,IACR,WAAY,qBACZ,QAAS,CACR,GAAGA,GAAM,QACT,SAAUQ,CACX,CACD,CAAC,CACF,CACD,EC3DO,IAAMC,GAAwB,qCCO9B,SAASC,GACfC,EACAC,EACAC,EACC,CACD,IAAMC,EAAU,IAAI,QAAQ,CAC3B,eAAgBF,EAChB,KAAM,IAAID,IACX,CAAC,EAED,OAAII,GAAYF,CAAO,GACtBC,EAAQ,OAAO,gBAAiBE,EAAqB,EAG/CF,CACR,CAEA,SAASC,GAAYF,EAAkB,CACtC,MAAO,CAACA,EAAQ,QAAQ,IAAI,eAAe,GAAK,CAACA,EAAQ,QAAQ,IAAI,OAAO,CAC7E,CCfO,IAAMI,GAAgB,MAC5BC,EACAC,EACAC,EACAC,IACI,CACJ,GAAM,CAAE,SAAAC,EAAU,OAAAC,CAAO,EAAI,IAAI,IAAIL,EAAQ,GAAG,EAE1CM,EAAS,MAAMC,GAAUH,EAAUH,EAAeC,CAAM,EAC9D,GAAI,CAACI,EACJ,OAAO,IAAIE,GAMZ,IAAMC,EAAST,EAAQ,OAAO,YAAY,EAC1C,GAAI,CAAC,CAAC,MAAO,MAAM,EAAE,SAASS,CAAM,EACnC,OAAO,IAAIC,EAEZ,GAAIJ,EAAO,SACV,OAAO,IAAIK,GAA0BL,EAAO,SAAWD,CAAM,EAE9D,GAAI,CAACC,EAAO,MACX,OAAO,IAAIM,GAA4B,IAAI,MAAM,gBAAgB,CAAC,EAGnE,IAAMC,EAAQ,MAAMV,EAAUG,EAAO,MAAM,IAAI,EAEzCQ,EAAUC,GAAWT,EAAO,MAAM,KAAMO,EAAM,YAAab,CAAO,EAElEgB,EAAa,IAAIV,EAAO,MAAM,QAC9BW,EAAW,KAAKD,IAChBE,EAAclB,EAAQ,QAAQ,IAAI,eAAe,GAAK,GAC5D,GAAI,CAACiB,EAAUD,CAAU,EAAE,SAASE,CAAW,EAC9C,OAAO,IAAIC,GAAoB,KAAM,CAAE,QAAAL,CAAQ,CAAC,EAGjD,IAAMM,EAAOX,IAAW,OAAS,KAAOI,EAAM,eAC9C,OAAQP,EAAO,MAAM,OAAQ,CAC5B,IAAK,KACJ,OAAO,IAAIE,GAAiBY,EAAM,CAAE,QAAAN,CAAQ,CAAC,EAC9C,IAAK,KACJ,OAAO,IAAIO,GAAWD,EAAM,CAAE,QAAAN,CAAQ,CAAC,CACzC,CACD,EAUaP,GAAY,MACxBH,EACAH,EACAC,EACAoB,EAAgB,KACK,CACrB,OAAQrB,EAAc,cAAe,CACpC,IAAK,sBACJ,OAAOsB,GACNnB,EACAH,EACAC,EACAoB,CACD,EAED,IAAK,uBACJ,OAAOE,GACNpB,EACAH,EACAC,EACAoB,CACD,EAED,IAAK,sBACJ,OAAOG,GACNrB,EACAH,EACAC,EACAoB,CACD,EAED,IAAK,OACJ,OAAOI,GAAiBtB,EAAUH,EAAeC,CAAM,CAEzD,CACD,EAEMqB,GAAgC,MACrCnB,EACAH,EACAC,EACAoB,IACqB,CACrB,IAAIK,EAAyB,KACzBC,EAA4B,KAC1BC,EAAY,MAAM3B,EAAOE,CAAQ,EACvC,GAAIA,EAAS,SAAS,QAAQ,EAAG,CAChC,GAAIyB,EAEH,MAAO,CAAE,MAAO,CAAE,KAAMA,EAAW,OAAQ,GAAI,EAAG,SAAU,IAAK,EAEjE,GACEF,EAAiB,MAAMG,EACvB,GAAG1B,SACHA,EAAS,MAAM,EAAG,EAAe,EACjCH,EACAC,EACAoB,CACD,EAGA,OAAOK,EACD,GACLA,EAAiB,MAAMG,EACvB,GAAG1B,EAAS,MAAM,EAAG,EAAgB,SACrCA,EAAS,MAAM,EAAG,EAAgB,EAClCH,EACAC,EACAoB,CACD,EAGA,OAAOK,UAGCvB,EAAS,SAAS,aAAa,EAAG,CAC5C,GACEuB,EAAiB,MAAMG,EACvB1B,EACAA,EAAS,MAAM,EAAG,GAAoB,EACtCH,EACAC,EACAoB,CACD,EAGA,OAAOK,EACD,GACLA,EAAiB,MAAMG,EACvB,GAAG1B,EAAS,MAAM,EAAG,GAAqB,SAC1CA,EAAS,MAAM,EAAG,GAAqB,EACvCH,EACAC,EACAoB,CACD,EAGA,OAAOK,UAEEvB,EAAS,SAAS,GAAG,EAAG,CAClC,GAAKwB,EAAa,MAAM1B,EAAO,GAAGE,aAAoB,EAErD,MAAO,CAAE,MAAO,CAAE,KAAMwB,EAAY,OAAQ,GAAI,EAAG,SAAU,IAAK,EAC5D,GACLD,EAAiB,MAAMG,EACvB,GAAG1B,EAAS,MAAM,EAAG,EAAW,SAChCA,EAAS,MAAM,EAAG,EAAW,EAC7BH,EACAC,EACAoB,CACD,EAGA,OAAOK,UAEEvB,EAAS,SAAS,OAAO,EAAG,CACtC,GACEuB,EAAiB,MAAMG,EACvB1B,EACAA,EAAS,MAAM,EAAG,EAAe,EACjCH,EACAC,EACAoB,CACD,EAGA,OAAOK,EACD,GACLA,EAAiB,MAAMG,EACvB,GAAG1B,EAAS,MAAM,EAAG,EAAe,eACpC,GAAGA,EAAS,MAAM,EAAG,EAAe,KACpCH,EACAC,EACAoB,CACD,EAGA,OAAOK,EAIT,OAAIE,EAEI,CAAE,MAAO,CAAE,KAAMA,EAAW,OAAQ,GAAI,EAAG,SAAU,IAAK,GACtDD,EAAa,MAAM1B,EAAO,GAAGE,QAAe,GAEhD,CAAE,MAAO,CAAE,KAAMwB,EAAY,OAAQ,GAAI,EAAG,SAAU,IAAK,GAEjED,EAAiB,MAAMG,EACvB,GAAG1B,eACH,GAAGA,KACHH,EACAC,EACAoB,CACD,GAGOK,EAGDI,GAAS3B,EAAUH,EAAeC,CAAM,CAChD,EAEMsB,GAAiC,MACtCpB,EACAH,EACAC,EACAoB,IACqB,CACrB,IAAIK,EAAyB,KACzBC,EAA4B,KAC1BC,EAAY,MAAM3B,EAAOE,CAAQ,EACvC,GAAIA,EAAS,SAAS,QAAQ,EAAG,CAChC,GAAIyB,EAEH,MAAO,CAAE,MAAO,CAAE,KAAMA,EAAW,OAAQ,GAAI,EAAG,SAAU,IAAK,EAEjE,GACEF,EAAiB,MAAMG,EACvB,GAAG1B,SACHA,EAAS,MAAM,EAAG,EAAe,EACjCH,EACAC,EACAoB,CACD,EAGA,OAAOK,EACD,GACLA,EAAiB,MAAMG,EACvB,GAAG1B,EAAS,MAAM,EAAG,EAAgB,SACrCA,EAAS,MAAM,EAAG,EAAe,EACjCH,EACAC,EACAoB,CACD,EAGA,OAAOK,UAGCvB,EAAS,SAAS,aAAa,EAAG,CAC5C,GACEuB,EAAiB,MAAMG,EACvB1B,EACAA,EAAS,MAAM,EAAG,GAAoB,EACtCH,EACAC,EACAoB,CACD,EAGA,OAAOK,EACD,GACLA,EAAiB,MAAMG,EACvB,GAAG1B,EAAS,MAAM,EAAG,GAAqB,SAC1CA,EAAS,MAAM,EAAG,GAAoB,EACtCH,EACAC,EACAoB,CACD,EAGA,OAAOK,UAEEvB,EAAS,SAAS,GAAG,EAAG,CAClC,GAAKwB,EAAa,MAAM1B,EAAO,GAAGE,aAAoB,EAErD,MAAO,CAAE,MAAO,CAAE,KAAMwB,EAAY,OAAQ,GAAI,EAAG,SAAU,IAAK,EAC5D,GACLA,EAAa,MAAM1B,EAAO,GAAGE,EAAS,MAAM,EAAG,EAAW,QAAQ,EAGnE,MAAO,CAAE,MAAO,CAAE,KAAMwB,EAAY,OAAQ,GAAI,EAAG,SAAU,IAAK,UAEzDxB,EAAS,SAAS,OAAO,EAAG,CACtC,GACEuB,EAAiB,MAAMG,EACvB1B,EACA,GAAGA,EAAS,MAAM,EAAG,EAAe,KACpCH,EACAC,EACAoB,CACD,EAGA,OAAOK,EACD,GAAIE,EAEV,MAAO,CAAE,MAAO,CAAE,KAAMA,EAAW,OAAQ,GAAI,EAAG,SAAU,IAAK,EAC3D,GACLF,EAAiB,MAAMG,EACvB,GAAG1B,EAAS,MAAM,EAAG,EAAe,eACpC,GAAGA,EAAS,MAAM,EAAG,EAAe,KACpCH,EACAC,EACAoB,CACD,EAGA,OAAOK,EAIT,OAAIE,EAEI,CAAE,MAAO,CAAE,KAAMA,EAAW,OAAQ,GAAI,EAAG,SAAU,IAAK,GAEhEF,EAAiB,MAAMG,EACvB,GAAG1B,SACH,GAAGA,KACHH,EACAC,EACAoB,CACD,KAKCK,EAAiB,MAAMG,EACvB,GAAG1B,eACH,GAAGA,KACHH,EACAC,EACAoB,CACD,GAROK,EAcDI,GAAS3B,EAAUH,EAAeC,CAAM,CAChD,EAEMuB,GAAgC,MACrCrB,EACAH,EACAC,EACAoB,IACqB,CACrB,IAAIK,EAAyB,KACzBC,EAA4B,KAC1BC,EAAY,MAAM3B,EAAOE,CAAQ,EACvC,GAAIA,EAAS,SAAS,QAAQ,EAAG,CAChC,GAAIyB,EAEH,MAAO,CAAE,MAAO,CAAE,KAAMA,EAAW,OAAQ,GAAI,EAAG,SAAU,IAAK,EAEjE,GAAIzB,IAAa,UAChB,GACEuB,EAAiB,MAAMG,EACvB,cACA,IACA7B,EACAC,EACAoB,CACD,EAEA,OAAOK,MAEF,IACLA,EAAiB,MAAMG,EACvB,GAAG1B,EAAS,MAAM,EAAG,EAAgB,SACrCA,EAAS,MAAM,EAAG,EAAgB,EAClCH,EACAC,EACAoB,CACD,EAGA,OAAOK,EACD,GACLA,EAAiB,MAAMG,EACvB,GAAG1B,SACHA,EAAS,MAAM,EAAG,EAAgB,EAClCH,EACAC,EACAoB,CACD,EAGA,OAAOK,WAGCvB,EAAS,SAAS,aAAa,EAEzC,GAAIA,IAAa,eAChB,GACEuB,EAAiB,MAAMG,EACvB,cACA,IACA7B,EACAC,EACAoB,CACD,EAEA,OAAOK,MAEF,IACLA,EAAiB,MAAMG,EACvB1B,EACAA,EAAS,MAAM,EAAG,GAAqB,EACvCH,EACAC,EACAoB,CACD,EAGA,OAAOK,EACD,GAAIE,EAEV,MAAO,CAAE,MAAO,CAAE,KAAMA,EAAW,OAAQ,GAAI,EAAG,SAAU,IAAK,EAC3D,GACLF,EAAiB,MAAMG,EACvB,GAAG1B,EAAS,MAAM,EAAG,GAAqB,SAC1CA,EAAS,MAAM,EAAG,GAAqB,EACvCH,EACAC,EACAoB,CACD,EAGA,OAAOK,UAEEvB,EAAS,SAAS,GAAG,EAC/B,GAAIA,IAAa,KAChB,GAAKwB,EAAa,MAAM1B,EAAO,aAAa,EAE3C,MAAO,CAAE,MAAO,CAAE,KAAM0B,EAAY,OAAQ,GAAI,EAAG,SAAU,IAAK,MAE7D,IACLD,EAAiB,MAAMG,EACvB,GAAG1B,EAAS,MAAM,EAAG,EAAW,SAChCA,EAAS,MAAM,EAAG,EAAW,EAC7BH,EACAC,EACAoB,CACD,EAGA,OAAOK,EACD,GACLA,EAAiB,MAAMG,EACvB,GAAG1B,EAAS,MAAM,EAAG,EAAW,eAChCA,EAAS,MAAM,EAAG,EAAW,EAC7BH,EACAC,EACAoB,CACD,EAGA,OAAOK,UAEEvB,EAAS,SAAS,OAAO,EAAG,CACtC,GACEuB,EAAiB,MAAMG,EACvB1B,EACAA,EAAS,MAAM,EAAG,EAAe,EACjCH,EACAC,EACAoB,CACD,EAGA,OAAOK,EACD,GACLA,EAAiB,MAAMG,EACvB,GAAG1B,EAAS,MAAM,EAAG,EAAe,eACpCA,EAAS,MAAM,EAAG,EAAe,EACjCH,EACAC,EACAoB,CACD,EAGA,OAAOK,EAIT,OAAIE,EAEI,CAAE,MAAO,CAAE,KAAMA,EAAW,OAAQ,GAAI,EAAG,SAAU,IAAK,GACtDD,EAAa,MAAM1B,EAAO,GAAGE,QAAe,GAEhD,CAAE,MAAO,CAAE,KAAMwB,EAAY,OAAQ,GAAI,EAAG,SAAU,IAAK,GACvDA,EAAa,MAAM1B,EAAO,GAAGE,cAAqB,GAEtD,CAAE,MAAO,CAAE,KAAMwB,EAAY,OAAQ,GAAI,EAAG,SAAU,IAAK,EAG5DG,GAAS3B,EAAUH,EAAeC,CAAM,CAChD,EAEMwB,GAAmB,MACxBtB,EACAH,EACAC,IACqB,CACrB,IAAM2B,EAAY,MAAM3B,EAAOE,CAAQ,EACvC,OAAIyB,EACI,CAAE,MAAO,CAAE,KAAMA,EAAW,OAAQ,GAAI,EAAG,SAAU,IAAK,EAE1DE,GAAS3B,EAAUH,EAAeC,CAAM,CAEjD,EAEM6B,GAAW,MAChB3B,EACAH,EACAC,IACqB,CACrB,OAAQD,EAAc,mBAAoB,CACzC,IAAK,0BAA2B,CAC/B,IAAM+B,EAAO,MAAM9B,EAAO,aAAa,EACvC,OAAI8B,EACI,CAAE,MAAO,CAAE,KAAAA,EAAM,OAAQ,GAAI,EAAG,SAAU,IAAK,EAEhD,IACR,CACA,IAAK,WAAY,CAChB,IAAIC,EAAM7B,EACV,KAAO6B,GAAK,CACXA,EAAMA,EAAI,MAAM,EAAGA,EAAI,YAAY,GAAG,CAAC,EACvC,IAAMD,EAAO,MAAM9B,EAAO,GAAG+B,YAAc,EAC3C,GAAID,EACH,MAAO,CAAE,MAAO,CAAE,KAAAA,EAAM,OAAQ,GAAI,EAAG,SAAU,IAAK,EAGxD,OAAO,IACR,CACA,IAAK,OACL,QACC,OAAO,IAET,CACD,EAEMF,EAAe,MACpBI,EACAC,EACAlC,EACAC,EACAkC,IACqB,CACrB,GAAIA,EACH,OAAO,KAGR,GAAI,CAAE,MAAMlC,EAAOiC,CAAW,EAAI,CACjC,IAAM7B,EAAS,MAAMC,GAAU4B,EAAalC,EAAeC,EAAQ,EAAI,EACvE,GAAII,GAAQ,OAASA,EAAO,MAAM,OAAU,MAAMJ,EAAOgC,CAAI,EAC5D,MAAO,CACN,MAAO,KACP,SAAUC,CACX,EAIF,OAAO,IACR,ECtkBA,eAAsBE,GACrBC,EACAC,EACAC,EAAU,EACT,CACD,IAAIC,EAAW,EAEf,KAAOA,GAAYD,GAClB,GAAI,CACH,OAAO,MAAMF,EAAkB,gBAA+BC,EAAU,CACvE,KAAM,SACN,SAAU,OACX,CAAC,CACF,MAAE,CACD,GAAIE,GAAYD,EACf,MAAM,IAAI,MACT,mBAAmBD,2CACpB,EAID,MAAM,IAAI,QAASG,GAClB,WAAWA,EAAgB,KAAK,IAAI,EAAGD,GAAU,EAAI,GAAI,CAC1D,CACD,CAEF,CzDcA,IAAOE,GAAP,cAA6BC,EAAsB,CAClD,MAAM,MAAMC,EAAqC,CAChD,IAAIC,EACJ,GAAI,CACH,OAAAA,EAASC,GACRF,EACA,KAAK,IACL,KAAK,IAAI,WACT,KAAK,IAAI,wBACT,KAAK,IAAI,2BACV,EAEOG,GACNH,EACAI,GAA2B,KAAK,IAAI,MAAM,EAC1C,KAAK,gBAAgB,KAAK,IAAI,EAC9B,KAAK,mBAAmB,KAAK,IAAI,CAClC,CACD,OAASC,EAAP,CACD,IAAMC,EAAW,IAAIC,GAA4BF,CAAY,EAG7D,OAAIJ,GACHA,EAAO,iBAAiBI,CAAG,EAGrBC,CACR,CACD,CAEA,MAAM,kBAAkBN,EAA+C,CACtE,IAAMQ,EAAM,IAAI,IAAIR,EAAQ,GAAG,EACzBS,EAAST,EAAQ,OAAO,YAAY,EACpCU,EAAS,MAAMC,GACpBH,EAAI,SACJ,CACC,GAAGJ,GAA2B,KAAK,IAAI,MAAM,EAC7C,mBAAoB,MACrB,EACA,KAAK,gBAAgB,KAAK,IAAI,CAC/B,EAEA,OAAIM,GAAU,CAAC,MAAO,MAAM,EAAE,SAASD,CAAM,EACrC,IAAIG,EAERF,IAAW,IAIhB,CAEA,MAAM,mBACLG,EACmE,CACnE,IAAMC,EAAQ,MAAMC,GACnB,KAAK,IAAI,oBACTF,CACD,EAEA,GAAI,CAACC,GAAS,CAACA,EAAM,MACpB,MAAM,IAAI,MACT,mBAAmBD,6DACpB,EAGD,MAAO,CACN,eAAgBC,EAAM,MACtB,YAAaA,EAAM,UAAU,aAAe,0BAC7C,CACD,CAEA,MAAM,uBACLE,EAC0E,CAC1E,IAAMH,EAAO,MAAM,KAAK,gBAAgBG,CAAQ,EAChD,OAAKH,EAIE,KAAK,mBAAmBA,CAAI,EAH3B,IAIT,CAEA,MAAM,gBAAgBG,EAA0C,CAE/D,OAAO,MADgB,IAAIC,GAAe,KAAK,IAAI,eAAe,EACtC,IAAID,CAAQ,CACzC,CACD", "names": ["WorkerEntrypoint", "objectToString", "isError", "wat", "isInstanceOf", "isBuiltin", "className", "isString", "wat", "isBuiltin", "isPrimitive", "isPlainObject", "isEvent", "isInstanceOf", "isElement", "isThenable", "wat", "isSyntheticEvent", "isPlainObject", "isNaN", "isInstanceOf", "base", "isVueViewModel", "truncate", "str", "max", "isGlobalObj", "obj", "GLOBAL_OBJ", "getGlobalObject", "getGlobalSingleton", "name", "creator", "gbl", "__SENTRY__", "WINDOW", "getGlobalObject", "DEFAULT_MAX_STRING_LENGTH", "htmlTreeAsString", "elem", "options", "currentElem", "MAX_TRAVERSE_HEIGHT", "out", "height", "len", "separator", "sep<PERSON><PERSON>th", "nextStr", "keyAttrs", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "_htmlElementAsString", "el", "className", "classes", "key", "attr", "i", "keyAttrPairs", "keyAttr", "keyAttrPair", "isString", "allowedAttrs", "PREFIX", "CONSOLE_LEVELS", "originalConsoleMethods", "consoleSandbox", "callback", "GLOBAL_OBJ", "console", "wrappedFuncs", "wrappedLevels", "level", "originalConsoleMethod", "<PERSON><PERSON>ogger", "enabled", "logger", "name", "args", "DSN_REGEX", "isValidProtocol", "protocol", "dsnToString", "dsn", "with<PERSON><PERSON><PERSON>", "host", "path", "pass", "port", "projectId", "public<PERSON>ey", "dsnFromString", "str", "match", "last<PERSON><PERSON>", "split", "projectMatch", "dsnFromComponents", "components", "validateDsn", "component", "logger", "makeDsn", "from", "SentryError", "message", "logLevel", "fill", "source", "name", "replacementFactory", "original", "wrapped", "markFunctionWrapped", "addNonEnumerableProperty", "obj", "value", "logger", "proto", "urlEncode", "object", "key", "convertToPlainObject", "value", "isError", "getOwnProperties", "isEvent", "newObj", "serializeEventTarget", "isInstanceOf", "target", "isElement", "htmlTreeAsString", "obj", "extractedProps", "property", "extractExceptionKeysForMessage", "exception", "max<PERSON><PERSON><PERSON>", "keys", "truncate", "<PERSON><PERSON><PERSON><PERSON>", "serialized", "dropUndefinedKeys", "inputValue", "_dropUndefinedKeys", "memoizationMap", "isPlainObject", "memoVal", "returnValue", "item", "filenameIsInApp", "filename", "isNative", "node", "getModule", "FILENAME_MATCH", "FULL_MATCH", "line", "lineMatch", "object", "method", "functionName", "typeName", "methodName", "methodStart", "objectEnd", "STACKTRACE_FRAME_LIMIT", "WEBPACK_ERROR_REGEXP", "STRIP_FRAME_REGEXP", "createStackParser", "parsers", "sortedParsers", "a", "b", "p", "stack", "<PERSON><PERSON><PERSON><PERSON>", "frames", "lines", "i", "line", "cleanedLine", "parser", "frame", "stripSentryFramesAndReverse", "stackParserFromStackParserOptions", "stack<PERSON>arser", "localStack", "defaultFunctionName", "getFunctionName", "fn", "nodeStackLineParser", "getModule", "node", "WINDOW", "getGlobalObject", "supportsFetch", "WINDOW", "isNativeFetch", "func", "supportsNativeFetch", "result", "doc", "sandbox", "err", "logger", "WINDOW", "getGlobalObject", "supportsHistory", "chrome", "isChromePackagedApp", "has<PERSON><PERSON>ory<PERSON><PERSON>", "WINDOW", "getGlobalObject", "SENTRY_XHR_DATA_KEY", "handlers", "instrumented", "instrument", "type", "instrumentConsole", "instrumentDOM", "instrumentXHR", "instrumentFetch", "instrumentHistory", "instrumentError", "instrumentUnhandledRejection", "logger", "addInstrumentationHandler", "callback", "triggerHandlers", "type", "data", "handlers", "handler", "e", "logger", "getFunctionName", "instrumentConsole", "GLOBAL_OBJ", "CONSOLE_LEVELS", "level", "fill", "originalConsoleMethod", "originalConsoleMethods", "args", "log", "instrumentFetch", "supportsNativeFetch", "originalFetch", "method", "url", "parseFetchArgs", "handlerData", "response", "error", "hasProp", "obj", "prop", "getUrlFromResource", "resource", "fetch<PERSON>rgs", "options", "arg", "instrumentXHR", "WINDOW", "xhrproto", "originalOpen", "startTimestamp", "xhrInfo", "SENTRY_XHR_DATA_KEY", "isString", "onreadystatechangeHandler", "original", "readyStateArgs", "setRequestHeaderArgs", "header", "value", "originalSend", "sentryXhrData", "lastHref", "instrumentHistory", "supportsHistory", "oldOnPopState", "to", "from", "historyReplacementFunction", "originalHistoryFunction", "DEBOUNCE_DURATION", "debounceTimerID", "lastCapturedEvent", "areSimilarDomEvents", "a", "b", "shouldSkipDOMEvent", "event", "target", "makeDOMEventHandler", "globalListener", "addNonEnumerableProperty", "name", "instrumentDOM", "triggerDOMHandler", "globalDOMEventHandler", "proto", "originalAddEventListener", "listener", "el", "handlerForType", "originalRemoveEventListener", "_old<PERSON>n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "instrumentError", "msg", "line", "column", "_oldOnUnhandledRejectionHandler", "instrumentUnhandledRejection", "isBrowserBundle", "isNodeEnv", "isBrowserBundle", "dynamicRequire", "mod", "request", "memoBuilder", "hasWeakSet", "inner", "memoize", "obj", "unmemoize", "uuid4", "gbl", "GLOBAL_OBJ", "crypto", "getRandomByte", "c", "getFirstException", "event", "addExceptionTypeValue", "event", "value", "type", "exception", "values", "firstException", "addExceptionMechanism", "newMechanism", "getFirstException", "defaultMechanism", "currentMechanism", "mergedData", "checkOrSetAlreadyCaught", "exception", "addNonEnumerableProperty", "arrayify", "maybeA<PERSON>y", "normalize", "input", "depth", "maxProperties", "visit", "err", "normalizeToSize", "object", "maxSize", "normalized", "jsonSize", "key", "value", "memo", "memoBuilder", "memoize", "unmemoize", "isNaN", "stringified", "stringifyValue", "remainingDepth", "valueWithToJSON", "jsonValue", "numAdded", "visitable", "convertToPlainObject", "<PERSON><PERSON><PERSON>", "visitValue", "isVueViewModel", "isSyntheticEvent", "getFunctionName", "objName", "getConstructorName", "prototype", "utf8Length", "splitPathRe", "splitPath", "filename", "truncated", "parts", "basename", "path", "ext", "f", "splitPath", "States", "RESOLVED", "REJECTED", "resolvedSyncPromise", "value", "SyncPromise", "resolve", "rejectedSyncPromise", "reason", "_", "reject", "executor", "e", "onfulfilled", "onrejected", "result", "val", "onfinally", "isRejected", "state", "isThenable", "cachedHandlers", "handler", "makePromiseBuffer", "limit", "buffer", "isReady", "remove", "task", "add", "taskProducer", "rejectedSyncPromise", "SentryError", "drain", "timeout", "SyncPromise", "resolve", "reject", "counter", "capturedSetTimeout", "item", "resolvedSyncPromise", "WINDOW", "getGlobalObject", "dateTimestampSource", "getBrowserPerformance", "performance", "<PERSON><PERSON><PERSON><PERSON>", "getNodePerformance", "dynamicRequire", "platformPerformance", "isNodeEnv", "timestampSource", "dateTimestampInSeconds", "timestampInSeconds", "_browserPerformanceTimeOriginMode", "browserPerformanceTimeOrigin", "performance", "WINDOW", "threshold", "performanceNow", "dateNow", "time<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "timeOriginIsReliable", "navigationStart", "navigationStartDelta", "navigationStartIsReliable", "TRACEPARENT_REGEXP", "generateSentryTraceHeader", "traceId", "uuid4", "spanId", "sampled", "sampledString", "createEnvelope", "headers", "items", "addItemToEnvelope", "envelope", "newItem", "forEachEnvelopeItem", "callback", "envelopeItems", "envelopeItem", "envelopeItemType", "encodeUTF8", "input", "textEncoder", "serializeEnvelope", "envelope", "envHeaders", "items", "parts", "append", "next", "item", "itemHeaders", "payload", "stringifiedPayload", "normalize", "concatBuffers", "buffers", "totalLength", "acc", "buf", "merged", "offset", "buffer", "createAttachmentEnvelopeItem", "attachment", "textEncoder", "buffer", "encodeUTF8", "dropUndefinedKeys", "ITEM_TYPE_TO_DATA_CATEGORY_MAP", "envelopeItemTypeToDataCategory", "type", "getSdkMetadataForEnvelopeHeader", "metadataOrEvent", "name", "version", "createEventEnvelopeHeaders", "event", "sdkInfo", "tunnel", "dsn", "dynamicSamplingContext", "dsnToString", "parseRetryAfterHeader", "header", "now", "headerDelay", "headerDate", "disabledUntil", "limits", "category", "isRateLimited", "updateRateLimits", "statusCode", "headers", "updatedRateLimits", "rateLimitHeader", "retryAfterHeader", "limit", "retryAfter", "categories", "delay", "parseStackFrames", "stack<PERSON>arser", "error", "exceptionFromError", "exception", "frames", "getMessageForObject", "message", "extractExceptionKeysForMessage", "eventFromUnknownInput", "getCurrentHub", "hint", "ex", "mechanism", "isError", "isPlainObject", "hub", "client", "normalizeDepth", "scope", "normalizeToSize", "event", "addExceptionTypeValue", "addExceptionMechanism", "eventFromMessage", "level", "attachStacktrace", "DEFAULT_ENVIRONMENT", "getGlobalEventProcessors", "getGlobalSingleton", "addGlobalEventProcessor", "callback", "notifyEventProcessors", "processors", "event", "hint", "index", "SyncPromise", "resolve", "reject", "processor", "result", "logger", "isThenable", "final", "makeSession", "context", "startingTime", "timestampInSeconds", "session", "uuid4", "sessionToJSON", "updateSession", "duration", "closeSession", "status", "dropUndefinedKeys", "DEFAULT_MAX_BREADCRUMBS", "<PERSON><PERSON>", "generatePropagationContext", "scope", "newScope", "callback", "user", "updateSession", "requestSession", "tags", "key", "value", "extras", "extra", "fingerprint", "level", "name", "context", "span", "session", "captureContext", "updatedScope", "isPlainObject", "breadcrumb", "maxBreadcrumbs", "maxCrumbs", "mergedBreadcrumb", "dateTimestampInSeconds", "breadcrumbs", "attachment", "event", "hint", "additionalEventProcessors", "transaction", "transactionName", "scopeBreadcrumbs", "notifyEventProcessors", "getGlobalEventProcessors", "newData", "arrayify", "uuid4", "API_VERSION", "DEFAULT_BREADCRUMBS", "<PERSON><PERSON>", "client", "scope", "<PERSON><PERSON>", "_version", "version", "top", "callback", "exception", "hint", "eventId", "uuid4", "syntheticException", "message", "level", "event", "breadcrumb", "beforeBreadcrumb", "maxBreadcrumbs", "mergedBreadcrumb", "dateTimestampInSeconds", "finalBreadcrumb", "consoleSandbox", "user", "tags", "extras", "key", "value", "extra", "name", "context", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "integration", "logger", "customSamplingContext", "result", "endSession", "session", "closeSession", "release", "environment", "DEFAULT_ENVIRONMENT", "userAgent", "GLOBAL_OBJ", "makeSession", "currentSession", "updateSession", "options", "method", "args", "sentry", "getMainCarrier", "hub", "registry", "getHubFromCarrier", "setHubOnCarrier", "getCurrentHub", "getGlobalHub", "hasHubOnCarrier", "hasHubOnCarrier", "carrier", "getHubFromCarrier", "getGlobalSingleton", "<PERSON><PERSON>", "setHubOnCarrier", "hub", "__SENTRY__", "getActiveTransaction", "maybeHub", "getCurrentHub", "errorsInstrumented", "registerErrorInstrumentation", "addInstrumentationHandler", "<PERSON><PERSON><PERSON><PERSON>", "activeTransaction", "getActiveTransaction", "status", "logger", "SpanRecorder", "maxlen", "span", "Span", "spanContext", "uuid4", "timestampInSeconds", "name", "childSpan", "opStr", "nameStr", "idStr", "logMessage", "logger", "key", "value", "httpStatus", "spanStatus", "spanStatusfromHttpCode", "endTimestamp", "generateSentryTraceHeader", "dropUndefinedKeys", "getDynamicSamplingContextFromClient", "trace_id", "client", "scope", "options", "public_key", "user_segment", "dsc", "dropUndefinedKeys", "DEFAULT_ENVIRONMENT", "Transaction", "SpanClass", "transactionContext", "hub", "getCurrentHub", "incomingDynamicSamplingContext", "newName", "name", "source", "maxlen", "SpanRecorder", "key", "context", "value", "unit", "newMetadata", "endTimestamp", "transaction", "spanContext", "dropUndefinedKeys", "client", "scope", "dsc", "getDynamicSamplingContextFromClient", "maybeSampleRate", "logger", "finishedSpans", "s", "prev", "current", "metadata", "hasTracingEnabled", "maybeOptions", "client", "getCurrentHub", "options", "sampleTransaction", "transaction", "options", "samplingContext", "hasTracingEnabled", "sampleRate", "isValidSampleRate", "logger", "rate", "isNaN", "traceHeaders", "span", "_startTransaction", "transactionContext", "customSamplingContext", "client", "options", "configInstrumenter", "transactionInstrumenter", "logger", "transaction", "Transaction", "sampleTransaction", "addTracingExtensions", "carrier", "getMainCarrier", "_startTransaction", "traceHeaders", "registerErrorInstrumentation", "<PERSON><PERSON><PERSON><PERSON>", "client", "attrs", "sessionAggregates", "aggregates", "key", "dropUndefinedKeys", "scope", "getCurrentHub", "requestSession", "status", "date", "sessionStartedTrunc", "aggregationCounts", "SENTRY_API_VERSION", "getBaseApiEndpoint", "dsn", "protocol", "port", "_getIngestEndpoint", "_encodedAuth", "sdkInfo", "urlEncode", "getEnvelopeEndpointWithUrlEncodedAuth", "tunnelOrOptions", "tunnel", "enhanceEventWithSdkInfo", "event", "sdkInfo", "createSessionEnvelope", "session", "dsn", "metadata", "tunnel", "getSdkMetadataForEnvelopeHeader", "envelopeHeaders", "dsnToString", "envelopeItem", "createEnvelope", "createEventEnvelope", "eventType", "createEventEnvelopeHeaders", "installedIntegrations", "filterDuplicates", "integrations", "integrationsByName", "currentInstance", "name", "existingInstance", "k", "getIntegrationsToSetup", "options", "defaultIntegrations", "userIntegrations", "integration", "arrayify", "finalIntegrations", "debugIndex", "findIndex", "debugInstance", "setupIntegrations", "client", "integrationIndex", "setupIntegration", "addGlobalEventProcessor", "getCurrentHub", "callback", "event", "hint", "processor", "logger", "findIndex", "arr", "callback", "i", "prepareEvent", "options", "event", "hint", "scope", "client", "normalizeDepth", "normalizeMaxBreadth", "prepared", "uuid4", "dateTimestampInSeconds", "integrations", "i", "applyClientOptions", "applyIntegrationsMetadata", "applyDebugIds", "finalScope", "<PERSON><PERSON>", "result", "resolvedSyncPromise", "clientEventProcessors", "attachments", "notifyEventProcessors", "getGlobalEventProcessors", "evt", "applyDebugMeta", "normalizeEvent", "environment", "release", "dist", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DEFAULT_ENVIRONMENT", "truncate", "exception", "request", "debugIdStackParserCache", "stack<PERSON>arser", "debugIdMap", "GLOBAL_OBJ", "debugIdStackFramesCache", "cachedDebugIdStackFrameCache", "filenameDebugIdMap", "acc", "debugIdStackTrace", "parsedStack", "cachedParsedStack", "stackFrame", "frame", "images", "filename", "integrationNames", "depth", "max<PERSON><PERSON><PERSON>", "normalized", "b", "normalize", "span", "ALREADY_SEEN_ERROR", "BaseClient", "options", "makeDsn", "logger", "url", "getEnvelopeEndpointWithUrlEncodedAuth", "exception", "hint", "scope", "checkOrSetAlreadyCaught", "eventId", "event", "result", "message", "level", "promisedEvent", "isPrimitive", "session", "updateSession", "timeout", "transport", "clientFinished", "transportFlushed", "resolvedSyncPromise", "eventProcessor", "forceInitialize", "setupIntegrations", "integrationId", "integration", "setupIntegration", "env", "createEventEnvelope", "attachment", "addItemToEnvelope", "createAttachmentEnvelopeItem", "promise", "sendResponse", "createSessionEnvelope", "reason", "category", "_event", "key", "hook", "callback", "rest", "crashed", "errored", "exceptions", "ex", "mechanism", "sessionNonTerminal", "SyncPromise", "resolve", "ticked", "tick", "interval", "integrations", "prepareEvent", "evt", "propagationContext", "trace_id", "spanId", "parentSpanId", "dsc", "dynamicSamplingContext", "getDynamicSamplingContextFromClient", "finalEvent", "sentryError", "sampleRate", "isTransaction", "isTransactionEvent", "isError", "isErrorEvent", "eventType", "beforeSendLabel", "rejectedSyncPromise", "SentryError", "dataCategory", "prepared", "processBeforeSend", "_validateBeforeSendResult", "processedEvent", "transactionInfo", "source", "value", "envelope", "outcomes", "beforeSendResult", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isThenable", "isPlainObject", "e", "beforeSend", "beforeSendTransaction", "createCheckInEnvelope", "checkIn", "dynamicSamplingContext", "metadata", "tunnel", "dsn", "headers", "dsnToString", "dropUndefinedKeys", "item", "createCheckInEnvelopeItem", "createEnvelope", "ServerRuntimeClient", "BaseClient", "options", "addTracingExtensions", "exception", "hint", "resolvedSyncPromise", "eventFromUnknownInput", "getCurrentHub", "message", "level", "eventFromMessage", "scope", "requestSession", "event", "timeout", "release", "environment", "<PERSON><PERSON><PERSON><PERSON>", "logger", "checkIn", "monitorConfig", "id", "uuid4", "tunnel", "serializedCheckIn", "dynamicSamplingContext", "traceContext", "envelope", "createCheckInEnvelope", "span", "traceId", "spanId", "parentSpanId", "dsc", "getDynamicSamplingContextFromClient", "DEFAULT_TRANSPORT_BUFFER_SIZE", "createTransport", "options", "makeRequest", "buffer", "makePromiseBuffer", "rateLimits", "flush", "timeout", "send", "envelope", "filteredEnvelopeItems", "forEachEnvelopeItem", "item", "type", "envelopeItemDataCategory", "envelopeItemTypeToDataCategory", "isRateLimited", "event", "getEventForEnvelopeItem", "resolvedSyncPromise", "filteredEnvelope", "createEnvelope", "recordEnvelopeLoss", "reason", "requestTask", "serializeEnvelope", "response", "logger", "updateRateLimits", "error", "result", "SentryError", "isObject", "value", "isMechanism", "containsMechanism", "getSentryRelease", "GLOBAL_OBJ", "setOnOptional", "target", "entry", "parseStackFrames", "stack<PERSON>arser", "error", "extractMessage", "ex", "message", "exceptionFromError", "exception", "frames", "eventFromUnknownInput", "sdk", "hint", "mechanism", "isError", "isPlainObject", "extractExceptionKeysForMessage", "client", "normalizeDepth", "scope", "normalizeToSize", "event", "addExceptionTypeValue", "addExceptionMechanism", "eventFromMessage", "level", "attachStacktrace", "DEFAULT_LIMIT", "_LinkedErrors", "options", "addGlobalEventProcessor", "getCurrentHub", "self", "handler", "LinkedErrors", "__publicField", "parser", "limit", "isInstanceOf", "linkedErrors", "walkError<PERSON>ree", "stack", "defaultRequestDataOptions", "_options", "_RequestData", "__privateAdd", "__privateSet", "sdkProcessingMetadata", "toEventRequest", "__privateGet", "toEventUser", "RequestData", "user", "request", "ip_address", "allowedIps", "newUser", "testAllowlist", "cookieString", "cookies", "parse<PERSON><PERSON><PERSON>", "headers", "k", "v", "eventRequest", "url", "qi", "allowedHeaders", "allowedCookies", "allowedSearchParams", "applyAllowlistToObject", "params", "allowedParams", "<PERSON><PERSON><PERSON>", "allowlist", "item", "predicate", "allowlistLowercased", "allowed", "key", "part", "acc", "<PERSON><PERSON><PERSON>", "cookieValue", "setupIntegrations", "integrations", "integrationIndex", "integration", "callback", "ToucanClient", "ServerRuntimeClient", "#sdk", "resolvedSyncPromise", "body", "enabled", "workersStackLineParser", "getModule", "arg1", "arg2", "nodeStackLineParser", "line", "result", "filename", "basename", "defaultStackParser", "createStackParser", "makeFetchTransport", "makeRequest", "response", "e", "rejectedSyncPromise", "createTransport", "Toucan", "<PERSON><PERSON>", "detectedRelease", "getIntegrationsToSetup", "stackParserFromStackParserOptions", "checkIn", "monitorConfig", "setupSentry", "request", "context", "dsn", "clientId", "clientSecret", "sentry", "Toucan", "colo", "userAgent", "AssetsManifest", "data", "pathname", "pathHash", "hash<PERSON><PERSON>", "entry", "binarySearch", "contentHashToKey", "path", "hash<PERSON><PERSON><PERSON>", "arr", "searchValue", "offset", "current", "cmp", "compare", "nextOffset", "<PERSON><PERSON><PERSON><PERSON>", "a", "b", "i", "v", "buffer", "applyConfigurationDefaults", "configuration", "OkResponse", "body", "init", "NotFoundResponse", "MethodNotAllowedResponse", "InternalServerErrorResponse", "err", "NotModifiedResponse", "_body", "TemporaryRedirectResponse", "location", "CACHE_CONTROL_BROWSER", "getHeaders", "eTag", "contentType", "request", "headers", "isCacheable", "CACHE_CONTROL_BROWSER", "handleRequest", "request", "configuration", "exists", "getByETag", "pathname", "search", "intent", "getIntent", "NotFoundResponse", "method", "MethodNotAllowedResponse", "TemporaryRedirectResponse", "InternalServerErrorResponse", "asset", "headers", "getHeaders", "strongETag", "weakETag", "ifNoneMatch", "NotModifiedResponse", "body", "OkResponse", "skipRedirects", "htmlHandlingAutoTrailingSlash", "htmlHandlingForceTrailingSlash", "htmlHandlingDropTrailingSlash", "htmlHandlingNone", "redirectResult", "eTagResult", "exactETag", "safeRedirect", "notFound", "eTag", "cwd", "file", "destination", "skip", "getAssetWithMetadataFromKV", "assetsKVNamespace", "assetKey", "retries", "attempts", "resolvePromise", "src_default", "WorkerEntrypoint", "request", "sentry", "setupSentry", "handleRequest", "applyConfigurationDefaults", "err", "response", "InternalServerErrorResponse", "url", "method", "intent", "getIntent", "MethodNotAllowedResponse", "eTag", "asset", "getAssetWithMetadataFromKV", "pathname", "AssetsManifest"]}