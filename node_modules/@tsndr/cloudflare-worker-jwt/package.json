{"name": "@tsndr/cloudflare-worker-jwt", "version": "2.5.3", "description": "A lightweight JWT implementation with ZERO dependencies for Cloudflare Worker", "type": "module", "exports": "./index.js", "types": "index.d.ts", "engine": {"node": ">=18"}, "scripts": {"build": "tsc & esbuild --bundle --target=esnext --platform=neutral --outfile=index.js src/index.ts & wait", "test": "vitest"}, "repository": {"type": "git", "url": "git+https://github.com/tsndr/cloudflare-worker-jwt.git"}, "keywords": ["jwt", "token", "cloudflare", "worker", "cloudflare-worker"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/tsndr/cloudflare-worker-jwt/issues"}, "homepage": "https://github.com/tsndr/cloudflare-worker-jwt#readme", "devDependencies": {"@cloudflare/workers-types": "^4.20240208.0", "@edge-runtime/vm": "^3.2.0", "typescript": "^5.3.3", "vitest": "^1.3.1"}}