# @agilesoft/js_ags_authrest2

Login npm agilesoft username&password in onepassword

## Installation

Use the package manager [NPM](https://www.npmjs.com/package/@agilesoft/type_ags_authrest2) to install foobar.

```bash
npm i @agilesoft/type_ags_authrest2
```

## Usage

```javascript
import ags_restauth from "@agilesoft/type_ags_authrest2";

var ENV_DEPLOY = ENVIRONMENT || "dev";
if (ENV_DEPLOY === "production") {
  var Auth = new ags_restauth(R_TOKEN, R_USER, R_PASS, R_PATH);
}

# Exlample 'Middleware'
    var data;
    if (ENV_DEPLOY === "production") {
      // production-specific code
      console.log("production");
      data = await Auth.Middleware(request);
    } else {
      // staging-specific code
      console.log("staging");
      try {
        data = await request.json();
      } catch (error) {
        data = await request.text();
      }
    }

# Exlample 'Encrept Headers,BodyData'
var request = require('request')
(async () => {
  var options = {
    method: 'POST',
    url: 'http://127.0.0.1:8787/encrypt',
    headers: {
      Authorization: await Auth.genTokenEncryp(),
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      encrypData: await Auth.encrypbody({ data: 'test' }),
    }),
  }
  request(options, function(error, response) {
    if (error) throw new Error(error)
    console.log(response.body)
  })
})()

```

## Contributing
Pull requests are welcome. For major changes, please open an issue first to discuss what you would like to change.

Please make sure to update tests as appropriate.

## License
[MIT](https://choosealicense.com/licenses/mit/)# -agilesoft-type_ags_authrest2
