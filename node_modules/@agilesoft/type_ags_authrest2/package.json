{"name": "@agilesoft/type_ags_authrest2", "version": "1.1.8", "description": "setup_babel", "main": "index.js", "scripts": {"start": "node server.js", "test": "nodemon testx.js"}, "author": "", "license": "ISC", "devDependencies": {"babel-core": "^6.26.3", "babel-loader": "^8.2.3", "babel-plugin-transform-runtime": "^6.23.0", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.5.2", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-0": "^6.24.1", "babel-register": "^6.26.0"}, "babel": {"presets": ["env"]}, "dependencies": {"@tsndr/cloudflare-worker-jwt": "^2.1.2", "crypto-js": "^4.1.1", "date-and-time": "^2.2.1", "is-base64": "^1.1.0", "uuid": "^9.0.0"}}