// "use strict";
import jwt from "@tsndr/cloudflare-worker-jwt";
const date = require("date-and-time");
var CryptoJS = require("crypto-js");
const { v4: uuidv4 } = require("uuid");
var isBase64 = require("is-base64");

async function SECRET(secretOrKey) {
  return new Promise(function (resolve, reject) {
    var dateNowSE = new Date().toLocaleString("sv-SE", { timeZone: "UTC" });
    const date2 = dateNowSE.split(" ")[0].split("-");
    var encrept = CryptoJS.HmacSHA256(
      `${date2[1]}/${date2[2]}`,
      secretOrKey
    ).toString();
    return resolve(encrept);
  });
}

async function SECRET2(secretOrKey) {
  return new Promise(function (resolve, reject) {
    var dateNow = new Date();
    var dateNowSE = new Date().toLocaleString("sv-SE", { timeZone: "UTC" });
    const date2 = dateNowSE.split(" ")[0].split("-");
    var encrept = CryptoJS.HmacSHA256(
      `${date2[1]}/${date2[2]}`,
      secretOrKey
    ).toString();
    // secert2
    const an_hour_ago2 = date.addHours(dateNow, -24);
    var encrept2 = CryptoJS.HmacSHA256(
      date.format(an_hour_ago2, "MM/DD"),
      secretOrKey
    ).toString();
    // console.log(`${date2[2]}/${date2[1]}`);
    return resolve([encrept, encrept2]);
  });
}

var arr = [];

function check(token) {
  return new Promise(function (resolve, reject) {
    for (let index = 0; index < arr.length; index++) {
      const element = arr[index];
      if (element == token) {
        return resolve(true);
      }
    }
    return resolve(false);
  });
}

async function encrypt(secretRaw, decryptedMessage) {
  return new Promise(function (resolve, reject) {
    (async () => {
      try {
        var secret = await SECRET(secretRaw);
        var encrypt = CryptoJS.AES.encrypt(
          decryptedMessage,
          CryptoJS.enc.Utf8.parse(secret.substr(0, 32)),
          {
            iv: CryptoJS.enc.Utf8.parse(secret.substr(0, 16)), // parse the IV
            padding: CryptoJS.pad.Pkcs7,
            mode: CryptoJS.mode.CBC,
          }
        ).toString();
        return resolve(encrypt);
      } catch (err) {
        console.log("err");
        console.log(err);
        return reject(err);
      }
    })();
  });
}

async function Decipheriv(secret, encryptedMessage) {
  return new Promise(function (resolve, reject) {
    (async () => {
      for (let index = 0; index < secret.length; index++) {
        const element = secret[index];
        try {
          var decrypted = CryptoJS.AES.decrypt(
            encryptedMessage,
            CryptoJS.enc.Utf8.parse(element.substr(0, 32)),
            {
              iv: CryptoJS.enc.Utf8.parse(element.substr(0, 16)), // parse the IV
              padding: CryptoJS.pad.Pkcs7,
              mode: CryptoJS.mode.CBC,
            }
          );
          decrypted = decrypted.toString(CryptoJS.enc.Utf8);
          if (decrypted !== "") {
            return resolve(decrypted);
          } else {
            return reject(false);
          }
        } catch (err) {
          if (index == secret.length - 1) {
            console.log(`err decipher`);
            return reject(err);
          }
        }
      }
    })();
  });
}

async function decrypt(encryptedMessage, secretOption) {
  if (secretOption === "" || secretOption === undefined) {
    secretOption = R_TOKEN;
  }
  return new Promise(function (resolve, reject) {
    (async () => {
      try {
        var secret = await SECRET2(secretOption);
        var decipher = await Decipheriv(secret, encryptedMessage);
        return resolve(decipher);
      } catch (err) {
        console.log("err");
        console.log(err);
        return reject(err);
      }
    })();
  });
}

class Auth {
  constructor(R_TOKEN, R_USER, R_PASS, R_PATH) {
    this.R_TOKEN = R_TOKEN;
    this.R_USER = R_USER;
    this.R_PASS = R_PASS;
    this.R_PATH = R_PATH;
  }

  Logconstruc() {
    console.log("R_TOKEN", R_TOKEN);
    console.log("R_USER", R_USER);
    console.log("R_PASS", R_PASS);
    console.log("R_PATH", R_PATH);
  }

  Middleware(req, res, next) {
    return new Promise(function (resolve, reject) {
      (async () => {
        // console.log("Middleware");
        const body = await req.json();
        req = {
          headers: {
            authorization: req.headers.get("authorization"),
          },
          body: body,
        };
        try {
          var headersEncrypter = false;
          var bodyOriginal = req.body;
          if (req.headers.authorization) {
            let findSecurity = req.headers.authorization.indexOf("securityAgs");
            // console.log(findSecurity);
            if (findSecurity == -1) {
              const isb64 = isBase64(bodyOriginal.encrypData);
              if (isb64) {
                req.headers.authorization = await decrypt(
                  req.headers.authorization,
                  R_TOKEN
                );
                headersEncrypter = true;
              }
            } else {
              const replaceStr = req.headers.authorization.replace(
                /securityAgs/g,
                "/"
              );
              const isb64 = isBase64(bodyOriginal.encrypData);
              if (isb64) {
                req.headers.authorization = await decrypt(replaceStr, R_TOKEN);
                headersEncrypter = true;
              }
            }
            // console.log(req.headers.authorization);
            // console.log(await SECRET(R_TOKEN));
            const isValid = await jwt.verify(
              req.headers.authorization,
              await SECRET(R_TOKEN)
            );
            // console.log(isValid);
            if (!isValid) {
              return reject({
                status: 401,
                message: "Unauthorized",
              });
            }

            // console.log("payload");
            const { payload } = jwt.decode(req.headers.authorization);
            // console.log(payload);
            var isExpiredToken = false;

            var dateNow = new Date();
            if (payload.exp.toString().length > 10) {
              payload.exp = parseInt(payload.exp.toString().substr(0, 10));
            }
            // console.log(
            //   `${payload.exp} < ${parseInt(
            //     new Date().getTime().toString().slice(0, -3)
            //   )}`
            // );
            if (
              payload.exp + 900 <
              parseInt(new Date().getTime().toString().slice(0, -3))
            ) {
              isExpiredToken = true;
            }

            if (payload.sub === R_USER && isExpiredToken === false) {
              const chk = await check(req.headers.authorization);
              // console.log(chk);
              arr.push(req.headers.authorization);
              if (
                chk == true &&
                req.headers.testdev !== true &&
                req.headers.testdev !== "true"
              ) {
                return reject({
                  status: 401,
                  message: "Duplicate token",
                });
              }
              if (arr.length > 200) {
                arr = [];
              }
              // console.log(`headersEncrypter ${headersEncrypter}`);
              if (headersEncrypter === true) {
                if (bodyOriginal.encrypData) {
                  const isb64 = isBase64(bodyOriginal.encrypData);
                  if (isb64) {
                    const result = await decrypt(
                      bodyOriginal.encrypData,
                      R_TOKEN
                    );
                    try {
                      return resolve(JSON.parse(result));
                    } catch (error) {
                      return resolve(result);
                    }
                  } else {
                    req.body = { result: `isb64 false` };
                    return reject({
                      status: 401,
                      message: req.body,
                    });
                  }
                } else {
                  req.body = { result: "no encrypData" };
                  return reject({
                    status: 401,
                    message: req.body,
                  });
                }
              } else {
                return resolve(req.body);
              }
            } else {
              return reject({
                status: 401,
                message: "Unauthorized 2",
              });
            }
          } else {
            console.log("no headers");
            return reject({
              status: 401,
              message: "Unauthorized headers",
            });
          }
        } catch (error) {
          console.log("error");
          console.log(error);
          return reject({
            status: 401,
            message: error,
          });
        }
      })();
    });
  }

  RequestToken(req, res, next) {
    // console.log(req.body.username, req.body.password, req.path);
    // console.log(R_USER, R_PASS, R_PATH);
    if (
      req.body.username === R_USER &&
      req.body.password === R_PASS &&
      (req.path === R_PATH || req.path === `/${R_PATH}`)
    ) {
      (async () => {
        const time_unix =
          parseInt(new Date().getTime().toString().slice(0, -3)) + 300;
        const payload = {
          sub: R_USER,
          iat: time_unix,
        };
        res.send(
          jwt.sign(payload, await SECRET(R_TOKEN), {
            expiresIn: 5,
            jwtid: uuidv4(),
          })
        );
      })();
    } else {
      next();
    }
  }

  GenToken(secret, user) {
    if (secret === "" || secret === undefined) {
      secret = R_TOKEN;
    }
    if (user === "" || user === undefined) {
      user = R_USER;
    }
    // console.log("GenToken");
    // console.log(R_TOKEN);

    return new Promise(function (resolve, reject) {
      (async () => {
        try {
          const time_unix =
            parseInt(new Date().getTime().toString().slice(0, -3)) + 300;
          const payload = {
            sub: user,
            iat: time_unix,
            exp: time_unix,
          };
          return resolve(
            jwt.sign(payload, await SECRET(secret), {
              expiresIn: "1d",
              jwtid: "uuidv4()",
            })
          );
        } catch (error) {
          reject(error);
        }
      })();
    });
  }

  genTokenEncryp(secret, user) {
    if (secret === "" || secret === undefined) {
      secret = R_TOKEN;
    }
    if (user === "" || user === undefined) {
      user = R_USER;
    }

    return new Promise(function (resolve, reject) {
      (async () => {
        try {
          const time_unix =
            parseInt(new Date().getTime().toString().slice(0, -3)) + 300;
          const payload = {
            exp: time_unix,
            sub: user,
            iat: new Date().getTime(),
          };
          const SEV = await SECRET(secret);
          // console.log(SEV);
          const token = await jwt.sign(payload, SEV, {
            expiresIn: time_unix,
          });
          // console.log(token);
          const encryp = await encrypt(secret, token);

          return resolve(encryp.replace(/\//g, "securityAgs"));
        } catch (error) {
          reject(error);
        }
      })();
    });
  }

  encrypbody(body, secret) {
    if (secret === "" || secret === undefined) {
      secret = R_TOKEN;
    }

    return new Promise(function (resolve, reject) {
      (async () => {
        try {
          const encryp = await encrypt(secret, JSON.stringify(body));
          const result = { encrypData: encryp };
          return resolve(result);
        } catch (error) {
          console.log(error);
          return reject(error);
        }
      })();
    });
  }

  encrypbodyReplece(body, secret) {
    if (secret === "" || secret === undefined) {
      secret = R_TOKEN;
    }

    return new Promise(function (resolve, reject) {
      (async () => {
        try {
          const encryp = await encrypt(secret, JSON.stringify(body));
          const result = { encrypData: encryp.replace(/\//g, "securityAgs") };
          return resolve(result);
        } catch (error) {
          console.log(error);
          return reject(error);
        }
      })();
    });
  }

  decrypbody(body, secret) {
    if (secret === "" || secret === undefined) {
      secret = R_TOKEN;
    }

    return new Promise(function (resolve, reject) {
      (async () => {
        try {
          let findSecurity = body.indexOf("securityAgs");
          if (findSecurity != -1) {
            const replaceStr = body.replace(/securityAgs/g, "/");
            body = replaceStr;
          }
          const result = await decrypt(body, secret);
          return resolve(result);
        } catch (error) {
          console.log(error);
          return reject(error);
        }
      })();
    });
  }

  test(secret) {
    if (secret === "" || secret === undefined) {
      secret = R_TOKEN;
    }
    return new Promise(function (resolve, reject) {
      (async () => {
        try {
          const encryp2 = await encrypt(R_TOKEN, "test");
          console.log(encryp2);
          const decrypt2 = await decrypt(encryp2);
          console.log(decrypt2);
          return resolve(decrypt2);
        } catch (error) {
          console.log(error);
          reject(error);
        }
      })();
    });
  }
}
module.exports = Auth;
